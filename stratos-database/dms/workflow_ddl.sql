CREATE TABLE `dms_action`
(
    `id`                          bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `action_id`                   varchar(56) NOT NULL,
    `tenant_id`                   varchar(56) NOT NULL,
    `name`                        varchar(56) NOT NULL,
    `description`                 varchar(1024) NULL,
    `state`                       varchar(56) NOT NULL,
    `action_type`                 varchar(56) NOT NULL,
    `action_config`               JSON        NOT NULL,
    `retry_config`                JSON NULL,
    `action_mode`                 varchar(56) NOT NULL,
    `is_action_failure_ignorable` tinyint(1) default 0 NOT NULL,
    `created_at`                  datetime(3) NOT NULL DEFAULT current_timestamp (),
    `updated_at`                  datetime(3) NOT NULL DEFAULT current_timestamp () ON UPDATE current_timestamp (),
    PRIMARY KEY (`id`),
    UNIQUE KEY `UK_dms_action_name_mode_tenant` (`tenant_id`,`name`,`action_mode`),
    UNIQUE KEY `UK_dms_action_id` (`action_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `dms_stage`
(
    `id`          bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `stage_id`    varchar(56) NOT NULL,
    `tenant_id`   varchar(56) NOT NULL,
    `name`        varchar(56) NOT NULL,
    `description` varchar(1024) NULL,
    `state`       varchar(56) NOT NULL,
    `action_ids`  JSON        NOT NULL,
    `approved_by` varchar(56)  NULL,
    `updated_by`  varchar(56) NOT NULL,
    `created_by`  varchar(56) NOT NULL,
    `version`     int(3) NOT NULL DEFAULT 1,
    `created_at`  datetime(3) NOT NULL DEFAULT current_timestamp (),
    `updated_at`  datetime(3) NOT NULL DEFAULT current_timestamp () ON UPDATE current_timestamp (),
    PRIMARY KEY (`id`),
    UNIQUE KEY `UK_STAGE_ROW` (`tenant_id`,`name`,`version`),
    UNIQUE KEY `UK_STAGE_ID` (`stage_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `dms_stage_transition`
(
    `id`                           bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `workflow_class_id`            varchar(56)  NOT NULL,
    `source_stage_id`              varchar(56)  NOT NULL,
    `target_stage_id`              varchar(56)  NOT NULL,
    `action_id`                    varchar(56)  NOT NULL,
    `action_response_context_type` varchar(256) NOT NULL,
    `created_at`                   datetime(3) NOT NULL DEFAULT current_timestamp (),
    `updated_at`                   datetime(3) NOT NULL DEFAULT current_timestamp () ON UPDATE current_timestamp (),
    PRIMARY KEY (`id`),
    UNIQUE KEY `UK_STAGE_TRANS_ROW` (`workflow_class_id`,`source_stage_id`,`target_stage_id`),
    KEY                            `idx_source_stage` (`workflow_class_id`,`source_stage_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `dms_stage_transition_instance`
(
    `id`                           bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `workflow_class_instance_id`   varchar(56) NOT NULL,
    `source_stage_id`              varchar(56) NOT NULL,
    `target_stage_id`              varchar(56) NULL,
    `action_id`                    varchar(56) NOT NULL,
    `processed_by`                 varchar(56) NOT NULL,
    `partition_id`                 int(3) unsigned NOT NULL,
    `action_response_context_type` varchar(56) NOT NULL,
    `action_response_context`      JSON NULL,
    `created_at`                   datetime(3) NOT NULL DEFAULT current_timestamp (),
    `updated_at`                   datetime(3) NOT NULL DEFAULT current_timestamp () ON UPDATE current_timestamp (),
    PRIMARY KEY (`id`, `partition_id`),
    UNIQUE KEY `UK_WORKFLOW_INST_TRNS_ROW` (`partition_id`,`workflow_class_instance_id`,`source_stage_id`,`action_id`),
    KEY                            `idx_workflow_instance_trans` (`workflow_class_instance_id`,`source_stage_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci  PARTITION BY HASH (`partition_id`)
PARTITIONS 120;

CREATE TABLE `dms_workflow_class`
(
    `id`                bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `workflow_class_id` varchar(56) NOT NULL,
    `name`              varchar(56) NOT NULL,
    `description`       varchar(1024) NULL,
    `state`             varchar(56) NOT NULL,
    `tenant_id`         varchar(56) NOT NULL,
    `approved_by`       varchar(56)  NULL,
    `updated_by`        varchar(56) NOT NULL,
    `created_by`        varchar(56) NOT NULL,
    `version`           int(3) NOT NULL DEFAULT 1,
    `created_at`        datetime(3) NOT NULL DEFAULT current_timestamp (),
    `updated_at`        datetime(3) NOT NULL DEFAULT current_timestamp () ON UPDATE current_timestamp (),
    PRIMARY KEY (`id`),
    UNIQUE KEY `UK_WORKFLOW_CLASS_ROW` (`tenant_id`,`name`,`version`),
    UNIQUE KEY `UK_WORKFLOW_CLASS_ID` (`workflow_class_id`),
    KEY                 `idx_workflow_class` (`tenant_id`,`workflow_class_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `dms_workflow_class_instance`
(
    `id`                         bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `workflow_class_id`          varchar(56) NOT NULL,
    `workflow_class_instance_id` varchar(56) NOT NULL,
    `state`                      varchar(56) NOT NULL,
    `workflow_current_stage_id`  varchar(56) NOT NULL,
    `partition_id`               int(3) unsigned NOT NULL,
    `created_by`                 varchar(56) NOT NULL,
    `created_at`                 datetime(3) NOT NULL DEFAULT current_timestamp (),
    `updated_at`                 datetime(3) NOT NULL DEFAULT current_timestamp () ON UPDATE current_timestamp (),
    PRIMARY KEY (`id`, `partition_id`),
    UNIQUE KEY `UK_WORKFLOW_INST_ROW` (`partition_id`,`workflow_class_id`,`workflow_class_instance_id`),
    UNIQUE KEY `UK_WORKFLOW_INST_ID` (`partition_id`,`workflow_class_instance_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci  PARTITION BY HASH (`partition_id`)
PARTITIONS 120;

CREATE TABLE `dms_workflow_class_instance_meta`
(
    `id`                         bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `workflow_class_instance_id` varchar(56) NOT NULL,
    `meta_data_context_type`     varchar(56) NOT NULL,
    `context`                    JSON        NOT NULL,
    `partition_id`               int(3) unsigned NOT NULL,
    `created_by`                 varchar(56) NOT NULL,
    `created_at`                 datetime(3) NOT NULL DEFAULT current_timestamp (),
    `updated_at`                 datetime(3) NOT NULL DEFAULT current_timestamp () ON UPDATE current_timestamp (),
    PRIMARY KEY (`id`, `partition_id`),
    KEY                          `idx_workflow_class` (`partition_id`,`workflow_class_instance_id`,`meta_data_context_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci  PARTITION BY HASH (`partition_id`)
PARTITIONS 120;

/* drop table dms_action;
drop table dms_stage;
drop table dms_stage_transition;
drop table dms_stage_transition_instance;
drop table dms_workflow_class;
drop table dms_workflow_class_instance;
drop table dms_workflow_class_instance_meta; */




