CREATE TABLE `dms_dispute_class`
(
    `id`                bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `dispute_class_id`  varchar(56) NOT NULL,
    `name`              varchar(56) NOT NULL,
    `description`       varchar(1024) NULL,
    `workflow_class_id` varchar(56) NOT NULL,
    `dispute_level`     varchar(56) NOT NULL,
    `tenant_id`         varchar(56) NOT NULL,
    `state`             varchar(56) NOT NULL,
    `version`           int(3) NOT NULL DEFAULT 1,
    `approved_by`       varchar(56) NULL,
    `updated_by`        varchar(56) NOT NULL,
    `created_by`        varchar(56) NOT NULL,
    `created_at`        datetime(3) NOT NULL DEFAULT current_timestamp (),
    `updated_at`        datetime(3) NOT NULL DEFAULT current_timestamp () ON UPDATE current_timestamp (),
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_dms_dispute_class_id` (`dispute_class_id`),
    UNIQUE KEY `uk_dms_dispute_class_tenant_level_version_name` (`tenant_id`,`dispute_level`,`version`,`name`),
    KEY                 `idx_dms_dispute_class_class_id` (`tenant_id`,`dispute_class_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;



CREATE TABLE `dms_dispute`
(
    `id`                 bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `dispute_id`         varchar(56) NOT NULL,
    `name`               varchar(56) NOT NULL,
    `description`        varchar(56) NULL,
    `transaction_amount` int         NOT NULL,
    `transaction_id`     varchar(56) NOT NULL,
    `tenant_id`          varchar(56) NOT NULL,
    `state`              varchar(56) NOT NULL,
    `partition_id`       int(3) unsigned NOT NULL,
    `raised_at`          datetime(3) NULL DEFAULT current_timestamp (),
    `closed_at`          datetime(3) NULL,
    `created_at`         datetime(3) NOT NULL DEFAULT current_timestamp (),
    `updated_at`         datetime(3) NOT NULL DEFAULT current_timestamp () ON UPDATE current_timestamp (),
    PRIMARY KEY (`id`, `partition_id`),
    UNIQUE KEY `uk_dms_dispute_id` (`partition_id`,`dispute_id`),
    UNIQUE KEY `uk_dms_dispute_name_txn_id` (`partition_id`,`tenant_id`,`name`,`transaction_id`),
    KEY                  `idx_dms_dispute_tenant_txn_id` (`tenant_id`,`transaction_id`),
    KEY                  `idx_dms_dispute_id` (`dispute_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci  PARTITION BY HASH (`partition_id`)
PARTITIONS 120;


CREATE TABLE `dms_dispute_class_instance`
(
    `id`                          bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `dispute_class_id`            varchar(56) NOT NULL,
    `dispute_id`                  varchar(56) NOT NULL,
    `dispute_class_instance_id`   varchar(56) NOT NULL,
    `workflow_class_instance_id`  varchar(56) NOT NULL,
    `disputed_amount`             int         NOT NULL,
    `accepted_amount`             int         NOT NULL,
    `penalty_amount`              int         NOT NULL,
    `total_charges`               int         NOT NULL,
    `raised_by_user_id`           varchar(56) NOT NULL,
    `raised_by_user_type`         varchar(56) NOT NULL,
    `raised_against_by_user_id`   varchar(56) NOT NULL,
    `raised_against_by_user_type` varchar(56) NOT NULL,
    `state`                       varchar(56) NOT NULL,
    `partition_id`                int(3) unsigned NOT NULL,
    `raised_at`                   datetime(3) NULL DEFAULT current_timestamp (),
    `closed_at`                   datetime(3) NULL,
    `created_at`                  datetime(3) NOT NULL DEFAULT current_timestamp (),
    `updated_at`                  datetime(3) NOT NULL DEFAULT current_timestamp () ON UPDATE current_timestamp (),
    PRIMARY KEY (`id`, `partition_id`),
    UNIQUE KEY `uk_dms_dispute_class_instance_id` (`partition_id`,`dispute_class_instance_id`),
    UNIQUE KEY `uk_dms_dispute_class_id_dispute_id_instance_id` (`partition_id`,`dispute_class_id`,`dispute_id`,`dispute_class_instance_id`),
    KEY                           `idx_dms_dispute_class_instance_class_instance_id` (`dispute_class_id`,`dispute_class_instance_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci  PARTITION BY HASH (`partition_id`)
PARTITIONS 120;

CREATE TABLE `dms_dispute_class_instance_meta`
(
    `id`                        bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `dispute_class_id`          varchar(56) NOT NULL,
    `dispute_class_instance_id` varchar(56) NOT NULL,
    `meta_key`                  varchar(56) NOT NULL,
    `meta_value`                JSON        NOT NULL,
    `partition_id`              int(3) unsigned NOT NULL,
    `created_at`                datetime(3) NOT NULL DEFAULT current_timestamp (),
    `updated_at`                datetime(3) NOT NULL DEFAULT current_timestamp () ON UPDATE current_timestamp (),
    PRIMARY KEY (`id`, `partition_id`),
    KEY                         `idx_class_inst_id` (`dispute_class_id`,`dispute_class_instance_id`),
    KEY                         `idx_class_inst_key_id` (`dispute_class_id`,`dispute_class_instance_id`,`meta_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci  PARTITION BY HASH (`partition_id`)
PARTITIONS 120;


CREATE TABLE `dms_dispute_disbursement`
(
    `id`                           bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `dispute_class_id`             varchar(56) NOT NULL,
    `dispute_class_instance_id`    varchar(56) NOT NULL,
    `disbursement_id`              varchar(56) NOT NULL,
    `disbursement_transaction_id`  varchar(56) NOT NULL,
    `expected_disbursement_amount` int         NOT NULL,
    `actual_disbursement_amount`   int         NOT NULL,
    `state`                        varchar(56) NOT NULL,
    `disbursement_mode`            varchar(56) NOT NULL,
    `disbursement_to_user_type`    varchar(56) NOT NULL,
    `disbursement_to_user_id`      varchar(56) NOT NULL,
    `partition_id`                 int(3) unsigned NOT NULL,
    `created_at`                   datetime(3) NOT NULL DEFAULT current_timestamp (),
    `updated_at`                   datetime(3) NOT NULL DEFAULT current_timestamp () ON UPDATE current_timestamp (),
    PRIMARY KEY (`id`, `partition_id`),
    UNIQUE KEY `uk_dms_disbursement_transaction` (`partition_id`,`dispute_class_id`,`dispute_class_instance_id`,`disbursement_transaction_id`),
    UNIQUE KEY `uk_dms_disbursement_id` (`partition_id`,`disbursement_id`),
    KEY                            `idx_dms_disbursement_id` (`dispute_class_id`,`disbursement_id`),
    KEY                            `idx_dms_disbursement_instance_id` (`dispute_class_id`,`dispute_class_instance_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci  PARTITION BY HASH (`partition_id`)
PARTITIONS 120;

CREATE TABLE `dms_dispute_recovery`
(
    `id`                        bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `dispute_class_id`          varchar(56) NOT NULL,
    `dispute_class_instance_id` varchar(56) NOT NULL,
    `recovery_id`               varchar(56) NOT NULL,
    `recovery_transaction_id`   varchar(56) NOT NULL,
    `expected_recovery_amount`  int         NOT NULL,
    `actual_recovery_amount`    int         NOT NULL,
    `tenant_id`                 varchar(56) NOT NULL,
    `state`                     varchar(56) NOT NULL,
    `recovery_mode`             varchar(56) NOT NULL,
    `recovered_from_user_type`  varchar(56) NOT NULL,
    `recovered_from_user_id`    varchar(56) NOT NULL,
    `partition_id`              int(3) unsigned NOT NULL,
    `created_at`                datetime(3) NOT NULL DEFAULT current_timestamp (),
    `updated_at`                datetime(3) NOT NULL DEFAULT current_timestamp () ON UPDATE current_timestamp (),
    PRIMARY KEY (`id`,`partition_id`),
    UNIQUE KEY `uk_dms_recovery_transaction` (`partition_id`,`dispute_class_id`,`dispute_class_instance_id`,`recovery_transaction_id`),
    UNIQUE KEY `uk_dms_recovery_id` (`partition_id`,`recovery_id`),
    KEY                         `idx_dms_recovery_instance_id` (`dispute_class_id`,`recovery_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  PARTITION BY HASH (`partition_id`)
PARTITIONS 120;

CREATE TABLE `dms_tenant`
(
    `id`           bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `tenant_id`    varchar(56) NOT NULL,
    `name`         varchar(56) NOT NULL,
    `sub_category` varchar(56) NOT NULL,
    `description`  varchar(1024) NULL,
    `email_ids`    varchar(56) NOT NULL,
    `state`        varchar(56) NOT NULL,
    `created_at`   datetime(3) NOT NULL DEFAULT current_timestamp (),
    `updated_at`   datetime(3) NOT NULL DEFAULT current_timestamp () ON UPDATE current_timestamp (),
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_dms_tenant_name_subcategory` (`name`,`sub_category`),
    UNIQUE KEY `uk_dms_tenant_tenant_id` (`tenant_id`),
    KEY            `idx_dms_tenant_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


/* drop table dms_dispute_class;
drop table dms_dispute;
drop table dms_dispute_class_instance;
drop table dms_dispute_class_instance_meta;
drop table dms_dispute_disbursement;
drop table dms_dispute_recovery;
drop table dms_tenant; */




