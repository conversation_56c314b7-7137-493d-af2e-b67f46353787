package com.phonepe.central.workflow.action;

import com.phonepe.central.workflow.action.context.StageActionContext;
import lombok.experimental.UtilityClass;
import org.springframework.statemachine.StateMachine;

/**
 * <AUTHOR>
 */
@UtilityClass
public class StageMachineActionUtil {

    private StateMachine<String, String> stateMachine;

    public void init(StateMachine<String, String> stateMachine) {
        StageMachineActionUtil.stateMachine = stateMachine;
    }

    public boolean fireEvent(StageActionContext nextStageActionContext) {
        if (stateMachine == null) {
            throw new IllegalStateException("State machine is not initialized");
        }
        if (nextStageActionContext == null) {
            throw new IllegalArgumentException("nextStageActionContext cannot be null");
        }
        return stateMachine.sendEvent(nextStageActionContext.getStageEvent());
    }

}
