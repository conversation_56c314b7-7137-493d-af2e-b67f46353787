package com.phonepe.central.workflow.action.response;

import com.fasterxml.jackson.databind.JsonNode;
import com.phonepe.central.dispute.request.retry.RetryConfig;
import com.phonepe.central.workflow.action.ActionMode;
import com.phonepe.central.workflow.action.ActionState;
import com.phonepe.central.workflow.action.ActionType;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Action {

    @NotBlank
    private String actionId;

    @NotBlank
    private String name;


    @NotBlank
    private String description;

    @NotBlank
    private String tenantId;

    @NotNull
    private ActionState state;

    //This config will have the execution type and the actual config require to run the action
    @NotNull
    private ActionType actionType;

    //This config will have specific to action like namespace, path for docstore folder etc.
    @NotNull
    private JsonNode actionConfig;

    @NotNull
    private ActionMode actionMode;

    private RetryConfig retryConfig;

    private boolean isActionFailureIgnorable;

}
