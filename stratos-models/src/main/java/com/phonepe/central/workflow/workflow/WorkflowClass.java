package com.phonepe.central.workflow.workflow;

import com.phonepe.central.workflow.stage.StageTransition;
import com.phonepe.central.stratos.penalty.escalation.EscalationLevelConfig;
import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import java.util.Date;
import java.util.List;
import java.util.Set;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Singular;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowClass {

    @NotBlank
    private String workflowClassId;

    @NotBlank
    private String name;

    @NotBlank
    private String description;

    @NotNull
    private TenantInfo tenantInfo;

    @NotNull
    @Singular
    private Set<StageTransition> stageTransitions;


    @NotNull
    private WorkflowState state;

    @NotNull
    @Singular
    private List<EscalationLevelConfig> escalationLevelConfigs;

    @NotNull
    private Integer version;


    @NotNull
    private Date createdAt;

    @NotNull
    private Date updatedAt;

    @NotBlank
    private String updatedBy;

    @NotNull
    private String createdBy;

    public List<String> getEndStageIds() {
        return List.of();
    }
    public List<String> getStartStageIds() {
        return List.of();
    }

}
