package com.phonepe.central.workflow.action.response;

import com.phonepe.merchant.platform.stratos.models.disputemanagement.PaginationDetails;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ActionList {
    private List<Action> actions;
    private PaginationDetails paginationDetails;
}
