package com.phonepe.central.workflow.action.response.context;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum ActionResponseContextType {

    /**
     * This context is used when the action has been executed & got success.
     * In list of action, this output context will lead to calculate next stage id based on action output, #StageTransition.java
     * if not available for this context then next available action in the list will be executed.
     */
    SUCCESSFUL_CONTEXT(ActionResponseContextType.SUCCESSFUL_CONTEXT_TEXT),
    /**
     * This context is used when the action has been executed & failed.
     * In list of action, this output context will lead to calculate next stage id based on action output, #StageTransition.java
     * if not available for this context then next available action in the list will be executed.
     */
    FAILED_CONTEXT(ActionResponseContextType.FAILED_CONTEXT_TEXT),

    /**
     * This context is used when the action has been executed but any condition did not match like TTL etc.
     * In list of action, this output context will lead to waiting stage, so instead of pending in current action
     * next available action in the list will be executed. if this is the last action in the list then again
     * crosscheck else wait in the current state
     */
    WAITING_CONTEXT(ActionResponseContextType.WAITING_CONTEXT_TEXT),
    /**
     * This context is used when the action does not produce any output.
     * It is used to indicate that the action has completed without producing any output.
     * In list of action, this output context will lead to process next action in the list,
     * instead going for calculation of next stage id based on action output.
     */
    NO_OUTPUT_CONTEXT(ActionResponseContextType.NO_OUTPUT_CONTEXT_TEXT);

    public static final String SUCCESSFUL_CONTEXT_TEXT = "SUCCESSFUL_CONTEXT";
    public static final String FAILED_CONTEXT_TEXT = "FAILED_CONTEXT";
    public static final String WAITING_CONTEXT_TEXT = "WAITING_CONTEXT";
    public static final String NO_OUTPUT_CONTEXT_TEXT = "NO_OUTPUT_CONTEXT";

    @Getter
    private final String value;

}
