package com.phonepe.central.workflow.action.response.context.impl;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.phonepe.central.workflow.action.response.context.ActionResponseContext;
import com.phonepe.central.workflow.action.response.context.ActionResponseContextType;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class FailedActionResponseContext extends ActionResponseContext {


    @Builder
    @JsonCreator
    public FailedActionResponseContext(@JsonProperty("workflowInstanceId") String workflowInstanceId,
                                       @JsonProperty("stageId") String stageId,
                                       @JsonProperty("actionId") String actionId,
                                       @JsonProperty("context") JsonNode context) {
        super(ActionResponseContextType.FAILED_CONTEXT,workflowInstanceId, stageId, actionId, context);
    }

    @Override
    public <T> T accept(ActionResponseContextVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
