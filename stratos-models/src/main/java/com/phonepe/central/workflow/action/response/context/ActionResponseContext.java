package com.phonepe.central.workflow.action.response.context;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.JsonNode;
import com.phonepe.central.workflow.action.response.context.impl.FailedActionResponseContext;
import com.phonepe.central.workflow.action.response.context.impl.NoOutputActionResponseContext;
import com.phonepe.central.workflow.action.response.context.impl.SuccessfulActionResponseContext;
import com.phonepe.central.workflow.action.response.context.impl.WaitingActionResponseContext;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */

@Data
@AllArgsConstructor
@EqualsAndHashCode
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type")
@JsonSubTypes({
        @JsonSubTypes.Type(name = ActionResponseContextType.SUCCESSFUL_CONTEXT_TEXT, value = SuccessfulActionResponseContext.class),
        @JsonSubTypes.Type(name = ActionResponseContextType.FAILED_CONTEXT_TEXT, value = FailedActionResponseContext.class),
        @JsonSubTypes.Type(name = ActionResponseContextType.NO_OUTPUT_CONTEXT_TEXT, value = NoOutputActionResponseContext.class),
        @JsonSubTypes.Type(name = ActionResponseContextType.WAITING_CONTEXT_TEXT, value = WaitingActionResponseContext.class)})
public abstract class ActionResponseContext {

    private final ActionResponseContextType type;
    private String workflowInstanceId;
    private String stageId;
    private String actionId;
    private JsonNode context;

    public abstract <T> T accept(ActionResponseContextVisitor<T> visitor);


    public interface ActionResponseContextVisitor<T> {

        T visit(final SuccessfulActionResponseContext actionResponseContext);

        T visit(final FailedActionResponseContext actionResponseContext);

        T visit(final WaitingActionResponseContext actionResponseContext);

        T visit(final NoOutputActionResponseContext actionResponseContext);
    }
}

