package com.phonepe.central.workflow.workflow.metadata.context;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum MetaDataType {
    WORKFLOW_CLASS_INSTANCE_META(MetaDataType.WORKFLOW_CLASS_INSTANCE_META_TEXT),
    WORKFLOW_STATE_INSTANCE_META(MetaDataType.WORKFLOW_STATE_INSTANCE_META_TEXT);

    public static final String WORKFLOW_CLASS_INSTANCE_META_TEXT = "WORKFLOW_CLASS_INSTANCE_META";
    public static final String WORKFLOW_STATE_INSTANCE_META_TEXT = "WORKFLOW_STATE_INSTANCE_META";

    @Getter
    private final String value;

}
