package com.phonepe.central.workflow.workflow.metadata.context;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WorkflowInstanceMetaContext extends MetaDataContext {

    @NotNull
    private JsonNode context;

    @NotBlank
    private String workflowId;

    @NotBlank
    private String workflowInstanceId;

    @Builder
    @JsonCreator
    public WorkflowInstanceMetaContext(@JsonProperty("workflowId") String workFlowId,
                                       @JsonProperty("workflowInstanceId") String workflowInstanceId,
                                       @JsonProperty("context") JsonNode context) {
        super(MetaDataType.WORKFLOW_INSTANCE_META);
        this.context = context;
        this.workflowId = workFlowId;
        this.workflowInstanceId = workflowInstanceId;
    }

    @Override
    public <T> T accept(WorkflowInstanceMetaContextVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
