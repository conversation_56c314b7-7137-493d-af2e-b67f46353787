package com.phonepe.central.workflow.action.context;

import java.util.Map;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StageActionContext {

    @NotBlank
    private String stageId;

    @NotBlank
    private String callerStageId;

    @NotBlank
    private String stageEvent;

    @NotNull
    private Map<String, Object> metaData;

    @Valid
    public boolean isValid() {
        return !callerStageId.equals(stageId);
    }

}
