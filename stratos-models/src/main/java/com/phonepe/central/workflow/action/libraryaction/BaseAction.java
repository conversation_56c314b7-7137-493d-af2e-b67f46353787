package com.phonepe.central.workflow.action.libraryaction;

import com.phonepe.central.workflow.action.context.StageActionContext;
import com.phonepe.central.workflow.action.libraryaction.param.ActionParam;
import com.phonepe.central.workflow.action.response.context.ActionResponseContext;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Data
@AllArgsConstructor
public abstract class BaseAction <T extends ActionParam>{
    private final Class<T> paramClass;

    public void beforeAction(T param, StageActionContext stageActionContext){
        //NOOP
    }

    public final ActionResponseContext process(T param, StageActionContext stageActionContext) {
        this.beforeAction(param, stageActionContext);
        ActionResponseContext actionResponseContext = this.execute(param, stageActionContext);
        this.afterAction(param, stageActionContext);
        return actionResponseContext;
    }

    public void afterAction(T param, StageActionContext stageActionContext) {
       //NOOP
    }

    public abstract ActionResponseContext execute(T param, StageActionContext stageActionContext);
}
