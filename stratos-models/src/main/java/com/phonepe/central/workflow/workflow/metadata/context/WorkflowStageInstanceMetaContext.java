package com.phonepe.central.workflow.workflow.metadata.context;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WorkflowStageInstanceMetaContext extends MetaDataContext {

    @NotNull
    private Map<String, Object> context;

    @NotBlank
    private String workflowId;

    @NotBlank
    private String stageId;

    @NotBlank
    private String workflowInstanceId;

    @Builder
    @JsonCreator
    public WorkflowStageInstanceMetaContext(@JsonProperty("workflowId") String workFlowId,
                                            @JsonProperty("stageId") String stageId,
                                            @JsonProperty("workflowInstanceId") String workflowInstanceId,
                                            @JsonProperty("context") Map<String, Object> context) {
        super(MetaDataType.WORKFLOW_CLASS_INSTANCE_META);
        this.context = context;
        this.stageId = stageId;
        this.workflowId = workFlowId;
        this.workflowInstanceId = workflowInstanceId;
    }

    @Override
    public <T> T accept(WorkflowClassInstanceMetaContextVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
