package com.phonepe.central.workflow.stage;

import com.phonepe.central.workflow.action.response.context.ActionResponseContextType;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StageTransition {

    //While executing the action output,
    // I will ask stage service to provide me next stage id based on sourceStageId, actionId and actionResponseContextType.
    @NotBlank
    private String sourceStageId;

    @NotBlank
    private String targetStageId;

    @NotBlank
    private String actionId;

    @NotNull
    private ActionResponseContextType actionResponseContextType;


}
