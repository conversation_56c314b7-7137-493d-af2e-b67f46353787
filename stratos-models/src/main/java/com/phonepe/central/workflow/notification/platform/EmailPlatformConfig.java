package com.phonepe.central.workflow.notification.platform;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EmailPlatformConfig extends NotificationPlatformConfig {

    private String subject;
    private String templatePath;
    private List<String> toEmailIds;

    private List<String> toCCEmailIds;

    @Builder
    @JsonCreator
    public EmailPlatformConfig(@JsonProperty("subject") String subject,
                               @JsonProperty("templatePath") String templatePath,
                               @JsonProperty("toEmailIds") List<String> toEmailIds,
                               @JsonProperty("toEmailIds") List<String> toCCEmailIds) {
        super(NotificationPlatformType.EMAIL);
        this.subject = subject;
        this.templatePath = templatePath;
        this.toEmailIds = toEmailIds;
        this.toCCEmailIds = toCCEmailIds;
    }

}
