package com.phonepe.central.workflow.workflow.instance;

import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.phonepe.central.workflow.workflow.metadata.WorkflowClassInstanceMetaData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowClassInstance {

    @NotBlank
    private String workflowClassId;

    @NotBlank
    private String workflowClassInstanceId;

    @NotNull
    private String currentStageId;

    @NotNull
    private WorkflowClassInstanceState state;

    @NotNull
    private List<WorkflowClassInstanceMetaData> workflowInstanceMetaData;

    @NotNull
    private Date createdAt;

    @NotNull
    private Date updatedAt;

    @NotBlank
    private String createdBy;

}
