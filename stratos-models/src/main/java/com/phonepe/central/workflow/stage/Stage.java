package com.phonepe.central.workflow.stage;

import com.phonepe.central.workflow.action.response.Action;
import com.phonepe.central.stratos.penalty.escalation.EscalationLevelConfig;
import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import io.appform.dropwizard.actors.retry.config.RetryConfig;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Singular;
import org.apache.commons.lang3.builder.EqualsExclude;
import org.apache.commons.lang3.builder.HashCodeExclude;

/**
 * <AUTHOR>
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Stage {

    @NotBlank
    private String stageId;

    @NotBlank
    private String name;

    @NotBlank
    private String description;

    @NotNull
    private StageState stageState;

    @NotNull
    private TenantInfo tenantInfo;

    @NotNull
    @Singular
    private List<EscalationLevelConfig> escalationLevelConfigs;

    @NotEmpty
    @HashCodeExclude
    @EqualsExclude
    private List<Action> actions;
}
