package com.phonepe.central.workflow.action.libraryaction;
import com.google.inject.Singleton;
import com.phonepe.central.workflow.action.context.StageActionContext;
import com.phonepe.central.workflow.action.libraryaction.param.DocstoreParam;
import com.phonepe.central.workflow.action.response.context.ActionResponseContext;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class DocstoreUpload extends BaseAction<DocstoreParam> {

    public DocstoreUpload() {
        super(DocstoreParam.class);
    }

    @Override
    public ActionResponseContext execute(DocstoreParam param, StageActionContext stageActionContext) {
        // This is a placeholder for the actual upload logic
        log.info("Uploading to docstore with namespace: {}", param.getNamespace());
        return null; // Return appropriate response context
    }
}
