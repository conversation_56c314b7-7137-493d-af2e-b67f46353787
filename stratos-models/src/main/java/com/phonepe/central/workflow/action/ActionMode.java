package com.phonepe.central.workflow.action;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum ActionMode {

    /**
     * An action that has no side effects on the system's state. It can be
     * safely repeated any number of times.
     * (e.g., HTTP GET, HEAD).
     */
    SAFE_ACTION(ActionMode.SAFE_ACTION_TEXT),
    /**
     * An action that has side effects on the system's state, but can be
     * repeated without changing the outcome. It is idempotent.
     * (e.g., HTTP PUT, DELETE).
     */
    IDEM_POTENT_ACTION(ActionMode.IDEM_POTENT_ACTION_TEXT),
    /**
     * An action that has side effects on the system's state and cannot be
     * repeated without changing the outcome. It is not idempotent.
     * (e.g., HTTP POST).
     */
    NON_IDEM_POTENT_ACTION(ActionMode.NON_IDEM_POTENT_ACTION_TEXT);


    public static final String IDEM_POTENT_ACTION_TEXT = "IDEM_POTENT_ACTION";
    public static final String SAFE_ACTION_TEXT = "SAFE_ACTION";
    public static final String NON_IDEM_POTENT_ACTION_TEXT = "NON_IDEM_POTENT_ACTION";


    @Getter
    private final String value;
}
