package com.phonepe.central.workflow.notification;

import com.phonepe.central.workflow.notification.platform.NotificationPlatformConfig;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationConfig {

    @NotBlank
    private String name;

    @NotBlank
    private String description;

    @NotNull
    private NotificationPlatformConfig platformConfig;
}
