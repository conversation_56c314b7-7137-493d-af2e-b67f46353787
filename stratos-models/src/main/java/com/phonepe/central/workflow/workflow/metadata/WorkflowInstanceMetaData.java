package com.phonepe.central.workflow.workflow.metadata;

import com.phonepe.central.workflow.workflow.metadata.context.MetaDataContext;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowInstanceMetaData {

    @NotBlank
    private String workflowInstanceId;


    @NotNull
    private MetaDataContext metaDataContext;

    @NotNull
    private Date createdAt;

    @NotNull
    private Date updatedAt;

    @NotBlank
    private String updatedBy;


}
