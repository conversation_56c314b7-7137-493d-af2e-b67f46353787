package com.phonepe.central.dispute.request.disputeinstance.signal.debit;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonSubTypes.Type;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import com.fasterxml.jackson.annotation.JsonTypeInfo.Id;
import com.phonepe.central.dispute.request.disputeinstance.signal.debit.impl.EDCDebitSignalRequest;
import com.phonepe.central.dispute.request.disputeinstance.signal.debit.impl.PGDebitSignalRequest;
import com.phonepe.central.dispute.request.disputeinstance.signal.debit.impl.UPIDebitSignalRequest;
import com.phonepe.central.dispute.request.disputeinstance.signal.debit.impl.WalletDebitSignalRequest;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = Id.NAME, include = As.EXISTING_PROPERTY, property = "signalType", visible = true)
@JsonSubTypes(value = {@Type(value = UPIDebitSignalRequest.class, name = DebitSignalType.UPI_TEXT),
        @Type(value = PGDebitSignalRequest.class, name = DebitSignalType.PG_TEXT),
        @Type(value = EDCDebitSignalRequest.class, name = DebitSignalType.EDC_TEXT),
        @Type(value = WalletDebitSignalRequest.class, name = DebitSignalType.WALLET_TEXT)})
public abstract class DebitSignalRequest {

    @NotNull
    private DebitSignalType signalType;

    @NotBlank
    private String transactionId;

}
