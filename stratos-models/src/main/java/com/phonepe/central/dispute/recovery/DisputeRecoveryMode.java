package com.phonepe.central.dispute.recovery;

/**
 * <AUTHOR>
 */
public enum DisputeRecoveryMode {
    REFUND {
        @Override
        public <T> T accept(DisputeRecoveryModeVisitor<T> visitor) {
            return visitor.visitRefund();
        }
    },
    ToA {
        @Override
        public <T> T accept(DisputeRecoveryModeVisitor<T> visitor) {
            return visitor.visitToA();
        }
    };

    public abstract <T> T accept(DisputeRecoveryModeVisitor<T> visitor);

    public interface DisputeRecoveryModeVisitor<T> {

        T visitRefund();

        T visitToA();
    }
}
