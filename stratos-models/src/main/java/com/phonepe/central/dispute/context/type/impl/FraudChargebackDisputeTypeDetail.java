package com.phonepe.central.dispute.context.type.impl;

import com.phonepe.central.dispute.context.type.ChargebackType;
import com.phonepe.central.dispute.context.type.DisputeType;
import com.phonepe.central.dispute.context.type.DisputeTypeDetail;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@AllArgsConstructor
public class FraudChargebackDisputeTypeDetail extends DisputeTypeDetail {

    @NotNull
    private ChargebackType chargebackType;

    public FraudChargebackDisputeTypeDetail() {
        super(DisputeType.CHARGEBACK);
        this.chargebackType = ChargebackType.FRAUD;
    }

    @Override
    public <T> T accept(DisputeTypeDetailVisitor<T> visitor) {
        return visitor.visitFraudChargeback();
    }
}
