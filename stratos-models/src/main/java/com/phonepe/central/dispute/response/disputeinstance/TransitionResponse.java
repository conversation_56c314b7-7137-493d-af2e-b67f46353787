package com.phonepe.central.dispute.response.disputeinstance;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.phonepe.central.workflow.action.response.context.ActionResponseContext;
import com.phonepe.central.workflow.action.response.context.ActionResponseContextType;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransitionResponse {

    @NotBlank
    private String targetStageId;

    @NotBlank
    private String actionId;

    @NotNull
    private ActionResponseContextType actionResponseContextType;

    @NotNull
    private ActionResponseContext actionResponseContextValue;

    @NotNull
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "Asia/Kolkata")
    private Date created;

    @NotNull
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "Asia/Kolkata")
    private Date updated;
}
