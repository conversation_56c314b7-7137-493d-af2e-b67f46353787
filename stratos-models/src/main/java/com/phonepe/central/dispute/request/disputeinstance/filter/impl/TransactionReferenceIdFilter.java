package com.phonepe.central.dispute.request.disputeinstance.filter.impl;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.phonepe.central.dispute.request.disputeinstance.filter.DisputeFilterType;
import com.phonepe.central.dispute.request.disputeinstance.filter.DisputeInstanceFilter;
import java.util.Collections;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Singular;

@Data
@EqualsAndHashCode(callSuper = true)
public class TransactionReferenceIdFilter extends DisputeInstanceFilter {

    @NotEmpty
    private List<String> transactionReferenceIds;

    public TransactionReferenceIdFilter() {
        super(DisputeFilterType.TRANSACTION_REFERENCE_ID);
    }

    @Builder
    public TransactionReferenceIdFilter(@Singular final List<String> transactionReferenceIds) {
        this();
        this.transactionReferenceIds = transactionReferenceIds;
    }

    @JsonProperty("transactionReferenceId")
    public void setTransactionReferenceId(final String transactionReferenceId) {
        this.transactionReferenceIds = Collections.singletonList(transactionReferenceId);
    }

}
