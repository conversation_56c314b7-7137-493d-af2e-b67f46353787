package com.phonepe.central.dispute.evidence;

import com.phonepe.central.dispute.user.UserType;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class DisputeEvidence {

    @NotBlank
    private String disputeId;

    @NotBlank
    private String evidenceId;

    @NotBlank
    private String disputeClassInstanceId;

    @NotNull
    private EvidenceType evidenceType;

    @NotNull
    private DocumentType documentType;

    @NotBlank
    private String uploadedByUserId;

    @NotNull
    private UserType uploadedByUserType;

    @NotBlank
    private String verifiedByUserId;

    @NotNull
    private UserType verifiedByUserType;

    @NotNull
    private DisputeEvidenceStatus status;

    private String message;

    @NotNull
    private Date createdAt;

    @NotNull
    private Date verifiedAt;
}
