package com.phonepe.central.dispute.response.disputeinstance;

import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DisputeTransitionResponse {

    @NotBlank
    private String disputeClassInstanceId;

    @NotBlank
    private String sourceStageId;

    @Valid
    private List<TransitionResponse> transitions;

}
