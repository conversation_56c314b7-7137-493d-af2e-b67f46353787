package com.phonepe.central.dispute.disbursement;
/**
 * <AUTHOR>
 */
public enum DisputeDisbursementMode {
    REFUND {
        @Override
        public <T> T accept(DisputeDisbursementModeVisitor<T> visitor) {
            return visitor.visitRefund();
        }
    },
    ToA {
        @Override
        public <T> T accept(DisputeDisbursementModeVisitor<T> visitor) {
            return visitor.visitToA();
        }
    };

    public abstract <T> T accept(DisputeDisbursementModeVisitor<T> visitor);

    public interface DisputeDisbursementModeVisitor<T> {

        T visitRefund();

        T visitToA();
    }
}
