package com.phonepe.central.dispute.request.disputeinstance.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.phonepe.central.dispute.request.disputeinstance.DisputeRegisterRequest;
import com.phonepe.merchant.platform.stratos.models.commons.TransactionType;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@NoArgsConstructor(force = true)
@EqualsAndHashCode(callSuper = true)
public class UPIDisputeRegisterRequest extends DisputeRegisterRequest {

    @NotBlank
    private String disputeClassId;

    @NotNull
    @Min(1)
    private Long disputedAmount;

    @NotBlank
    private String transactionId;

    @Min(1)
    private long transactionAmount;

    @NotNull
    private TransactionType transactionType;

    @Builder
    public UPIDisputeRegisterRequest(@NotBlank String disputeClassId,
                                     @NotNull Long disputedAmount,
                                     @NotBlank String transactionId,
                                     @NotNull Long transactionAmount,
                                     @Valid JsonNode metaData) {
        super(TransactionType.UPI, disputeClassId, disputedAmount, transactionId, transactionAmount, metaData);

    }
}
