package com.phonepe.central.dispute.recovery;

import java.util.Date;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DisputeRecovery {

    @NotBlank
    private String disputeRecoveryId;

    @NotBlank
    private String disputeInstanceId;

    @NotBlank
    private String disputeRecoveryTransactionId;

    @NotNull
    private DisputeRecoveryState state;

    @Min(1)
    private long expectedRecoveryAmount;

    @Min(1)
    private long actualRecoveredAmount;

    @NotBlank
    private String recoverableResponseEntityId;

    @NotBlank
    private DisputeRecoveryMode recoveryMode;

    private Date createdAt;

    private Date updatedAt;

}
