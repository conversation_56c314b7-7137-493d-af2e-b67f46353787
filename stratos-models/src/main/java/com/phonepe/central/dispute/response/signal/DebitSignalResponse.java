package com.phonepe.central.dispute.response.signal;

import com.phonepe.central.dispute.request.disputeinstance.signal.debit.DebitSignalType;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DebitSignalResponse {

    @NotBlank
    private String transactionId;

    @NotNull
    private DebitSignalType signalType;

    @NotBlank
    private String disputeWorkflowInstanceId;

    @NotBlank
    private String message;

}
