package com.phonepe.central.dispute.response.disputeclass;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.phonepe.central.dispute.DisputeClassState;
import com.phonepe.central.dispute.DisputeLevel;
import com.phonepe.central.stratos.penalty.escalation.EscalationLevelConfig;
import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import java.time.LocalDateTime;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DisputeClassResponse {

    @NotBlank
    private String disputeClassId;

    @NotBlank
    private String name;

    @NotBlank
    private String description;

    @NotBlank
    private String workflowClassId;

    @NotNull
    private DisputeLevel disputeLevel;

    @NotNull
    @Valid
    private List<EscalationLevelConfig> escalationLevelConfigs;

    @NotNull
    @Valid
    private TenantInfo tenantInfo;

    @NotNull
    private DisputeClassState state;

    @NotNull
    private Integer version;

    @NotNull
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "Asia/Kolkata")
    private LocalDateTime createAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "Asia/Kolkata")
    private LocalDateTime updatedAt;

}
