package com.phonepe.central.dispute;

import com.phonepe.central.dispute.user.UserType;
import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import com.phonepe.central.stratos.penalty.request.TransactionContext;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Dispute {

    @NotBlank
    private String disputeId;

    @NotBlank
    private String disputeClassId;

    @NotBlank
    private String workflowId;

    @NotBlank
    private String description;

    @NotNull
    private TransactionContext transactionContext;

    @NotNull
    private TenantInfo tenantInfo;

    @NotNull
    private DisputeState disputeState;

    @NotBlank
    private String raisedBy;

    @NotNull
    private UserType raisedUserType;

    @NotNull
    private Date raisedAt;

    @NotNull
    private Date closedAt;

    @NotNull
    private Date createdAt;

    @NotNull
    private Date updatedAt;

}
