package com.phonepe.central.dispute.request.disputeinstance.signal.credit;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonSubTypes.Type;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import com.fasterxml.jackson.annotation.JsonTypeInfo.Id;
import com.phonepe.central.dispute.request.disputeinstance.signal.credit.impl.EDCCreditSignalRequest;
import com.phonepe.central.dispute.request.disputeinstance.signal.credit.impl.PGCreditSignalRequest;
import com.phonepe.central.dispute.request.disputeinstance.signal.credit.impl.UPICreditSignalRequest;
import com.phonepe.central.dispute.request.disputeinstance.signal.credit.impl.WalletCreditSignalRequest;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = Id.NAME, include = As.EXISTING_PROPERTY, property = "signalType", visible = true)
@JsonSubTypes(value = {@Type(value = UPICreditSignalRequest.class, name = CreditSignalType.UPI_TEXT),
        @Type(value = PGCreditSignalRequest.class, name = CreditSignalType.PG_TEXT),
        @Type(value = EDCCreditSignalRequest.class, name = CreditSignalType.EDC_TEXT),
        @Type(value = WalletCreditSignalRequest.class, name = CreditSignalType.WALLET_TEXT)})
public abstract class CreditSignalRequest {

    @NotNull
    private CreditSignalType signalType;

    @NotBlank
    private String transactionId;

}
