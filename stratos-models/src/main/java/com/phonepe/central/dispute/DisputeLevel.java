package com.phonepe.central.dispute;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum DisputeLevel {
    L3(null) {
        @Override
        public <T> T accept(final DisputeLevelVisitor<T> visitor) {
            return visitor.visitL3();
        }
    },
    L2(DisputeLevel.L3) {
        @Override
        public <T> T accept(final DisputeLevelVisitor<T> visitor) {
            return visitor.visitL2();
        }
    },
    L1(DisputeLevel.L2) {
        @Override
        public <T> T accept(final DisputeLevelVisitor<T> visitor) {
            return visitor.visitL1();
        }
    };
    private DisputeLevel next;


    public abstract <T> T accept(final DisputeLevelVisitor<T> visitor);

    public interface DisputeLevelVisitor<T> {
        T visitL1();

        T visitL2();

        T visitL3();
    }

}
