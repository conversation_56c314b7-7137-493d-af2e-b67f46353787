package com.phonepe.central.dispute.context;

import com.fasterxml.jackson.databind.JsonNode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DisputeClassInstanceContext {

    @NotBlank
    private String disputeClassInstanceId;

    @NotBlank
    private String disputeClassId;

    private DisputeClassInstanceMetaKey metaKey;

    @NotNull
    private JsonNode metaValue; // List of metaValue context details, each containing a type and data  e.g. "NPCI Signals", "DISBURSEMENT Signals", "Merchant Additional", etc.


}
