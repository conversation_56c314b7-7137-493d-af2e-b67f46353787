package com.phonepe.central.dispute.request.workflow;

import com.phonepe.central.workflow.workflow.WorkflowState;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowClassSearchRequest {

    private String workflowClassId;

    private String name;

    private String tenantId;

    private WorkflowState state;

    private Integer version;


}
