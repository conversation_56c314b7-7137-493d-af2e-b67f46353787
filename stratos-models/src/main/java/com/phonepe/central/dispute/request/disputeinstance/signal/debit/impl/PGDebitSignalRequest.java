package com.phonepe.central.dispute.request.disputeinstance.signal.debit.impl;

import com.phonepe.central.dispute.request.disputeinstance.signal.debit.DebitSignalRequest;
import com.phonepe.central.dispute.request.disputeinstance.signal.debit.DebitSignalType;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PGDebitSignalRequest extends DebitSignalRequest {


    public PGDebitSignalRequest(@NotBlank String transactionId) {
        super(DebitSignalType.PG,transactionId);
    }


}
