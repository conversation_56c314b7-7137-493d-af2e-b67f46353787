package com.phonepe.central.dispute.request.evidence;

import com.phonepe.central.dispute.evidence.DocumentType;
import com.phonepe.central.dispute.evidence.EvidenceType;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class DisputeEvidenceUploadRequest {

    @NotNull
    private EvidenceType evidenceType;

    @NotNull
    private DocumentType documentType;

}
