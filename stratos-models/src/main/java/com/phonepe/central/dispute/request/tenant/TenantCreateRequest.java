package com.phonepe.central.dispute.request.tenant;

import java.util.List;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class TenantCreateRequest {

    @NotBlank
    private String name;

    private String description;

    @NotBlank
    private String subCategory;

    @NotEmpty(message = "At least one email is required")
    @Size(max = 2, message = "More than 2 emails  are not allowed")
    private List<@NotBlank @Email @Pattern(regexp = "^[A-Za-z0-9._%+-]+@phonepe\\.com$", message = "Email must be a valid @phonepe.com address") String> emailIds;

}
