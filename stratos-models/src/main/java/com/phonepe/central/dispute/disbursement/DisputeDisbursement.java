package com.phonepe.central.dispute.disbursement;

import java.util.Date;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DisputeDisbursement {

    @NotBlank
    private String disbursementId;

    @NotBlank
    private String disputeClassInstanceId;

    @NotBlank
    private String disbursementTransactionId;

    @Min(1)
    private long expectedDisbursementAmount;

    @Min(1)
    private long actualDisbursedAmount;

    @NotNull
    private DisputeDisbursementState disputeDisbursementState;

    @NotBlank
    private DisputeDisbursementMode disbursementMode;

    @NotBlank
    private String beneficiaryId;

    private Date createdAt;

    private Date updatedAt;

}
