package com.phonepe.central.dispute.request.disputeinstance.filter.impl;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.phonepe.central.dispute.request.disputeinstance.filter.DisputeFilterType;
import com.phonepe.central.dispute.request.disputeinstance.filter.DisputeInstanceFilter;
import java.time.LocalDate;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DateRangeFilter extends DisputeInstanceFilter {


    @NotNull
    private LocalDate startDate;

    @NotNull
    private LocalDate endDate;

    public DateRangeFilter() {
        super(DisputeFilterType.DATE_RANGE);
    }


    @JsonCreator
    public DateRangeFilter(@JsonProperty("startDate") final LocalDate startDate,
                           @JsonProperty("endDate") final LocalDate endDate) {
        super(DisputeFilterType.DATE_RANGE);
        if (endDate.isBefore(startDate)) {
            throw new IllegalArgumentException("Invalid Date Range - End Date should be after Start Date");
        }
        if (endDate.isAfter(LocalDate.now())) {
            throw new IllegalArgumentException("Invalid Date Range - End Date should be not more than today");
        }
        this.startDate = startDate;
        this.endDate = endDate;
    }

    //TODO : Add validation , Date range cant be more than a month
}
