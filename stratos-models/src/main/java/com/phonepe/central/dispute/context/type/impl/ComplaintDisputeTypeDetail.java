package com.phonepe.central.dispute.context.type.impl;

import com.phonepe.central.dispute.context.type.DisputeType;
import com.phonepe.central.dispute.context.type.DisputeTypeDetail;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ComplaintDisputeTypeDetail extends DisputeTypeDetail {

    public ComplaintDisputeTypeDetail() {
        super(DisputeType.COMPLAINT);
    }

    @Override
    public <T> T accept(DisputeTypeDetailVisitor<T> visitor) {
        return visitor.visitComplaint();
    }
}
