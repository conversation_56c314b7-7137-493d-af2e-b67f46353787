package com.phonepe.central.dispute.request.workflow;

import com.phonepe.central.stratos.penalty.escalation.EscalationLevelConfig;
import com.phonepe.central.workflow.stage.StageTransition;
import java.util.List;
import java.util.Set;
import javax.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Singular;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowClassUpdateRequest {

    private String name;

    private String description;

    private String tenantId;

    @Singular
    @Valid
    private Set<StageTransition> stageTransitions;

    @Singular
    @Valid
    private List<EscalationLevelConfig> escalationLevelConfigs;


}
