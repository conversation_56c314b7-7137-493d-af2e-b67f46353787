package com.phonepe.central.dispute.context.type;

import static com.phonepe.central.dispute.context.type.DisputeType.Names.COMPLAINT;
import static com.phonepe.central.dispute.context.type.DisputeType.Names.FRAUD_CHARGEBACK;
import static com.phonepe.central.dispute.context.type.DisputeType.Names.SERVICE_CHARGEBACK;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonSubTypes.Type;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import com.fasterxml.jackson.annotation.JsonTypeInfo.Id;
import com.phonepe.central.dispute.context.type.impl.ComplaintDisputeTypeDetail;
import com.phonepe.central.dispute.context.type.impl.FraudChargebackDisputeTypeDetail;
import com.phonepe.central.dispute.context.type.impl.ServiceChargebackDisputeTypeDetail;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = Id.NAME, include = As.EXISTING_PROPERTY, property = "disputeType", visible = true)
@JsonSubTypes(value = {@Type(value = ServiceChargebackDisputeTypeDetail.class, name = SERVICE_CHARGEBACK),
        @Type(value = FraudChargebackDisputeTypeDetail.class, name = FRAUD_CHARGEBACK),
        @Type(value = ComplaintDisputeTypeDetail.class, name = COMPLAINT),})
public abstract class DisputeTypeDetail {

    @NotNull
    private DisputeType disputeType;


    public abstract <T> T accept(DisputeTypeDetailVisitor<T> visitor);

    public interface DisputeTypeDetailVisitor<T> {

        T visitServiceChargeback();

        T visitFraudChargeback();

        T visitComplaint();

    }
}
