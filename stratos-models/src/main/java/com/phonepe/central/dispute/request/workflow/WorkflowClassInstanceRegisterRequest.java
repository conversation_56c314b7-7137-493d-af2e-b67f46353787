package com.phonepe.central.dispute.request.workflow;

import com.phonepe.central.dispute.user.UserType;
import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowClassInstanceRegisterRequest {

    @NotBlank
    private String workflowClassId;

    @NotBlank
    private String disputeClassId;

    @NotBlank
    private String disputeClassInstanceId;

    @NotBlank
    private String tenantId;

    @NotBlank
    private String raisedById;

    @NotNull
    private UserType raisedUserType;

}
