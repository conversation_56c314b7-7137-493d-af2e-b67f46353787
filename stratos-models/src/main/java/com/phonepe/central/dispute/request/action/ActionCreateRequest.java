package com.phonepe.central.dispute.request.action;

import com.fasterxml.jackson.databind.JsonNode;
import com.phonepe.central.dispute.request.retry.RetryConfig;
import com.phonepe.central.workflow.action.ActionMode;
import com.phonepe.central.workflow.action.ActionType;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActionCreateRequest {
    @Size(min = 1, max = 56, message = "Name length must be between 1 and 56")
    private String name;

    @Size(min = 1, max = 1024, message = "Description length must be between 1 and 1024")
    private String description;
    @NotNull
    private ActionType actionType;
    @NotNull
    private JsonNode actionConfig;
    @NotNull
    private ActionMode actionMode;
    @NotBlank
    private String tenantId;
    @NotNull
    private RetryConfig retryConfig;
    private boolean isActionFailureIgnorable;
}
