package com.phonepe.central.dispute.request.disputeinstance.filter.impl;

import com.phonepe.central.dispute.request.disputeinstance.filter.DisputeFilterType;
import com.phonepe.central.dispute.request.disputeinstance.filter.DisputeInstanceFilter;
import com.phonepe.central.dispute.user.UserType;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class RaisedByFilter extends DisputeInstanceFilter {

    @NotBlank
    private String raisedByUserId;

    @NotNull
    private UserType raisedByUserType;

    public RaisedByFilter() {
        super(DisputeFilterType.RAISED_BY_USER_ID);
    }

    @Builder
    public RaisedByFilter(@NotBlank final String raisedByUserId,
                          @NotNull final UserType raisedByUserType) {
        this();
        this.raisedByUserId = raisedByUserId;
        this.raisedByUserType = raisedByUserType;
    }

}
