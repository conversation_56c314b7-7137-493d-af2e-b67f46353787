package com.phonepe.central.dispute.request.disputeinstance.filter;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonSubTypes.Type;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import com.fasterxml.jackson.annotation.JsonTypeInfo.Id;
import com.phonepe.central.dispute.request.disputeinstance.filter.impl.DateRangeFilter;
import com.phonepe.central.dispute.request.disputeinstance.filter.impl.RaisedAgainstByFilter;
import com.phonepe.central.dispute.request.disputeinstance.filter.impl.RaisedByFilter;
import com.phonepe.central.dispute.request.disputeinstance.filter.impl.TransactionReferenceIdFilter;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = Id.NAME, include = As.EXISTING_PROPERTY, property = "filterType", visible = true)
@JsonSubTypes(value = {@Type(value = DateRangeFilter.class, name = DisputeFilterType.DATE_RANGE_TEXT),
        @Type(value = TransactionReferenceIdFilter.class, name = DisputeFilterType.TRANSACTION_REFERENCE_ID_TEXT),
        @Type(value = RaisedByFilter.class, name = DisputeFilterType.RAISED_BY_USER_ID_TEXT),
        @Type(value = RaisedAgainstByFilter.class, name = DisputeFilterType.RAISED_AGAINST_BY_USER_ID_TEXT)})
public abstract class DisputeInstanceFilter {

    @NotNull
    private DisputeFilterType filterType;

}
