package com.phonepe.central.dispute.request.stage;

import com.phonepe.central.stratos.penalty.escalation.EscalationLevelConfig;
import io.appform.dropwizard.actors.retry.config.RetryConfig;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Singular;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class StageCreateRequest {

    @NotBlank
    private String name;

    @NotBlank
    private String description;

    @NotBlank
    private String tenantId;

    @NotNull
    @Singular
    @Valid
    private List<EscalationLevelConfig> escalationLevelConfigs;

    @NotEmpty
    private List<String> actionIds;

    private RetryConfig retryConfig;

}
