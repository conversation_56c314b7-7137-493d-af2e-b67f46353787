package com.phonepe.central.dispute.request.action;

import com.phonepe.central.dispute.request.tenant.TenantInfo;
import com.phonepe.central.workflow.action.ActionMode;
import com.phonepe.central.workflow.action.ActionState;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActionFilterParams {
    private String actionId;
    private ActionState actionState;
    private ActionMode actionMode;
    private Boolean isActionFailureIgnorable;
    private TenantInfo tenantInfo;
    //pagination params
    private int limit;
    private int offset;
}
