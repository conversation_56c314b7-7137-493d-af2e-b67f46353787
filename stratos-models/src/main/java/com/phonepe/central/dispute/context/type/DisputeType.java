package com.phonepe.central.dispute.context.type;

import lombok.experimental.UtilityClass;

/**
 * <AUTHOR>
 */
public enum DisputeType {
    CHARGEBACK,
    COMPLAINT;

    @UtilityClass
    public static final class Names {

        public static final String SERVICE_CHARGEBACK = "SERVICE_CHARGEBACK";
        public static final String FRAUD_CHARGEBACK = "FRAUD_CHARGEBACK";
        public static final String COMPLAINT = "COMPLAINT";
    }
}
