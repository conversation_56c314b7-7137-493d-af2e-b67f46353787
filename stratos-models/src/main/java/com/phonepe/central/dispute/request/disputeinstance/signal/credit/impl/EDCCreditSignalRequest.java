package com.phonepe.central.dispute.request.disputeinstance.signal.credit.impl;

import com.phonepe.central.dispute.request.disputeinstance.signal.credit.CreditSignalRequest;
import com.phonepe.central.dispute.request.disputeinstance.signal.credit.CreditSignalType;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EDCCreditSignalRequest extends CreditSignalRequest {


    public EDCCreditSignalRequest(@NotBlank String transactionId) {
        super(CreditSignalType.EDC, transactionId);
    }


}
