package com.phonepe.central.dispute.request.stage;

import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import com.phonepe.central.workflow.stage.StageState;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class StageSearchRequest {


    private String stageId;


    private String name;


    private TenantInfo tenantInfo;


    private StageState stageState;


}
