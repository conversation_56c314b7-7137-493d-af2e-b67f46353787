package com.phonepe.central.dispute.request.workflow;

import com.phonepe.central.stratos.penalty.escalation.EscalationLevelConfig;
import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import com.phonepe.central.workflow.stage.StageTransition;
import java.util.List;
import java.util.Set;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Singular;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowClassCreateRequest {

    @NotBlank
    private String name;

    @NotBlank
    private String description;

    @NotNull
    private TenantInfo tenantInfo;

    @NotNull
    @Singular
    private Set<StageTransition> stageTransitions;


    @NotNull
    @Singular
    private List<EscalationLevelConfig> escalationLevelConfigs;


}
