package com.phonepe.central.dispute.request.disputeclass;

import com.phonepe.central.dispute.DisputeClassState;
import com.phonepe.central.dispute.DisputeLevel;
import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import javax.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DisputeClassSearchRequest {

    private String disputeClassId;

    private String name;

    private String workflowClassId;

    private DisputeLevel disputeLevel;

    private DisputeClassState state;

    private TenantInfo tenantInfo;

    @Valid
    public boolean isValid() {
        return (disputeClassId != null && !disputeClassId.isBlank()) || (name != null && !name.isBlank()) || (
                workflowClassId != null && !workflowClassId.isBlank()) || disputeLevel != null || state != null
                || tenantInfo != null;
    }

}
