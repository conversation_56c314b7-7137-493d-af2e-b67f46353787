package com.phonepe.central.dispute.request.disputeclass;

import com.phonepe.central.stratos.penalty.escalation.EscalationLevelConfig;
import java.util.List;
import javax.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DisputeClassUpdateRequest {

    private String description;

    @Valid
    private List<EscalationLevelConfig> escalationLevelConfigs;

}
