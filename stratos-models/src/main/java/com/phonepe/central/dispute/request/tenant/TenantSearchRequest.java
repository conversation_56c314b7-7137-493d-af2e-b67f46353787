package com.phonepe.central.dispute.request.tenant;

import com.phonepe.central.dispute.tenant.TenantState;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantSearchRequest {

    private String tenantId;

    private String name;

    private String subCategory;

    private TenantState state;

}