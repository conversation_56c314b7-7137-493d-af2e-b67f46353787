package com.phonepe.central.dispute.request.disputeclass;

import com.phonepe.central.dispute.DisputeLevel;
import com.phonepe.central.stratos.penalty.escalation.EscalationLevelConfig;
import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DisputeClassCreateRequest {

    @NotBlank
    private String name;

    @NotBlank
    private String description;

    @NotBlank
    private String workflowClassId;

    @NotNull
    private DisputeLevel disputeLevel;

    @NotNull
    @Valid
    private List<EscalationLevelConfig> escalationLevelConfigs;

    @NotNull
    @Valid
    private TenantInfo tenantInfo;

}
