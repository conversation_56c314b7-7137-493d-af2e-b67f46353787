package com.phonepe.central.dispute.response.disputeinstance;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.phonepe.central.dispute.DisputeState;
import com.phonepe.central.dispute.user.UserType;
import java.time.LocalDateTime;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DisputeInstanceResponse {

    @NotBlank
    private String disputeId;

    @NotBlank
    private String disputeClassInstanceId;

    @NotBlank
    private String disputeClassId;

    @NotBlank
    private String workflowInstanceId;

    @NotBlank
    private String transactionId;

    @NotNull
    private Long transactionAmount;

    @NotNull
    private Long disputedAmount;

    @NotNull
    private Long acceptedAmount;

    @NotBlank
    private String raisedByUserId;

    @NotNull
    private UserType raisedByUserType;

    @NotBlank
    private String raisedAgainstByUserId;

    @NotNull
    private UserType raisedAgainstByUserType;

    @NotNull
    private DisputeState state;

    @NotNull
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "Asia/Kolkata")
    private LocalDateTime raisedAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "Asia/Kolkata")
    private LocalDateTime closedAt;

    @NotNull
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "Asia/Kolkata")
    private LocalDateTime createAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "Asia/Kolkata")
    private LocalDateTime updatedAt;

}
