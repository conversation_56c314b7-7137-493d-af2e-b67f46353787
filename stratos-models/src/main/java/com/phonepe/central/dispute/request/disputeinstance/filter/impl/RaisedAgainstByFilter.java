package com.phonepe.central.dispute.request.disputeinstance.filter.impl;

import com.phonepe.central.dispute.request.disputeinstance.filter.DisputeFilterType;
import com.phonepe.central.dispute.request.disputeinstance.filter.DisputeInstanceFilter;
import com.phonepe.central.dispute.user.UserType;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class RaisedAgainstByFilter extends DisputeInstanceFilter {

    @NotBlank
    private String raisedAgainstByUserId;

    @NotNull
    private UserType raisedAgainstByUserType;

    public RaisedAgainstByFilter() {
        super(DisputeFilterType.RAISED_AGAINST_BY_USER_ID);
    }

    @Builder
    public RaisedAgainstByFilter(@NotBlank final String raisedAgainstByUserId,
                                 @NotNull final UserType raisedAgainstByUserType) {
        this();
        this.raisedAgainstByUserId = raisedAgainstByUserId;
        this.raisedAgainstByUserType = raisedAgainstByUserType;
    }

}
