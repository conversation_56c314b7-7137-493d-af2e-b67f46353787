package com.phonepe.central.dispute;

import com.phonepe.central.stratos.penalty.escalation.EscalationLevelConfig;
import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DisputeClass {

    @NotBlank
    private String disputeClassId;

    @NotBlank
    private String workflowClassId;

    @NotBlank
    private String description;

    @NotNull
    private DisputeLevel disputeLevel;

    @NotNull
    private List<EscalationLevelConfig> escalationLevelConfigs;

    @NotNull
    private TenantInfo tenantInfo;

    @NotNull
    private DisputeClassState disputeClassState;

    @NotNull
    private String createdBy;

    @NotNull
    private String updatedBy;

    @NotNull
    private Date createdAt;

    @NotNull
    private Date updatedAt;

    @Min(1)
    private int version;
}
