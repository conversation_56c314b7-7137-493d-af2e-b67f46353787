package com.phonepe.central.dispute.response.evidence;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.phonepe.central.dispute.evidence.DisputeEvidenceStatus;
import com.phonepe.central.dispute.evidence.DocumentType;
import com.phonepe.central.dispute.evidence.EvidenceType;
import com.phonepe.central.dispute.user.UserType;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DisputeEvidenceResponse {

    @NotBlank
    private String evidenceId;

    @NotBlank
    private String disputeClassInstanceId;

    @NotBlank
    private String documentId;

    @NotBlank
    private String documentUrl;

    @NotBlank
    private String documentName;

    @NotNull
    private DocumentType documentType;

    @NotNull
    private EvidenceType evidenceType;

    @NotNull
    private DisputeEvidenceStatus status;

    private String message;

    @NotBlank
    private String uploadedByUserId;

    @NotNull
    private UserType uploadedByUserType;


    private String verifiedByUserId;

    private UserType verifiedByUserType;

    @NotNull
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "Asia/Kolkata")
    private Date createdAt;

    @NotNull
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "Asia/Kolkata")
    private Date verifiedAt;


}
