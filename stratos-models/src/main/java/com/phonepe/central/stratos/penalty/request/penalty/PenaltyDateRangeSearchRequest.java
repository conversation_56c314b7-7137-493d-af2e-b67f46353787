package com.phonepe.central.stratos.penalty.request.penalty;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.phonepe.central.stratos.penalty.request.DateRangeRequest;
import io.dropwizard.validation.ValidationMethod;
import java.util.concurrent.TimeUnit;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@JsonPropertyOrder({"type", "penaltyClassId", "details"})
public class PenaltyDateRangeSearchRequest extends PenaltySearchRequest {

    @NotNull
    private DateRangeRequest dateRangeRequest;

    @Builder
    @JsonCreator
    public PenaltyDateRangeSearchRequest(@JsonProperty("penaltyClassId") String penaltyClassId,
                                         @JsonProperty("dateRangeRequest") DateRangeRequest dateRangeRequest) {
        super(PenaltySearchType.BASIC_SEARCH, penaltyClassId);
        this.dateRangeRequest = dateRangeRequest;
    }

    @Override
    public <T> T accept(PenaltySearchRequestVisitor<T> visitor) {
        return visitor.visit(this);
    }

    @JsonIgnore
    @ValidationMethod(message = "Date range must be within 1 day")
    public boolean isValid() {
        return dateRangeRequest != null && dateRangeRequest.getDifferenceInTimeUnit(TimeUnit.DAYS) <= 1;
    }

}
