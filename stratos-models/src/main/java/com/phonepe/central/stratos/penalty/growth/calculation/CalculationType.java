package com.phonepe.central.stratos.penalty.growth.calculation;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum CalculationType {
    TRANSACTION_DATE_FIXED_AMOUNT(CalculationType.TRANSACTION_DATE_FIXED_AMOUNT_TEXT),
    PENALTY_CONVERSION_DATE_FIXED_AMOUNT(CalculationType.PENALTY_CONVERSION_DATE_FIXED_AMOUNT_TEXT),
    TRANSACTION_DATE_FIXED_PERCENTAGE(CalculationType.TRANSACTION_DATE_FIXED_PERCENTAGE_TEXT),
    PENALTY_CONVERSION_DATE_FIXED_PERCENTAGE(CalculationType.PENALTY_CONVERSION_DATE_FIXED_PERCENTAGE_TEXT);

    public static final String TRANSACTION_DATE_FIXED_AMOUNT_TEXT = "TRANSACTION_DATE_FIXED_AMOUNT";

    public static final String PENALTY_CONVERSION_DATE_FIXED_AMOUNT_TEXT = "PENALTY_CONVERSION_DATE_FIXED_AMOUNT";

    public static final String TRANSACTION_DATE_FIXED_PERCENTAGE_TEXT = "TRANSACTION_DATE_FIXED_PERCENTAGE";

    public static final String PENALTY_CONVERSION_DATE_FIXED_PERCENTAGE_TEXT = "PENALTY_CONVERSION_DATE_FIXED_PERCENTAGE";


    @Getter
    private final String value;

}