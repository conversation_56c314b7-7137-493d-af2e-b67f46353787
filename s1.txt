[INFO] Scanning for projects...
[INFO] ------------------------------------------------------------------------
[INFO] Detecting the operating system and CPU architecture
[INFO] ------------------------------------------------------------------------
[INFO] os.detected.name: osx
[INFO] os.detected.arch: aarch_64
[INFO] os.detected.bitness: 64
[INFO] os.detected.version: 14.7
[INFO] os.detected.version.major: 14
[INFO] os.detected.version.minor: 7
[INFO] os.detected.classifier: osx-aarch_64
[INFO] ------------------------------------------------------------------------
[INFO] Reactor Build Order:
[INFO] 
[INFO] stratos                                                            [pom]
[INFO] stratos-models                                                     [jar]
[INFO] stratos-server                                                     [jar]
[INFO] 
[INFO] ---------------< com.phonepe.merchant.platform:stratos >----------------
[INFO] Building stratos 2.0.67-SNAPSHOT                                   [1/3]
[INFO]   from pom.xml
[INFO] --------------------------------[ pom ]---------------------------------
[WARNING] Could not transfer metadata commons-io:commons-io/maven-metadata.xml from/to maven-default-http-blocker (http://0.0.0.0/): Blocked mirror for repositories: [phonepe-releases (http://artifactory.phonepe.com/content/repositories/releases, default, releases+snapshots), phonepe-snapshots (http://artifactory.phonepe.com/content/repositories/snapshots, default, releases+snapshots)]
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ stratos ---
[WARNING] commons-io:commons-io/maven-metadata.xml failed to transfer from http://0.0.0.0/ during a previous attempt. This failure was cached in the local repository and resolution will not be reattempted until the update interval of maven-default-http-blocker has elapsed or updates are forced. Original error: Could not transfer metadata commons-io:commons-io/maven-metadata.xml from/to maven-default-http-blocker (http://0.0.0.0/): Blocked mirror for repositories: [phonepe-releases (http://artifactory.phonepe.com/content/repositories/releases, default, releases+snapshots), phonepe-snapshots (http://artifactory.phonepe.com/content/repositories/snapshots, default, releases+snapshots)]
[INFO] com.phonepe.merchant.platform:stratos:pom:2.0.67-SNAPSHOT
[INFO] +- org.projectlombok:lombok:jar:1.18.22:compile
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.7.1:test
[INFO] |  +- org.apiguardian:apiguardian-api:jar:1.1.0:compile
[INFO] |  +- org.junit.platform:junit-platform-engine:jar:1.7.1:test
[INFO] |  |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  |  \- org.junit.platform:junit-platform-commons:jar:1.7.1:test
[INFO] |  \- org.junit.jupiter:junit-jupiter-api:jar:5.7.1:test
[INFO] +- org.junit.jupiter:junit-jupiter-params:jar:5.7.1:test
[INFO] \- com.phonepe.verified:kaizen-core:jar:0.0.5:compile
[INFO]    +- com.phonepe.verified:kaizen-models:jar:0.0.5:compile
[INFO]    |  +- io.dropwizard:dropwizard-util:jar:2.0.28:compile
[INFO]    |  +- javax.validation:validation-api:jar:2.0.1.Final:compile
[INFO]    |  +- org.hibernate.validator:hibernate-validator:jar:6.1.7.Final:compile
[INFO]    |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.12.1:compile
[INFO]    |  \- com.phonepe.shadow:shadow-v2-models:jar:1.0.104:compile
[INFO]    +- org.apache.commons:commons-jcs-core:jar:2.2.2:compile
[INFO]    |  \- commons-logging:commons-logging:jar:1.2:compile
[INFO]    +- com.google.guava:guava:jar:30.1.1-jre:compile
[INFO]    |  +- com.google.guava:failureaccess:jar:1.0.1:compile
[INFO]    |  +- com.google.guava:listenablefuture:jar:9999.0-empty-to-avoid-conflict-with-guava:compile
[INFO]    |  +- com.google.code.findbugs:jsr305:jar:3.0.2:compile
[INFO]    |  +- org.checkerframework:checker-qual:jar:3.8.0:compile
[INFO]    |  +- com.google.errorprone:error_prone_annotations:jar:2.5.1:compile
[INFO]    |  \- com.google.j2objc:j2objc-annotations:jar:1.3:compile
[INFO]    +- org.reflections:reflections:jar:0.9.11:compile
[INFO]    |  \- org.javassist:javassist:jar:3.28.0-GA:compile
[INFO]    +- com.fasterxml.jackson.core:jackson-databind:jar:2.12.1:compile
[INFO]    |  \- com.fasterxml.jackson.core:jackson-core:jar:2.12.1:compile
[INFO]    +- io.dropwizard:dropwizard-jackson:jar:2.0.23:compile
[INFO]    |  +- com.github.ben-manes.caffeine:caffeine:jar:2.9.1:compile
[INFO]    |  +- com.fasterxml.jackson.datatype:jackson-datatype-guava:jar:2.10.5:compile
[INFO]    |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.10.5:compile
[INFO]    |  +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.10.5:compile
[INFO]    |  +- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.10.5:compile
[INFO]    |  +- com.fasterxml.jackson.module:jackson-module-afterburner:jar:2.10.5:compile
[INFO]    |  +- com.fasterxml.jackson.datatype:jackson-datatype-joda:jar:2.10.5:compile
[INFO]    |  \- org.slf4j:slf4j-api:jar:1.7.31:compile
[INFO]    +- io.dropwizard:dropwizard-core:jar:2.0.23:compile
[INFO]    |  +- io.dropwizard:dropwizard-configuration:jar:2.0.23:compile
[INFO]    |  |  \- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.10.5:compile
[INFO]    |  |     \- org.yaml:snakeyaml:jar:1.26:compile
[INFO]    |  +- io.dropwizard:dropwizard-logging:jar:2.0.23:compile
[INFO]    |  |  +- io.dropwizard.metrics:metrics-logback:jar:4.1.23:compile
[INFO]    |  |  +- ch.qos.logback:logback-core:jar:1.2.3:compile
[INFO]    |  |  +- io.dropwizard.logback:logback-throttling-appender:jar:1.1.0:compile
[INFO]    |  |  \- org.slf4j:log4j-over-slf4j:jar:1.7.31:runtime
[INFO]    |  +- io.dropwizard:dropwizard-jersey:jar:2.0.23:compile
[INFO]    |  |  +- org.glassfish.jersey.ext:jersey-metainf-services:jar:2.33:runtime
[INFO]    |  |  +- org.glassfish.jersey.inject:jersey-hk2:jar:2.33:runtime
[INFO]    |  |  |  \- org.glassfish.hk2:hk2-locator:jar:2.6.1:runtime
[INFO]    |  |  +- io.dropwizard.metrics:metrics-jersey2:jar:4.1.23:compile
[INFO]    |  |  +- com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider:jar:2.10.5:compile
[INFO]    |  |  |  +- com.fasterxml.jackson.jaxrs:jackson-jaxrs-base:jar:2.10.5:compile
[INFO]    |  |  |  \- com.fasterxml.jackson.module:jackson-module-jaxb-annotations:jar:2.10.5:compile
[INFO]    |  |  +- jakarta.annotation:jakarta.annotation-api:jar:1.3.5:compile
[INFO]    |  |  +- org.glassfish.hk2:hk2-api:jar:2.6.1:compile
[INFO]    |  |  |  +- org.glassfish.hk2:hk2-utils:jar:2.6.1:compile
[INFO]    |  |  |  \- org.glassfish.hk2.external:aopalliance-repackaged:jar:2.6.1:compile
[INFO]    |  |  +- org.glassfish.jersey.containers:jersey-container-servlet:jar:2.33:runtime
[INFO]    |  |  +- org.glassfish.jersey.containers:jersey-container-servlet-core:jar:2.33:compile
[INFO]    |  |  \- org.eclipse.jetty:jetty-io:jar:9.4.42.v20210604:compile
[INFO]    |  +- io.dropwizard:dropwizard-servlets:jar:2.0.23:compile
[INFO]    |  |  \- io.dropwizard.metrics:metrics-annotation:jar:4.1.23:compile
[INFO]    |  +- io.dropwizard:dropwizard-jetty:jar:2.0.23:compile
[INFO]    |  |  +- org.eclipse.jetty:jetty-servlets:jar:9.4.42.v20210604:compile
[INFO]    |  |  |  \- org.eclipse.jetty:jetty-continuation:jar:9.4.42.v20210604:compile
[INFO]    |  |  \- org.eclipse.jetty:jetty-http:jar:9.4.42.v20210604:compile
[INFO]    |  +- io.dropwizard:dropwizard-lifecycle:jar:2.0.23:compile
[INFO]    |  +- io.dropwizard.metrics:metrics-core:jar:4.1.23:compile
[INFO]    |  +- io.dropwizard.metrics:metrics-jetty9:jar:4.1.23:compile
[INFO]    |  +- io.dropwizard.metrics:metrics-jvm:jar:4.1.23:compile
[INFO]    |  +- io.dropwizard.metrics:metrics-jmx:jar:4.1.23:compile
[INFO]    |  +- io.dropwizard.metrics:metrics-servlets:jar:4.1.23:compile
[INFO]    |  |  +- io.dropwizard.metrics:metrics-json:jar:4.1.23:compile
[INFO]    |  |  \- com.helger:profiler:jar:1.1.1:compile
[INFO]    |  +- io.dropwizard.metrics:metrics-healthchecks:jar:4.1.23:compile
[INFO]    |  +- io.dropwizard:dropwizard-request-logging:jar:2.0.23:compile
[INFO]    |  |  \- ch.qos.logback:logback-access:jar:1.2.3:compile
[INFO]    |  +- ch.qos.logback:logback-classic:jar:1.2.3:compile
[INFO]    |  +- jakarta.servlet:jakarta.servlet-api:jar:4.0.4:compile
[INFO]    |  +- jakarta.validation:jakarta.validation-api:jar:2.0.2:compile
[INFO]    |  +- jakarta.ws.rs:jakarta.ws.rs-api:jar:2.1.6:compile
[INFO]    |  +- net.sourceforge.argparse4j:argparse4j:jar:0.8.1:compile
[INFO]    |  +- org.eclipse.jetty:jetty-security:jar:9.4.42.v20210604:compile
[INFO]    |  +- org.eclipse.jetty:jetty-server:jar:9.4.42.v20210604:compile
[INFO]    |  +- org.eclipse.jetty:jetty-servlet:jar:9.4.42.v20210604:compile
[INFO]    |  |  \- org.eclipse.jetty:jetty-util-ajax:jar:9.4.42.v20210604:compile
[INFO]    |  +- org.eclipse.jetty:jetty-util:jar:9.4.42.v20210604:compile
[INFO]    |  +- org.eclipse.jetty.toolchain.setuid:jetty-setuid-java:jar:1.0.4:compile
[INFO]    |  +- org.glassfish.hk2.external:jakarta.inject:jar:2.6.1:compile
[INFO]    |  +- org.glassfish.jersey.core:jersey-common:jar:2.33:compile
[INFO]    |  |  \- org.glassfish.hk2:osgi-resource-locator:jar:1.0.3:compile
[INFO]    |  \- org.glassfish.jersey.ext:jersey-bean-validation:jar:2.33:compile
[INFO]    +- io.dropwizard:dropwizard-validation:jar:2.0.23:compile
[INFO]    |  +- com.fasterxml:classmate:jar:1.5.1:compile
[INFO]    |  \- org.glassfish:jakarta.el:jar:3.0.3:compile
[INFO]    +- io.dropwizard:dropwizard-metrics:jar:2.0.23:compile
[INFO]    +- io.dropwizard:dropwizard-forms:jar:2.0.23:compile
[INFO]    |  \- org.glassfish.jersey.media:jersey-media-multipart:jar:2.33:compile
[INFO]    |     \- org.jvnet.mimepull:mimepull:jar:1.9.13:compile
[INFO]    +- ru.vyarus:dropwizard-guicey:jar:5.4.0:compile
[INFO]    |  +- com.google.inject:guice:jar:5.0.1:compile
[INFO]    |  |  +- javax.inject:javax.inject:jar:1:compile
[INFO]    |  |  \- aopalliance:aopalliance:jar:1.0:compile
[INFO]    |  +- com.google.inject.extensions:guice-servlet:jar:5.0.1:compile
[INFO]    |  \- ru.vyarus:generics-resolver:jar:3.0.3:compile
[INFO]    +- com.phonepe.platform:requestinfo-bundle:jar:2.0.23-36:compile
[INFO]    |  +- com.phonepe.platform:requestinfo-core:jar:2.0.23-36:compile
[INFO]    |  +- com.phonepe.platform:atlas-client:jar:2.1.143:compile
[INFO]    |  |  +- com.phonepe.dataplatform:mvel-common-functions:jar:0.1.15:compile
[INFO]    |  |  |  \- com.github.davidmoten:geo:jar:0.7.1:compile
[INFO]    |  |  |     \- com.github.davidmoten:grumpy-core:jar:0.2.2:compile
[INFO]    |  |  |        \- org.apache.commons:commons-math3:jar:3.2:compile
[INFO]    |  |  +- org.apache.curator:curator-framework:jar:4.2.0:compile
[INFO]    |  |  |  \- org.apache.curator:curator-client:jar:4.2.0:compile
[INFO]    |  |  +- io.sgr:s2-geometry-library-java:jar:1.0.0:compile
[INFO]    |  |  +- io.dropwizard.metrics:metrics-caffeine:jar:4.2.8:compile
[INFO]    |  |  +- com.phonepe.platform:spyglass-core:jar:2.0.21:compile
[INFO]    |  |  |  \- io.appform.opentracing.annotations:opentracing-annotations:jar:1.0.2:compile
[INFO]    |  |  |     +- io.opentracing:opentracing-api:jar:0.33.0:compile
[INFO]    |  |  |     \- io.opentracing:opentracing-util:jar:0.33.0:compile
[INFO]    |  |  |        \- io.opentracing:opentracing-noop:jar:0.33.0:compile
[INFO]    |  |  \- com.phonepe.platform:spyglass-common:jar:2.0.21:compile
[INFO]    |  \- org.mockito:mockito-core:jar:4.3.1:test
[INFO]    |     +- net.bytebuddy:byte-buddy:jar:1.12.7:compile
[INFO]    |     +- net.bytebuddy:byte-buddy-agent:jar:1.12.7:test
[INFO]    |     \- org.objenesis:objenesis:jar:3.2:test
[INFO]    +- com.phonepe.platform:requestinfo-models:jar:2.0.23-23:compile
[INFO]    |  +- io.dropwizard:dropwizard-maxmind:jar:1.3.7-1:compile
[INFO]    |  \- com.google.code.findbugs:annotations:jar:3.0.1:compile
[INFO]    |     \- net.jcip:jcip-annotations:jar:1.0:compile
[INFO]    +- com.phonepe.platform:metric-ingestion-bundle:jar:1.80:compile
[INFO]    |  +- com.phonepe.platform:metric-ingestion-models:jar:1.80:compile
[INFO]    |  +- com.phonepe.platform:metric-ingestion-client:jar:1.80:compile
[INFO]    |  |  \- com.squareup.okhttp3:logging-interceptor:jar:4.9.0:compile
[INFO]    |  |     \- org.jetbrains.kotlin:kotlin-stdlib-jdk8:jar:1.4.10:compile
[INFO]    |  |        \- org.jetbrains.kotlin:kotlin-stdlib-jdk7:jar:1.4.10:compile
[INFO]    |  \- io.micrometer:micrometer-core:jar:1.11.5:compile
[INFO]    |     +- io.micrometer:micrometer-commons:jar:1.11.5:compile
[INFO]    |     +- io.micrometer:micrometer-observation:jar:1.11.5:compile
[INFO]    |     \- org.latencyutils:LatencyUtils:jar:2.0.3:runtime
[INFO]    +- io.raven.dropwizard:dropwizard-oor:jar:2.0.18-1:compile
[INFO]    +- io.dropwizard:dropwizard-hibernate:jar:2.0.28:compile
[INFO]    |  +- io.dropwizard:dropwizard-db:jar:2.0.28:compile
[INFO]    |  +- com.fasterxml.jackson.datatype:jackson-datatype-hibernate5:jar:2.10.5:compile
[INFO]    |  |  \- javax.transaction:javax.transaction-api:jar:1.3:compile
[INFO]    |  +- javax.persistence:javax.persistence-api:jar:2.2:compile
[INFO]    |  +- joda-time:joda-time:jar:2.10.13:compile
[INFO]    |  +- org.apache.tomcat:tomcat-***********************
[INFO]    |  |  \- org.apache.tomcat:tomcat-juli:jar:9.0.56:compile
[INFO]    |  +- org.glassfish.jersey.core:jersey-client:jar:2.33:compile
[INFO]    |  +- org.glassfish.jersey.core:jersey-server:jar:2.33:compile
[INFO]    |  +- org.jadira.usertype:usertype.core:jar:7.0.0.CR1:runtime
[INFO]    |  |  \- org.jadira.usertype:usertype.spi:jar:7.0.0.CR1:runtime
[INFO]    |  +- org.hibernate:hibernate-core:jar:5.4.30.Final:compile
[INFO]    |  |  +- antlr:antlr:jar:2.7.7:compile
[INFO]    |  |  +- org.jboss.spec.javax.transaction:jboss-transaction-api_1.2_spec:jar:1.1.1.Final:compile
[INFO]    |  |  +- org.jboss:jandex:jar:2.2.3.Final:compile
[INFO]    |  |  +- org.hibernate.common:hibernate-commons-annotations:jar:5.1.2.Final:compile
[INFO]    |  |  \- org.glassfish.jaxb:jaxb-runtime:jar:2.3.1:compile
[INFO]    |  |     +- org.glassfish.jaxb:txw2:jar:2.3.1:compile
[INFO]    |  |     +- com.sun.istack:istack-commons-runtime:jar:3.0.7:compile
[INFO]    |  |     +- org.jvnet.staxex:stax-ex:jar:1.8:compile
[INFO]    |  |     \- com.sun.xml.fastinfoset:FastInfoset:jar:1.2.15:compile
[INFO]    |  +- jakarta.activation:jakarta.activation-api:jar:1.2.2:compile
[INFO]    |  \- jakarta.xml.bind:jakarta.xml.bind-api:jar:2.3.3:compile
[INFO]    +- org.hibernate:hibernate-envers:jar:5.6.4.Final:compile
[INFO]    |  +- org.jboss.logging:jboss-logging:jar:3.4.3.Final:compile
[INFO]    |  \- org.dom4j:dom4j:jar:2.1.3:compile
[INFO]    +- org.apache.tika:tika-core:jar:2.0.0:compile
[INFO]    |  \- commons-io:commons-io:jar:2.10.0:compile
[INFO]    +- io.appform.dropwizard.sharding:db-sharding-bundle:jar:2.0.28-9:compile
[INFO]    |  +- cglib:cglib-nodep:jar:3.3.0:compile
[INFO]    |  \- com.github.rholder:guava-retrying:jar:2.0.0:compile
[INFO]    +- com.phonepe.data.provider:rosey-data-provider-bundle:jar:2.15:compile
[INFO]    |  +- com.phonepe.platform:rosey-standanlone-config:jar:2.0.84:compile
[INFO]    |  |  \- com.fasterxml.jackson.dataformat:jackson-dataformat-xml:jar:2.10.1:compile
[INFO]    |  |     +- org.codehaus.woodstox:stax2-api:jar:4.2:compile
[INFO]    |  |     \- com.fasterxml.woodstox:woodstox-core:jar:6.0.2:compile
[INFO]    |  \- com.phonepe.data.provider:data-provider-core:jar:2.15:compile
[INFO]    +- io.appform.hope:hope-lang:jar:2.0.3:compile
[INFO]    |  +- io.appform.hope:hope-core:jar:2.0.3:compile
[INFO]    |  +- com.jayway.jsonpath:json-path:jar:2.7.0:compile
[INFO]    |  \- net.minidev:json-smart:jar:2.4.11:compile
[INFO]    |     \- net.minidev:accessors-smart:jar:2.4.11:compile
[INFO]    |        \- org.ow2.asm:asm:jar:9.3:compile
[INFO]    +- com.github.jknack:handlebars:jar:4.3.0:compile
[INFO]    +- com.github.jknack:handlebars-jackson2:jar:4.3.0:compile
[INFO]    +- io.appform.dropwizard.actors:dropwizard-rabbitmq-actors:jar:2.0.28-2:compile
[INFO]    |  +- org.apache.commons:commons-lang3:jar:3.12.0:compile
[INFO]    |  +- org.apache.httpcomponents:httpclient:jar:4.5.13:compile
[INFO]    |  |  +- org.apache.httpcomponents:httpcore:jar:4.4.13:compile
[INFO]    |  |  \- commons-codec:commons-codec:jar:1.11:compile
[INFO]    |  \- org.junit.vintage:junit-vintage-engine:jar:5.8.1:compile
[INFO]    +- com.rabbitmq:amqp-client:jar:5.12.0:compile
[INFO]    +- com.netflix.hystrix:hystrix-core:jar:1.5.18:compile
[INFO]    |  +- com.netflix.archaius:archaius-core:jar:0.4.1:compile
[INFO]    |  |  \- commons-configuration:commons-configuration:jar:1.8:compile
[INFO]    |  |     \- commons-lang:commons-lang:jar:2.6:compile
[INFO]    |  +- io.reactivex:rxjava:jar:1.2.0:compile
[INFO]    |  \- org.hdrhistogram:HdrHistogram:jar:2.1.9:compile
[INFO]    +- com.hystrix:hystrix-configurator:jar:0.0.8:compile
[INFO]    +- io.appform.core:hystrix-function-wrapper:jar:1.0.0:compile
[INFO]    |  \- junit:junit:jar:4.12:compile
[INFO]    |     \- org.hamcrest:hamcrest-core:jar:1.3:compile
[INFO]    +- org.zapodot:hystrix-dropwizard-bundle:jar:1.0.2:compile
[INFO]    |  +- com.netflix.hystrix:hystrix-metrics-event-stream:jar:1.5.12:compile
[INFO]    |  |  \- com.netflix.hystrix:hystrix-serialization:jar:1.5.12:runtime
[INFO]    |  \- com.netflix.hystrix:hystrix-codahale-metrics-publisher:jar:1.5.12:compile
[INFO]    +- com.phonepe.platform.docstore:docstore-client:jar:2.9:compile
[INFO]    |  +- commons-validator:commons-validator:jar:1.4.0:compile
[INFO]    |  |  +- commons-beanutils:commons-beanutils:jar:1.8.3:compile
[INFO]    |  |  \- commons-digester:commons-digester:jar:1.8:compile
[INFO]    |  +- com.phonepe.platform:docstore-common:jar:5.2.15:compile
[INFO]    |  +- org.apache.commons:commons-collections4:jar:4.4:compile
[INFO]    |  \- javax.ws.rs:javax.ws.rs-api:jar:2.1:compile
[INFO]    +- com.phonepe.olympus-im:olympus-im-client:jar:1.2.29:compile
[INFO]    |  +- io.dropwizard:dropwizard-auth:jar:2.0.23:compile
[INFO]    |  +- com.phonepe.olympus-im:olympus-im-core:jar:1.2.29:compile
[INFO]    |  |  +- com.phonepe.olympus-im:olympus-im-models:jar:1.2.29:compile
[INFO]    |  |  |  +- com.phonepe.services:warden-workflow-models:jar:2.0.25:compile
[INFO]    |  |  |  |  +- io.swagger:swagger-jersey2-jaxrs:jar:1.6.5:compile
[INFO]    |  |  |  |  +- io.swagger:swagger-annotations:jar:1.6.5:compile
[INFO]    |  |  |  |  +- com.phonepe.services:guardian-models:jar:1.1.19:compile
[INFO]    |  |  |  |  |  \- com.phonepe.platform:dropwizard-requestinfo-bundle:jar:1.3.5-6:compile
[INFO]    |  |  |  |  +- io.raven.dropwizard:dropwizard-jmxmp:jar:2.0.21-1:compile
[INFO]    |  |  |  |  |  \- org.glassfish.main.external:jmxremote_optional-repackaged:jar:5.0:compile
[INFO]    |  |  |  |  +- com.phonepe.merchants.platform:varys:jar:1.0.6:compile
[INFO]    |  |  |  |  |  \- com.sun.mail:javax.mail:jar:1.5.5:compile
[INFO]    |  |  |  |  |     \- javax.activation:activation:jar:1.1:compile
[INFO]    |  |  |  |  \- com.raskasa.metrics:metrics-okhttp:jar:0.5.5-PP:compile
[INFO]    |  |  |  \- com.phonepe.services:warden-core-models:jar:2.0.25:compile
[INFO]    |  |  |     \- com.phonepe.services:warden-workflow:jar:2.0.25:compile
[INFO]    |  |  +- org.bitbucket.b_c:jose4j:jar:0.7.7:compile
[INFO]    |  |  \- com.auth0:java-jwt:jar:3.3.0:compile
[INFO]    |  +- com.phonepe.olympus-im:olympus-im-routing:jar:1.2.29:compile
[INFO]    |  +- com.squareup.okhttp3:okhttp:jar:4.11.0-PPE:compile
[INFO]    |  |  +- com.squareup.okio:okio-jvm:jar:3.0.0:compile
[INFO]    |  |  |  \- org.jetbrains.kotlin:kotlin-stdlib-common:jar:1.5.31:compile
[INFO]    |  |  \- org.jetbrains.kotlin:kotlin-stdlib:jar:1.6.20:compile
[INFO]    |  |     \- org.jetbrains:annotations:jar:13.0:compile
[INFO]    |  +- io.github.openfeign:feign-slf4j:jar:11.0:compile
[INFO]    |  +- com.phonepe.platform:audit-logger-client:jar:1.4:compile
[INFO]    |  |  \- com.phonepe.platform:audit-logger-models:jar:1.4:compile
[INFO]    |  +- commons-collections:commons-collections:jar:3.2.2:compile
[INFO]    |  +- com.fasterxml.uuid:java-uuid-generator:jar:3.1.4:compile
[INFO]    |  +- com.google.api-client:google-api-client-java6:jar:1.24.1:compile
[INFO]    |  |  +- com.google.api-client:google-api-client:jar:1.24.1:compile
[INFO]    |  |  |  +- com.google.oauth-client:google-oauth-client:jar:1.24.1:compile
[INFO]    |  |  |  |  \- com.google.http-client:google-http-client:jar:1.24.1:compile
[INFO]    |  |  |  \- com.google.http-client:google-http-client-jackson2:jar:1.24.1:compile
[INFO]    |  |  \- com.google.oauth-client:google-oauth-client-java6:jar:1.24.1:compile
[INFO]    |  +- com.google.apis:google-api-services-oauth2:jar:v2-rev141-1.25.0:compile
[INFO]    |  +- io.appform.commonutils:common-utils:jar:1.4:compile
[INFO]    |  |  +- org.functionaljava:functionaljava:jar:3.0:compile
[INFO]    |  |  \- org.mongodb:bson:jar:2.3:compile
[INFO]    |  \- com.phonepe.platform:eventbus-recipes:jar:2.4:compile
[INFO]    |     \- com.slack.api:slack-api-client:jar:1.27.2:compile
[INFO]    |        \- com.slack.api:slack-api-model:jar:1.27.2:compile
[INFO]    +- com.phonepe.gandalf:gandalf-client:jar:2.0.40:compile
[INFO]    |  +- com.phonepe.gandalf:gandalf-core:jar:2.0.40:compile
[INFO]    |  |  \- com.phonepe.gandalf:gandalf-models:jar:2.0.40:compile
[INFO]    |  +- org.glassfish.jersey.bundles.repackaged:jersey-guava:jar:2.6:compile
[INFO]    |  \- org.glassfish.jersey.containers:jersey-container-grizzly2-http:jar:2.32:compile
[INFO]    |     \- org.glassfish.grizzly:grizzly-http-server:jar:2.4.4:compile
[INFO]    |        \- org.glassfish.grizzly:grizzly-http:jar:2.4.4:compile
[INFO]    |           \- org.glassfish.grizzly:grizzly-framework:jar:2.4.4:compile
[INFO]    +- com.phonepe.verified:oncall-dropwizard-bundle:jar:1.0.3:compile
[INFO]    +- com.phonepe.platform:vision-models:jar:2.1.106:compile
[INFO]    +- com.phonepe.shadow:shadow-v2-services:jar:1.0.104:compile
[INFO]    +- com.phonepe.shadow:shadow-v2-core:jar:1.0.104:compile
[INFO]    +- com.phonepe.verified:drishti-models:jar:1.0.8:compile
[INFO]    +- com.phonepe.platform:heimdall-models:jar:1.0.73:compile
[INFO]    |  \- org.freemarker:freemarker:jar:2.3.30:compile
[INFO]    +- com.phonepe.platform:sentinel-client:jar:1.161:compile
[INFO]    |  +- com.phonepe.platform:sentinel-models:jar:1.119:compile
[INFO]    |  |  +- com.phonepe.frontend:consent-models:jar:1.0.24:compile
[INFO]    |  |  \- com.phonepe.data.selector:vmn-data-selector:jar:1.16:compile
[INFO]    |  |     +- com.phonepe.data.selector:data-selector-core:jar:1.16:compile
[INFO]    |  |     \- com.flipkart.foxtrot:foxtrot-common:jar:6.3.1-138:compile
[INFO]    |  |        +- com.smoketurner:dropwizard-swagger:jar:1.3.7-1:compile
[INFO]    |  |        |  \- javax.activation:javax.activation-api:jar:1.2.0:runtime
[INFO]    |  |        +- io.raven.dropwizard:dropwizard-riemann:jar:1.3.13-1:compile
[INFO]    |  |        |  \- io.riemann:metrics3-riemann-reporter:jar:0.4.5:compile
[INFO]    |  |        |     +- com.codahale.metrics:metrics-core:jar:3.0.1:compile
[INFO]    |  |        |     \- io.riemann:riemann-java-client:jar:0.4.5:compile
[INFO]    |  |        \- com.google.protobuf:protobuf-java:jar:2.6.1:compile
[INFO]    |  +- org.junit.jupiter:junit-jupiter-migrationsupport:jar:5.6.0:compile
[INFO]    |  \- com.squareup.okio:okio:jar:2.2.2:compile
[INFO]    +- com.phonepe.platform.filters:api-killer-core:jar:1.26:compile
[INFO]    +- com.phonepe.platform.filters:api-killer-killswitch:jar:1.26:compile
[INFO]    +- com.phonepe.platform.killswitch:killswitch-client:jar:2.0.7:compile
[INFO]    |  \- com.phonepe.platform.killswitch:killswitch-common:jar:2.0.7:compile
[INFO]    +- com.phonepe.platform:columbus-core-models:jar:1.0.4:compile
[INFO]    |  \- com.phonepe.platform:release-scripts:zip:bin:0.1:compile
[INFO]    +- com.phonepe.platform:atlas-model:jar:2.1.148:compile
[INFO]    +- com.phonepe.platform:clockwork-models:jar:2.3.14:compile
[INFO]    +- com.phonepe.platform:rosey-dropwizard-config:jar:2.0.86:compile
[INFO]    |  +- com.phonepe.platform:rosey-client-models:jar:2.0.86:compile
[INFO]    |  \- org.apache.commons:commons-text:jar:1.9:compile
[INFO]    +- com.phonepe.dataplatform:event-ingestion-client:jar:2.1.0-12-SNAPSHOT:compile
[INFO]    |  +- org.clojars.nitishgoyal13:bigqueue:jar:0.7.8:compile
[INFO]    |  +- com.phonepe.platform:event-ingestion-models:jar:2.0.102:compile
[INFO]    |  +- com.phonepe.platform:checkmate-lib:jar:0.1.4:compile
[INFO]    |  \- com.phonepe.platform:requestinfo-utils:jar:2.0.23-37:compile
[INFO]    +- com.phonepe.platform.http.server.metrics:api-metrics:jar:0.0.11:compile
[INFO]    |  \- com.github.sgoertzen:sonar-break-maven-plugin:jar:*******:compile
[INFO]    |     +- org.apache.maven:maven-plugin-api:jar:3.5.2:compile
[INFO]    |     |  +- org.apache.maven:maven-model:jar:3.5.2:compile
[INFO]    |     |  +- org.eclipse.sisu:org.eclipse.sisu.plexus:jar:0.3.3:compile
[INFO]    |     |  |  \- javax.enterprise:cdi-api:jar:1.0:compile
[INFO]    |     |  |     \- javax.annotation:jsr250-api:jar:1.0:compile
[INFO]    |     |  \- org.codehaus.plexus:plexus-classworlds:jar:2.5.2:compile
[INFO]    |     +- org.apache.maven:maven-project:jar:2.2.1:compile
[INFO]    |     |  +- org.apache.maven:maven-settings:jar:2.2.1:compile
[INFO]    |     |  +- org.apache.maven:maven-profile:jar:2.2.1:compile
[INFO]    |     |  +- org.apache.maven:maven-artifact-manager:jar:2.2.1:compile
[INFO]    |     |  |  +- org.apache.maven.wagon:wagon-provider-api:jar:1.0-beta-6:compile
[INFO]    |     |  |  \- backport-util-concurrent:backport-util-concurrent:jar:3.1:compile
[INFO]    |     |  +- org.apache.maven:maven-plugin-registry:jar:2.2.1:compile
[INFO]    |     |  +- org.codehaus.plexus:plexus-interpolation:jar:1.11:compile
[INFO]    |     |  \- org.codehaus.plexus:plexus-container-default:jar:1.0-alpha-9-stable-1:compile
[INFO]    |     |     \- classworlds:classworlds:jar:1.1-alpha-2:compile
[INFO]    |     \- org.apache.maven.plugin-tools:maven-plugin-annotations:jar:3.5:compile
[INFO]    +- com.phonepe.platform:executors:jar:0.0.5:compile
[INFO]    +- com.platform:validation-bundle:jar:********:compile
[INFO]    +- org.aspectj:aspectjrt:jar:1.9.8:compile
[INFO]    +- io.appform.functionmetrics:function-metrics:jar:1.0.12:compile
[INFO]    |  \- org.aspectj:aspectjweaver:jar:1.9.7:compile
[INFO]    +- org.apache.zookeeper:zookeeper:jar:3.4.6:compile
[INFO]    |  +- jline:jline:jar:0.9.94:compile
[INFO]    |  \- io.netty:netty:jar:3.7.0.Final:compile
[INFO]    +- org.mariadb.jdbc:mariadb-java-client:jar:2.7.2:compile
[INFO]    +- org.springframework.statemachine:spring-statemachine-core:jar:2.4.0:compile
[INFO]    |  +- org.springframework:spring-tx:jar:5.3.3:compile
[INFO]    |  \- org.springframework:spring-messaging:jar:5.3.3:compile
[INFO]    +- org.springframework:spring-context:jar:5.3.3:compile
[INFO]    |  +- org.springframework:spring-aop:jar:5.3.3:compile
[INFO]    |  +- org.springframework:spring-beans:jar:5.3.3:compile
[INFO]    |  +- org.springframework:spring-core:jar:5.3.3:compile
[INFO]    |  |  \- org.springframework:spring-jcl:jar:5.3.3:compile
[INFO]    |  \- org.springframework:spring-expression:jar:5.3.3:compile
[INFO]    +- org.springframework.data:spring-data-commons:jar:2.7.5:compile
[INFO]    +- com.fasterxml.jackson.dataformat:jackson-dataformat-csv:jar:2.13.1:compile
[INFO]    +- io.github.openfeign:feign-core:jar:11.2:compile
[INFO]    +- io.github.openfeign:feign-jackson:jar:11.2:compile
[INFO]    +- io.github.openfeign:feign-httpclient:jar:11.2:compile
[INFO]    +- io.github.openfeign:feign-okhttp:jar:11.2:compile
[INFO]    +- guru.nidi:graphviz-java:jar:0.18.1:compile
[INFO]    |  +- org.webjars.npm:viz.js-graphviz-java:jar:2.1.3:compile
[INFO]    |  +- guru.nidi.com.kitfox:svgSalamander:jar:1.1.3:compile
[INFO]    |  +- net.arnx:nashorn-promise:jar:0.1.1:compile
[INFO]    |  +- org.apache.commons:commons-exec:jar:1.3:compile
[INFO]    |  +- org.slf4j:jcl-over-slf4j:jar:1.7.30:compile
[INFO]    |  +- org.slf4j:jul-to-slf4j:jar:1.7.30:compile
[INFO]    |  \- guru.nidi.com.eclipsesource.j2v8:j2v8_macosx_x86_64:jar:4.6.0:compile
[INFO]    +- com.phonepe.platform:aerospike-bundle:jar:3.0.5:compile
[INFO]    |  \- com.aerospike:aerospike-client:jar:4.4.18:compile
[INFO]    |     +- org.gnu:gnu-crypto:jar:2.0.1:compile
[INFO]    |     +- org.luaj:luaj-jse:jar:3.0:compile
[INFO]    |     \- org.mindrot:jbcrypt:jar:0.4:compile
[INFO]    +- com.phonepe.growth:mustang:jar:2.3.0:compile
[INFO]    |  \- org.apache.maven:maven-artifact:jar:3.8.5:compile
[INFO]    |     \- org.codehaus.plexus:plexus-utils:jar:3.3.0:compile
[INFO]    +- com.phonepe.platform.bullhorn:bullhorn-models:jar:1.0.132:compile
[INFO]    +- com.phonepe.growth:zencast-model:jar:1.1.79:compile
[INFO]    +- com.phonepe.growth:campaign-store-models:jar:2.0.29:compile
[INFO]    |  +- com.flipkart.flipcast:flipcast-models:jar:3.1.26:compile
[INFO]    |  +- com.phonepe.models:phonepe-model:jar:2.1.404:compile
[INFO]    |  +- com.phonepe.platform:validation-bundle:jar:********:compile
[INFO]    |  +- com.flipkart.zjsonpatch:zjsonpatch:jar:0.4.3:compile
[INFO]    |  +- javax.xml.bind:jaxb-api:jar:2.3.0:compile
[INFO]    |  \- com.github.phaneesh:sonar-break-maven-plugin:jar:1.2.7:compile
[INFO]    +- io.appform.rules:json-rules:jar:1.0.12:compile
[INFO]    +- com.openhtmltopdf:openhtmltopdf-pdfbox:jar:1.0.6:compile
[INFO]    |  +- org.apache.pdfbox:pdfbox:jar:2.0.22:compile
[INFO]    |  |  \- org.apache.pdfbox:fontbox:jar:2.0.22:compile
[INFO]    |  +- org.apache.pdfbox:xmpbox:jar:2.0.22:compile
[INFO]    |  +- com.openhtmltopdf:openhtmltopdf-core:jar:1.0.6:compile
[INFO]    |  \- de.rototor.pdfbox:graphics2d:jar:0.30:compile
[INFO]    +- com.phonepe.growth:hawkeye-model:jar:1.2.244:compile
[INFO]    +- com.phonepe.growth:hawkeye-common:jar:1.2.244:compile
[INFO]    +- com.phonepe.growth:hawkeye-client:jar:1.2.244:compile
[INFO]    +- in.vectorpro.dropwizard:dropwizard-swagger:jar:2.0.28-1:compile
[INFO]    |  +- io.dropwizard:dropwizard-views:jar:2.0.28:compile
[INFO]    |  +- io.dropwizard:dropwizard-assets:jar:2.0.28:compile
[INFO]    |  +- io.dropwizard:dropwizard-views-freemarker:jar:2.0.28:compile
[INFO]    |  +- in.vectorpro.dropwizard:dropwizard-swagger-ui:jar:4.6.2:compile
[INFO]    |  \- io.swagger.core.v3:swagger-jaxrs2:jar:2.1.13:compile
[INFO]    |     +- io.github.classgraph:classgraph:jar:4.8.138:compile
[INFO]    |     +- io.swagger.core.v3:swagger-models:jar:2.1.13:compile
[INFO]    |     +- io.swagger.core.v3:swagger-annotations:jar:2.1.13:compile
[INFO]    |     \- io.swagger.core.v3:swagger-integration:jar:2.1.13:compile
[INFO]    |        \- io.swagger.core.v3:swagger-core:jar:2.1.13:compile
[INFO]    \- com.phonepe.platform:db-sandbox-bundle:jar:1.0.3:compile
[INFO]       +- com.phonepe.platform:db-sandbox-model:jar:1.0.3:compile
[INFO]       +- com.phonepe.platform:db-sandbox-core:jar:1.0.3:compile
[INFO]       |  +- org.liquibase:liquibase-core:jar:3.6.3:compile
[INFO]       |  +- org.jgrapht:jgrapht-core:jar:1.4.0:compile
[INFO]       |  |  \- org.jheaps:jheaps:jar:0.11:compile
[INFO]       |  +- org.jooq:jooq:jar:3.14.7:compile
[INFO]       |  |  \- org.reactivestreams:reactive-streams:jar:1.0.2:compile
[INFO]       |  +- org.jooq:jooq-meta:jar:3.14.7:compile
[INFO]       |  \- org.jooq:jooq-codegen:jar:3.14.7:compile
[INFO]       +- com.flipkart.databuilderframework:databuilderframework:jar:0.5.11:compile
[INFO]       \- com.phonepe.payments:databuilder-execgraph-plugin:jar:1.0.5:compile
[INFO]          +- org.apache.maven:maven-core:jar:3.5.4:compile
[INFO]          |  +- org.apache.maven:maven-settings-builder:jar:3.5.4:compile
[INFO]          |  |  \- org.sonatype.plexus:plexus-sec-dispatcher:jar:1.4:compile
[INFO]          |  |     \- org.sonatype.plexus:plexus-cipher:jar:1.4:compile
[INFO]          |  +- org.apache.maven:maven-builder-support:jar:3.5.4:compile
[INFO]          |  +- org.apache.maven:maven-repository-metadata:jar:3.5.4:compile
[INFO]          |  +- org.apache.maven:maven-model-builder:jar:3.5.4:compile
[INFO]          |  +- org.apache.maven:maven-resolver-provider:jar:3.5.4:compile
[INFO]          |  +- org.apache.maven.resolver:maven-resolver-impl:jar:1.1.1:compile
[INFO]          |  +- org.apache.maven.resolver:maven-resolver-api:jar:1.1.1:compile
[INFO]          |  +- org.apache.maven.resolver:maven-resolver-spi:jar:1.1.1:compile
[INFO]          |  +- org.apache.maven.resolver:maven-resolver-util:jar:1.1.1:compile
[INFO]          |  +- org.apache.maven.shared:maven-shared-utils:jar:3.2.1:compile
[INFO]          |  +- org.eclipse.sisu:org.eclipse.sisu.inject:jar:0.3.3:compile
[INFO]          |  \- org.codehaus.plexus:plexus-component-annotations:jar:1.7.1:compile
[INFO]          +- com.google.code.gson:gson:jar:2.8.2:compile
[INFO]          \- org.json:json:jar:20180130:compile
[INFO] 
[INFO] ------------< com.phonepe.merchant.platform:stratos-models >------------
[INFO] Building stratos-models 2.0.67-SNAPSHOT                            [2/3]
[INFO]   from stratos-models/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[WARNING] 2 problems were encountered while building the effective model for org.apache.yetus:audience-annotations:jar:0.5.0 during dependency collection step for project (use -X to see details)
[WARNING] The POM for com.phonepe.platform:consent-model:jar:1.0.1 is invalid, transitive dependencies (if any) will not be available: 2 problems were encountered while building the effective model for com.phonepe.platform:consent-model:
[ERROR] Resolving expression: '${project.version}': Detected the following recursive expression cycle in 'project.version': [version] @ com.phonepe.platform:consent-model:${project.version}
[ERROR] 'version' is missing. @ com.phonepe.platform:consent-model:[unknown-version]

[WARNING] commons-io:commons-io/maven-metadata.xml failed to transfer from http://0.0.0.0/ during a previous attempt. This failure was cached in the local repository and resolution will not be reattempted until the update interval of maven-default-http-blocker has elapsed or updates are forced. Original error: Could not transfer metadata commons-io:commons-io/maven-metadata.xml from/to maven-default-http-blocker (http://0.0.0.0/): Blocked mirror for repositories: [phonepe-releases (http://artifactory.phonepe.com/content/repositories/releases, default, releases+snapshots), phonepe-snapshots (http://artifactory.phonepe.com/content/repositories/snapshots, default, releases+snapshots)]
[WARNING] The artifact org.hibernate:hibernate-validator:jar:6.1.7.Final has been relocated to org.hibernate.validator:hibernate-validator:jar:6.1.7.Final
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ stratos-models ---
[WARNING] commons-io:commons-io/maven-metadata.xml failed to transfer from http://0.0.0.0/ during a previous attempt. This failure was cached in the local repository and resolution will not be reattempted until the update interval of maven-default-http-blocker has elapsed or updates are forced. Original error: Could not transfer metadata commons-io:commons-io/maven-metadata.xml from/to maven-default-http-blocker (http://0.0.0.0/): Blocked mirror for repositories: [phonepe-releases (http://artifactory.phonepe.com/content/repositories/releases, default, releases+snapshots), phonepe-snapshots (http://artifactory.phonepe.com/content/repositories/snapshots, default, releases+snapshots)]
[WARNING] The artifact org.hibernate:hibernate-validator:jar:6.1.7.Final has been relocated to org.hibernate.validator:hibernate-validator:jar:6.1.7.Final
[INFO] com.phonepe.merchant.platform:stratos-models:jar:2.0.67-SNAPSHOT
[INFO] +- javax.validation:validation-api:jar:2.0.1.Final:compile
[INFO] +- org.hibernate.validator:hibernate-validator:jar:6.1.7.Final:compile
[INFO] |  +- jakarta.validation:jakarta.validation-api:jar:2.0.2:compile
[INFO] |  +- org.jboss.logging:jboss-logging:jar:3.3.2.Final:compile
[INFO] |  \- com.fasterxml:classmate:jar:1.3.4:compile
[INFO] +- com.fasterxml.jackson.core:jackson-annotations:jar:2.12.1:compile
[INFO] +- com.phonepe.payments:upi-client-model:jar:0.2.110:compile
[INFO] |  +- com.github.sgoertzen:sonar-break-maven-plugin:jar:*******:compile
[INFO] |  |  +- org.apache.maven:maven-plugin-api:jar:3.5.2:compile
[INFO] |  |  |  +- org.apache.maven:maven-model:jar:3.5.2:compile
[INFO] |  |  |  +- org.eclipse.sisu:org.eclipse.sisu.plexus:jar:0.3.3:compile
[INFO] |  |  |  |  \- javax.enterprise:cdi-api:jar:1.0:compile
[INFO] |  |  |  |     \- javax.annotation:jsr250-api:jar:1.0:compile
[INFO] |  |  |  +- org.codehaus.plexus:plexus-utils:jar:3.1.0:compile
[INFO] |  |  |  \- org.codehaus.plexus:plexus-classworlds:jar:2.5.2:compile
[INFO] |  |  +- org.apache.maven:maven-project:jar:2.2.1:compile
[INFO] |  |  |  +- org.apache.maven:maven-settings:jar:2.2.1:compile
[INFO] |  |  |  +- org.apache.maven:maven-profile:jar:2.2.1:compile
[INFO] |  |  |  +- org.apache.maven:maven-artifact-manager:jar:2.2.1:compile
[INFO] |  |  |  |  +- org.apache.maven.wagon:wagon-provider-api:jar:1.0-beta-6:compile
[INFO] |  |  |  |  \- backport-util-concurrent:backport-util-concurrent:jar:3.1:compile
[INFO] |  |  |  +- org.apache.maven:maven-plugin-registry:jar:2.2.1:compile
[INFO] |  |  |  +- org.codehaus.plexus:plexus-interpolation:jar:1.11:compile
[INFO] |  |  |  \- org.codehaus.plexus:plexus-container-default:jar:1.0-alpha-9-stable-1:compile
[INFO] |  |  |     \- classworlds:classworlds:jar:1.1-alpha-2:compile
[INFO] |  |  +- org.apache.maven.plugin-tools:maven-plugin-annotations:jar:3.5:compile
[INFO] |  |  +- commons-io:commons-io:jar:2.5:compile
[INFO] |  |  +- junit:junit:jar:4.12:compile
[INFO] |  |  |  \- org.hamcrest:hamcrest-core:jar:1.3:compile
[INFO] |  |  \- joda-time:joda-time:jar:2.9.6:compile
[INFO] |  +- com.google.code.findbugs:annotations:jar:3.0.1:compile
[INFO] |  |  \- net.jcip:jcip-annotations:jar:1.0:compile
[INFO] |  \- com.google.code.findbugs:jsr305:jar:3.0.1:compile
[INFO] +- com.phonepe.payments:netpe-model:jar:1.0.56:compile
[INFO] |  +- io.dropwizard:dropwizard-validation:jar:2.0.23:compile
[INFO] |  |  +- io.dropwizard:dropwizard-util:jar:2.0.23:compile
[INFO] |  |  \- org.glassfish:jakarta.el:jar:3.0.3:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-core:jar:2.9.9:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-databind:jar:2.12.1:compile
[INFO] |  \- com.phonepe.platform:release-scripts:zip:bin:0.1:compile
[INFO] +- com.phonepe.services:refund-orchestrator-models:jar:1.2.0:compile
[INFO] |  +- com.phonepe.pegasus:pegasus-pro-model:jar:1.2.99:compile
[INFO] |  +- com.phonepe.growth:offerengine-model:jar:4.2.43:compile
[INFO] |  +- com.phonepe.growth:cumulus:jar:1.1.2:compile
[INFO] |  +- com.phonepe.user:userservice-model:jar:2.0.8:compile
[INFO] |  +- com.phonepe.models:phonepe-model:jar:2.1.562:compile
[INFO] |  +- com.phonepe.payments:pg-transport-model:jar:0.1.126:compile
[INFO] |  +- com.phonepe.cp:caishen-api:jar:3.0.111:compile
[INFO] |  |  +- com.phonepe.payments:ft-client-model:jar:4.71:compile
[INFO] |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-joda:jar:2.13.5:compile
[INFO] |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-guava:jar:2.13.5:compile
[INFO] |  |  +- commons-validator:commons-validator:jar:1.8.0:compile
[INFO] |  |  |  +- commons-beanutils:commons-beanutils:jar:1.9.4:compile
[INFO] |  |  |  \- commons-digester:commons-digester:jar:2.1:compile
[INFO] |  |  +- io.dropwizard:dropwizard-configuration:jar:2.0.23:compile
[INFO] |  |  |  \- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.10.5:compile
[INFO] |  |  |     \- org.yaml:snakeyaml:jar:1.26:compile
[INFO] |  |  +- io.dropwizard:dropwizard-jdbi:jar:2.0.0-rc9:compile
[INFO] |  |  |  +- org.jdbi:jdbi:jar:2.78:compile
[INFO] |  |  |  \- io.dropwizard.metrics:metrics-jdbi:jar:4.1.0:compile
[INFO] |  |  +- io.dropwizard.metrics:metrics-core:jar:4.2.9:compile
[INFO] |  |  +- com.phonepe.platform:dropwizard-warmup-core:jar:2.0.30:compile
[INFO] |  |  +- com.squareup.okhttp3:okhttp:jar:4.11.0-PPE:compile
[INFO] |  |  |  +- com.squareup.okio:okio-jvm:jar:3.0.0:compile
[INFO] |  |  |  |  +- org.jetbrains.kotlin:kotlin-stdlib-jdk8:jar:1.5.31:compile
[INFO] |  |  |  |  |  \- org.jetbrains.kotlin:kotlin-stdlib-jdk7:jar:1.5.31:compile
[INFO] |  |  |  |  \- org.jetbrains.kotlin:kotlin-stdlib-common:jar:1.5.31:compile
[INFO] |  |  |  \- org.jetbrains.kotlin:kotlin-stdlib:jar:1.6.20:compile
[INFO] |  |  |     \- org.jetbrains:annotations:jar:13.0:compile
[INFO] |  |  \- com.phonepe.verified:pvc-models:jar:1.3.296:compile
[INFO] |  +- com.phonepe.payments:payment-model:jar:1.1.890-STAGE-SNAPSHOT:compile
[INFO] |  |  +- com.phonepe.services:merchant-mandates-model:jar:0.0.97-1-delegate-SNAPSHOT:compile
[INFO] |  |  |  +- com.phonepe.growth:pegasus-model:jar:1.2.61:compile
[INFO] |  |  |  |  +- com.phonepe.merchants:paradox-models:jar:2.7:compile
[INFO] |  |  |  |  +- com.phonepe.growth:offer-store-models:jar:1.0.28:compile
[INFO] |  |  |  |  |  +- com.phonepe.dataplatform:profile-model:jar:1.7.64:compile
[INFO] |  |  |  |  |  +- com.phonepe.dataplatform:yoda-model:jar:0.5.118:compile
[INFO] |  |  |  |  |  \- com.phonepe.growth:incentive-commons-model:jar:1.0.3:compile
[INFO] |  |  |  |  +- mysql:mysql-connector-java:jar:8.0.20:compile
[INFO] |  |  |  |  \- com.phonepe.pegasus:commons:jar:1.0.14:compile
[INFO] |  |  |  +- com.phonepe.payments:mandate-model:jar:0.0.133-DELEGATE-ALL-SNAPSHOT:compile
[INFO] |  |  |  +- com.phonepe.merchants:neo-models:jar:1.0.27:compile
[INFO] |  |  |  +- com.phonepe.services:checkout-model:jar:0.0.154:compile
[INFO] |  |  |  +- com.phonepe.payments.intent:upi-intent-utils:jar:1.0.17:compile
[INFO] |  |  |  \- com.phonepe.nexus:nexus-models:jar:1.0.4:compile
[INFO] |  |  +- com.phonepe.payments:vault-model:jar:0.0.106:compile
[INFO] |  |  \- com.phonepe.frontend:omada-model:jar:1.35:compile
[INFO] |  |     +- com.phonepe.frontend:connection-id-models:jar:1.6:compile
[INFO] |  |     \- com.google.protobuf:protobuf-java:jar:2.6.1:compile
[INFO] |  +- com.phonepe.payments:giftcard-provider-model:jar:1.0.27:compile
[INFO] |  +- com.phonepe.backend:app-service-models:jar:1.3.29:compile
[INFO] |  +- com.phonepe.platform:consent-model:jar:1.0.1:compile
[INFO] |  +- com.github.koushikr:fsm:jar:1.0.2:compile
[INFO] |  \- com.phonepe.models:merchant-service-model:jar:1.114:compile
[INFO] +- com.phonepe:error-configurator:jar:1.3:compile
[INFO] |  +- org.apache.commons:commons-collections4:jar:4.4:compile
[INFO] |  +- org.springframework.statemachine:spring-statemachine-core:jar:2.4.0:compile
[INFO] |  |  +- org.springframework:spring-tx:jar:5.3.3:compile
[INFO] |  |  \- org.springframework:spring-messaging:jar:5.3.3:compile
[INFO] |  +- org.springframework:spring-context:jar:5.3.3:compile
[INFO] |  |  +- org.springframework:spring-aop:jar:5.3.3:compile
[INFO] |  |  +- org.springframework:spring-beans:jar:5.3.3:compile
[INFO] |  |  +- org.springframework:spring-core:jar:5.3.3:compile
[INFO] |  |  |  \- org.springframework:spring-jcl:jar:5.3.3:compile
[INFO] |  |  \- org.springframework:spring-expression:jar:5.3.3:compile
[INFO] |  +- com.google.inject:guice:jar:5.0.1:compile
[INFO] |  |  +- javax.inject:javax.inject:jar:1:compile
[INFO] |  |  \- aopalliance:aopalliance:jar:1.0:compile
[INFO] |  \- ru.vyarus:dropwizard-guicey:jar:5.4.0:compile
[INFO] |     +- com.google.inject.extensions:guice-servlet:jar:5.0.1:compile
[INFO] |     \- ru.vyarus:generics-resolver:jar:3.0.3:compile
[INFO] +- com.tngtech.archunit:archunit:jar:1.3.0:test
[INFO] |  \- org.slf4j:slf4j-api:jar:2.0.12:compile
[INFO] +- com.tngtech.archunit:archunit-junit5:jar:1.3.0:test
[INFO] |  +- com.tngtech.archunit:archunit-junit5-api:jar:1.3.0:test
[INFO] |  \- com.tngtech.archunit:archunit-junit5-engine:jar:1.3.0:test
[INFO] |     \- com.tngtech.archunit:archunit-junit5-engine-api:jar:1.3.0:test
[INFO] +- com.phonepe.platform.http.v2:http-client-all:jar:4.0.44:compile
[INFO] |  +- com.phonepe.platform.http.v2:http-common:jar:4.0.44:compile
[INFO] |  |  +- com.phonepe.platform:zeus-models:jar:2.0.73:compile
[INFO] |  |  \- io.appform.ranger:ranger-server-common:jar:1.1.1:compile
[INFO] |  +- com.phonepe.platform.http.v2:http-executor:jar:4.0.44:compile
[INFO] |  |  \- com.phonepe.platform.http.v2:http-spyglass:jar:4.0.44:compile
[INFO] |  |     \- com.phonepe.platform:spyglass-common:jar:2.0.11:compile
[INFO] |  |        \- io.appform.opentracing.annotations:opentracing-annotations:jar:1.0.2:compile
[INFO] |  +- com.phonepe.platform.http.v2:http-discovery:jar:4.0.44:compile
[INFO] |  |  +- io.appform.ranger:ranger-discovery-bundle:jar:1.1.1:compile
[INFO] |  |  |  \- dev.failsafe:failsafe:jar:3.2.4:compile
[INFO] |  |  +- com.phonepe.platform:zeus-provider:jar:2.0.73:compile
[INFO] |  |  +- com.phonepe.platform:zeus-client:jar:2.0.73:compile
[INFO] |  |  |  \- org.oxerr.commons:commons-jax-rs-exception-mapper:jar:2.3.1:compile
[INFO] |  |  +- com.phonepe.platform:zeus-router-registry:jar:2.0.73:compile
[INFO] |  |  +- io.appform.ranger:ranger-http-client:jar:1.1.1:compile
[INFO] |  |  |  +- io.appform.ranger:ranger-http:jar:1.1.1:compile
[INFO] |  |  |  |  +- io.appform.ranger:ranger-core:jar:1.1.1:compile
[INFO] |  |  |  |  +- io.appform.ranger:ranger-http-model:jar:1.1.1:compile
[INFO] |  |  |  |  \- io.appform.ranger:ranger-core:test-jar:tests:1.1.1:compile
[INFO] |  |  |  \- io.appform.ranger:ranger-client:jar:1.1.1:compile
[INFO] |  |  +- io.appform.ranger:ranger-zk-client:jar:1.1.1:compile
[INFO] |  |  |  \- io.appform.ranger:ranger-zookeeper:jar:1.1.1:compile
[INFO] |  |  \- org.apache.curator:curator-recipes:jar:4.2.0:compile
[INFO] |  +- com.phonepe.platform.http.v2:http-client:jar:4.0.44:compile
[INFO] |  |  +- com.phonepe.platform.http.v2:http-tracing:jar:4.0.44:compile
[INFO] |  |  |  +- io.opentracing.contrib:opentracing-okhttp3:jar:3.0.0:compile
[INFO] |  |  |  |  \- io.opentracing.contrib:opentracing-concurrent:jar:0.4.0:compile
[INFO] |  |  |  +- io.opentracing:opentracing-util:jar:0.33.0:compile
[INFO] |  |  |  |  \- io.opentracing:opentracing-noop:jar:0.33.0:compile
[INFO] |  |  |  \- io.opentracing:opentracing-api:jar:0.33.0:compile
[INFO] |  |  \- com.phonepe.platform.http.v2:http-common:test-jar:tests:4.0.44:compile
[INFO] |  +- com.phonepe.platform.http.v2:http-feign:jar:4.0.44:compile
[INFO] |  \- org.bouncycastle:bcprov-jdk15on:jar:1.70:compile
[INFO] +- com.phonepe.services:crixus-core:jar:1.0.42:compile
[INFO] |  +- com.google.guava:guava:jar:30.1.1-jre:compile
[INFO] |  |  +- com.google.guava:failureaccess:jar:1.0.1:compile
[INFO] |  |  +- com.google.guava:listenablefuture:jar:9999.0-empty-to-avoid-conflict-with-guava:compile
[INFO] |  |  +- org.checkerframework:checker-qual:jar:3.8.0:compile
[INFO] |  |  +- com.google.errorprone:error_prone_annotations:jar:2.5.1:compile
[INFO] |  |  \- com.google.j2objc:j2objc-annotations:jar:1.3:compile
[INFO] |  +- org.reflections:reflections:jar:0.9.11:compile
[INFO] |  +- org.aspectj:aspectjrt:jar:1.9.8:compile
[INFO] |  +- org.javassist:javassist:jar:3.28.0-GA:compile
[INFO] |  +- com.phonepe.services:zapier:jar:1.0:compile
[INFO] |  +- com.flipkart.databuilderframework:databuilderframework:jar:0.5.11:compile
[INFO] |  +- com.phonepe.olympus-im:olympus-im-client:jar:1.2.29:compile
[INFO] |  |  +- io.dropwizard:dropwizard-auth:jar:2.0.23:compile
[INFO] |  |  |  +- io.dropwizard.metrics:metrics-caffeine:jar:4.1.23:compile
[INFO] |  |  |  \- jakarta.annotation:jakarta.annotation-api:jar:1.3.5:compile
[INFO] |  |  +- com.phonepe.olympus-im:olympus-im-core:jar:1.2.29:compile
[INFO] |  |  |  +- com.phonepe.olympus-im:olympus-im-models:jar:1.2.29:compile
[INFO] |  |  |  |  +- com.phonepe.services:warden-workflow-models:jar:2.0.25:compile
[INFO] |  |  |  |  |  +- io.swagger:swagger-jersey2-jaxrs:jar:1.6.5:compile
[INFO] |  |  |  |  |  +- io.swagger:swagger-annotations:jar:1.6.5:compile
[INFO] |  |  |  |  |  +- com.phonepe.services:guardian-models:jar:1.1.19:compile
[INFO] |  |  |  |  |  |  \- com.phonepe.platform:dropwizard-requestinfo-bundle:jar:1.3.5-6:compile
[INFO] |  |  |  |  |  +- io.raven.dropwizard:dropwizard-jmxmp:jar:2.0.21-1:compile
[INFO] |  |  |  |  |  |  \- org.glassfish.main.external:jmxremote_optional-repackaged:jar:5.0:compile
[INFO] |  |  |  |  |  \- com.phonepe.merchants.platform:varys:jar:1.0.6:compile
[INFO] |  |  |  |  |     \- com.sun.mail:javax.mail:jar:1.5.5:compile
[INFO] |  |  |  |  |        \- javax.activation:activation:jar:1.1:compile
[INFO] |  |  |  |  \- com.phonepe.services:warden-core-models:jar:2.0.25:compile
[INFO] |  |  |  |     \- com.phonepe.services:warden-workflow:jar:2.0.25:compile
[INFO] |  |  |  +- org.bitbucket.b_c:jose4j:jar:0.7.7:compile
[INFO] |  |  |  \- com.auth0:java-jwt:jar:3.3.0:compile
[INFO] |  |  +- com.phonepe.olympus-im:olympus-im-routing:jar:1.2.29:compile
[INFO] |  |  |  \- com.phonepe.platform:dropwizard-primer-bundle:jar:1.0.1:compile
[INFO] |  |  +- com.github.ben-manes.caffeine:caffeine:jar:2.6.2:compile
[INFO] |  |  +- com.github.rholder:guava-retrying:jar:2.0.0:compile
[INFO] |  |  +- io.github.openfeign:feign-slf4j:jar:11.0:compile
[INFO] |  |  +- com.phonepe.platform:audit-logger-client:jar:1.4:compile
[INFO] |  |  |  \- com.phonepe.platform:audit-logger-models:jar:1.4:compile
[INFO] |  |  +- commons-collections:commons-collections:jar:3.2.2:compile
[INFO] |  |  +- com.fasterxml.uuid:java-uuid-generator:jar:3.1.4:compile
[INFO] |  |  +- com.google.api-client:google-api-client-java6:jar:1.24.1:compile
[INFO] |  |  |  +- com.google.api-client:google-api-client:jar:1.24.1:compile
[INFO] |  |  |  |  +- com.google.oauth-client:google-oauth-client:jar:1.24.1:compile
[INFO] |  |  |  |  |  \- com.google.http-client:google-http-client:jar:1.24.1:compile
[INFO] |  |  |  |  \- com.google.http-client:google-http-client-jackson2:jar:1.24.1:compile
[INFO] |  |  |  \- com.google.oauth-client:google-oauth-client-java6:jar:1.24.1:compile
[INFO] |  |  +- com.google.apis:google-api-services-oauth2:jar:v2-rev141-1.25.0:compile
[INFO] |  |  +- org.apache.httpcomponents:httpclient:jar:4.5.13:compile
[INFO] |  |  |  +- org.apache.httpcomponents:httpcore:jar:4.4.13:compile
[INFO] |  |  |  \- commons-codec:commons-codec:jar:1.11:compile
[INFO] |  |  +- io.appform.commonutils:common-utils:jar:1.4:compile
[INFO] |  |  |  +- org.functionaljava:functionaljava:jar:3.0:compile
[INFO] |  |  |  \- org.mongodb:bson:jar:2.3:compile
[INFO] |  |  \- com.phonepe.platform:eventbus-recipes:jar:2.4:compile
[INFO] |  |     \- com.slack.api:slack-api-client:jar:1.27.2:compile
[INFO] |  |        \- com.slack.api:slack-api-model:jar:1.27.2:compile
[INFO] |  +- com.phonepe.payments:databuilder-execgraph-plugin:jar:1.0.7:compile
[INFO] |  |  +- org.apache.maven:maven-core:jar:3.5.4:compile
[INFO] |  |  |  +- org.apache.maven:maven-settings-builder:jar:3.5.4:compile
[INFO] |  |  |  |  \- org.sonatype.plexus:plexus-sec-dispatcher:jar:1.4:compile
[INFO] |  |  |  |     \- org.sonatype.plexus:plexus-cipher:jar:1.4:compile
[INFO] |  |  |  +- org.apache.maven:maven-builder-support:jar:3.5.4:compile
[INFO] |  |  |  +- org.apache.maven:maven-repository-metadata:jar:3.5.4:compile
[INFO] |  |  |  +- org.apache.maven:maven-model-builder:jar:3.5.4:compile
[INFO] |  |  |  +- org.apache.maven:maven-resolver-provider:jar:3.5.4:compile
[INFO] |  |  |  +- org.apache.maven.resolver:maven-resolver-impl:jar:1.1.1:compile
[INFO] |  |  |  +- org.apache.maven.resolver:maven-resolver-api:jar:1.1.1:compile
[INFO] |  |  |  +- org.apache.maven.resolver:maven-resolver-spi:jar:1.1.1:compile
[INFO] |  |  |  +- org.apache.maven.resolver:maven-resolver-util:jar:1.1.1:compile
[INFO] |  |  |  +- org.apache.maven.shared:maven-shared-utils:jar:3.2.1:compile
[INFO] |  |  |  +- org.eclipse.sisu:org.eclipse.sisu.inject:jar:0.3.3:compile
[INFO] |  |  |  \- org.codehaus.plexus:plexus-component-annotations:jar:1.7.1:compile
[INFO] |  |  +- com.google.code.gson:gson:jar:2.8.2:compile
[INFO] |  |  \- org.json:json:jar:20180130:compile
[INFO] |  +- org.mariadb.jdbc:mariadb-java-client:jar:2.7.2:compile
[INFO] |  +- io.appform.dropwizard.sharding:db-sharding-bundle:jar:2.0.28-7:compile
[INFO] |  |  \- cglib:cglib-nodep:jar:3.3.0:compile
[INFO] |  +- org.hibernate:hibernate-envers:jar:5.4.30.Final:compile
[INFO] |  |  \- org.hibernate:hibernate-core:jar:5.4.30.Final:compile
[INFO] |  |     +- antlr:antlr:jar:2.7.7:compile
[INFO] |  |     +- org.jboss.spec.javax.transaction:jboss-transaction-api_1.2_spec:jar:1.1.1.Final:compile
[INFO] |  |     +- org.jboss:jandex:jar:2.2.3.Final:compile
[INFO] |  |     +- javax.activation:javax.activation-api:jar:1.2.0:compile
[INFO] |  |     +- org.dom4j:dom4j:jar:2.1.3:compile
[INFO] |  |     +- org.hibernate.common:hibernate-commons-annotations:jar:5.1.2.Final:compile
[INFO] |  |     \- org.glassfish.jaxb:jaxb-runtime:jar:2.3.1:compile
[INFO] |  |        +- org.glassfish.jaxb:txw2:jar:2.3.1:compile
[INFO] |  |        +- com.sun.istack:istack-commons-runtime:jar:3.0.7:compile
[INFO] |  |        +- org.jvnet.staxex:stax-ex:jar:1.8:compile
[INFO] |  |        \- com.sun.xml.fastinfoset:FastInfoset:jar:1.2.15:compile
[INFO] |  +- com.phonepe.services:circumscribe-aerospike:jar:1.0.1:compile
[INFO] |  |  +- com.phonepe.services:circumscribe-commons:jar:1.0.1:compile
[INFO] |  |  \- com.aerospike:aerospike-client:jar:4.4.18:compile
[INFO] |  |     +- org.gnu:gnu-crypto:jar:2.0.1:compile
[INFO] |  |     +- org.luaj:luaj-jse:jar:3.0:compile
[INFO] |  |     \- org.mindrot:jbcrypt:jar:0.4:compile
[INFO] |  +- io.dropwizard:dropwizard-jackson:jar:2.0.23:compile
[INFO] |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.10.5:compile
[INFO] |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.10.5:compile
[INFO] |  |  +- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.10.5:compile
[INFO] |  |  \- com.fasterxml.jackson.module:jackson-module-afterburner:jar:2.10.5:compile
[INFO] |  +- io.dropwizard:dropwizard-core:jar:2.0.23:compile
[INFO] |  |  +- io.dropwizard:dropwizard-logging:jar:2.0.23:compile
[INFO] |  |  |  +- io.dropwizard.metrics:metrics-logback:jar:4.1.23:compile
[INFO] |  |  |  +- io.dropwizard.logback:logback-throttling-appender:jar:1.1.0:compile
[INFO] |  |  |  \- org.slf4j:log4j-over-slf4j:jar:1.7.31:runtime
[INFO] |  |  +- io.dropwizard:dropwizard-jersey:jar:2.0.23:compile
[INFO] |  |  |  +- org.glassfish.jersey.ext:jersey-metainf-services:jar:2.33:runtime
[INFO] |  |  |  +- org.glassfish.jersey.inject:jersey-hk2:jar:2.33:runtime
[INFO] |  |  |  |  \- org.glassfish.hk2:hk2-locator:jar:2.6.1:runtime
[INFO] |  |  |  +- io.dropwizard.metrics:metrics-jersey2:jar:4.1.23:compile
[INFO] |  |  |  +- com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider:jar:2.10.5:compile
[INFO] |  |  |  |  +- com.fasterxml.jackson.jaxrs:jackson-jaxrs-base:jar:2.10.5:compile
[INFO] |  |  |  |  \- com.fasterxml.jackson.module:jackson-module-jaxb-annotations:jar:2.10.5:compile
[INFO] |  |  |  +- org.glassfish.hk2:hk2-api:jar:2.6.1:compile
[INFO] |  |  |  |  +- org.glassfish.hk2:hk2-utils:jar:2.6.1:compile
[INFO] |  |  |  |  \- org.glassfish.hk2.external:aopalliance-repackaged:jar:2.6.1:compile
[INFO] |  |  |  +- org.glassfish.jersey.containers:jersey-container-servlet:jar:2.33:runtime
[INFO] |  |  |  +- org.glassfish.jersey.containers:jersey-container-servlet-core:jar:2.33:compile
[INFO] |  |  |  \- org.eclipse.jetty:jetty-io:jar:9.4.42.v20210604:compile
[INFO] |  |  +- io.dropwizard:dropwizard-servlets:jar:2.0.23:compile
[INFO] |  |  |  \- io.dropwizard.metrics:metrics-annotation:jar:4.1.23:compile
[INFO] |  |  +- io.dropwizard:dropwizard-jetty:jar:2.0.23:compile
[INFO] |  |  |  +- org.eclipse.jetty:jetty-servlets:jar:9.4.42.v20210604:compile
[INFO] |  |  |  |  \- org.eclipse.jetty:jetty-continuation:jar:9.4.42.v20210604:compile
[INFO] |  |  |  \- org.eclipse.jetty:jetty-http:jar:9.4.42.v20210604:compile
[INFO] |  |  +- io.dropwizard:dropwizard-lifecycle:jar:2.0.23:compile
[INFO] |  |  +- io.dropwizard.metrics:metrics-jetty9:jar:4.1.23:compile
[INFO] |  |  +- io.dropwizard.metrics:metrics-jvm:jar:4.1.23:compile
[INFO] |  |  +- io.dropwizard.metrics:metrics-jmx:jar:4.1.23:compile
[INFO] |  |  +- io.dropwizard.metrics:metrics-servlets:jar:4.1.23:compile
[INFO] |  |  |  +- io.dropwizard.metrics:metrics-json:jar:4.1.23:compile
[INFO] |  |  |  \- com.helger:profiler:jar:1.1.1:compile
[INFO] |  |  +- io.dropwizard.metrics:metrics-healthchecks:jar:4.1.23:compile
[INFO] |  |  +- io.dropwizard:dropwizard-request-logging:jar:2.0.23:compile
[INFO] |  |  |  \- ch.qos.logback:logback-access:jar:1.2.3:compile
[INFO] |  |  +- ch.qos.logback:logback-classic:jar:1.2.3:compile
[INFO] |  |  +- jakarta.servlet:jakarta.servlet-api:jar:4.0.4:compile
[INFO] |  |  +- jakarta.ws.rs:jakarta.ws.rs-api:jar:2.1.6:compile
[INFO] |  |  +- net.sourceforge.argparse4j:argparse4j:jar:0.8.1:compile
[INFO] |  |  +- org.eclipse.jetty:jetty-security:jar:9.4.42.v20210604:compile
[INFO] |  |  +- org.eclipse.jetty:jetty-server:jar:9.4.42.v20210604:compile
[INFO] |  |  +- org.eclipse.jetty:jetty-servlet:jar:9.4.42.v20210604:compile
[INFO] |  |  |  \- org.eclipse.jetty:jetty-util-ajax:jar:9.4.42.v20210604:compile
[INFO] |  |  +- org.eclipse.jetty:jetty-util:jar:9.4.42.v20210604:compile
[INFO] |  |  +- org.eclipse.jetty.toolchain.setuid:jetty-setuid-java:jar:1.0.4:compile
[INFO] |  |  +- org.glassfish.hk2.external:jakarta.inject:jar:2.6.1:compile
[INFO] |  |  +- org.glassfish.jersey.core:jersey-common:jar:2.33:compile
[INFO] |  |  |  \- org.glassfish.hk2:osgi-resource-locator:jar:1.0.3:compile
[INFO] |  |  \- org.glassfish.jersey.ext:jersey-bean-validation:jar:2.33:compile
[INFO] |  +- io.dropwizard:dropwizard-metrics:jar:2.0.23:compile
[INFO] |  +- io.dropwizard:dropwizard-hibernate:jar:2.0.28:compile
[INFO] |  |  +- io.dropwizard:dropwizard-db:jar:2.0.28:compile
[INFO] |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-hibernate5:jar:2.10.5:compile
[INFO] |  |  |  \- javax.transaction:javax.transaction-api:jar:1.3:compile
[INFO] |  |  +- javax.persistence:javax.persistence-api:jar:2.2:compile
[INFO] |  |  +- org.apache.tomcat:tomcat-***********************
[INFO] |  |  |  \- org.apache.tomcat:tomcat-juli:jar:9.0.56:compile
[INFO] |  |  +- org.glassfish.jersey.core:jersey-client:jar:2.33:compile
[INFO] |  |  +- org.glassfish.jersey.core:jersey-server:jar:2.33:compile
[INFO] |  |  +- org.jadira.usertype:usertype.core:jar:7.0.0.CR1:runtime
[INFO] |  |  |  \- org.jadira.usertype:usertype.spi:jar:7.0.0.CR1:runtime
[INFO] |  |  +- jakarta.activation:jakarta.activation-api:jar:1.2.2:compile
[INFO] |  |  \- jakarta.xml.bind:jakarta.xml.bind-api:jar:2.3.3:compile
[INFO] |  +- javax.ws.rs:javax.ws.rs-api:jar:2.1.1:compile
[INFO] |  +- org.apache.commons:commons-jcs-core:jar:2.2.2:compile
[INFO] |  |  \- commons-logging:commons-logging:jar:1.2:compile
[INFO] |  +- io.raven.dropwizard:dropwizard-oor:jar:2.0.24-1:compile
[INFO] |  +- in.vectorpro.dropwizard:dropwizard-swagger:jar:2.0.23-2:compile
[INFO] |  |  +- io.dropwizard:dropwizard-views:jar:2.0.23:compile
[INFO] |  |  +- io.dropwizard:dropwizard-assets:jar:2.0.23:compile
[INFO] |  |  +- io.dropwizard:dropwizard-views-freemarker:jar:2.0.23:compile
[INFO] |  |  +- in.vectorpro.dropwizard:dropwizard-swagger-ui:jar:3.51.1:compile
[INFO] |  |  +- io.swagger.core.v3:swagger-core:jar:2.0.10:compile
[INFO] |  |  |  +- io.swagger.core.v3:swagger-annotations:jar:2.0.10:compile
[INFO] |  |  |  \- io.swagger.core.v3:swagger-models:jar:2.0.10:compile
[INFO] |  |  \- io.swagger.core.v3:swagger-jaxrs2:jar:2.0.10:compile
[INFO] |  |     +- io.github.classgraph:classgraph:jar:4.6.32:compile
[INFO] |  |     \- io.swagger.core.v3:swagger-integration:jar:2.0.10:compile
[INFO] |  +- com.phonepe.data.provider:rosey-data-provider-bundle:jar:2.4:compile
[INFO] |  |  +- com.phonepe.platform:rosey-standanlone-config:jar:1.17:compile
[INFO] |  |  \- com.phonepe.data.provider:data-provider-core:jar:2.4:compile
[INFO] |  |     \- com.fasterxml.jackson.dataformat:jackson-dataformat-xml:jar:2.9.0:compile
[INFO] |  |        +- org.codehaus.woodstox:stax2-api:jar:3.1.4:compile
[INFO] |  |        \- com.fasterxml.woodstox:woodstox-core:jar:5.0.3:compile
[INFO] |  +- com.platform:validation-bundle:jar:********:compile
[INFO] |  +- com.phonepe.platform:aerospike-bundle:jar:1.3.13-6:compile
[INFO] |  +- com.netflix.hystrix:hystrix-core:jar:1.5.18:compile
[INFO] |  |  +- com.netflix.archaius:archaius-core:jar:0.4.1:compile
[INFO] |  |  |  \- commons-configuration:commons-configuration:jar:1.8:compile
[INFO] |  |  |     \- commons-lang:commons-lang:jar:2.6:compile
[INFO] |  |  +- io.reactivex:rxjava:jar:1.2.0:compile
[INFO] |  |  \- org.hdrhistogram:HdrHistogram:jar:2.1.9:compile
[INFO] |  +- io.appform.core:hystrix-function-wrapper:jar:1.0.0:compile
[INFO] |  +- com.hystrix:hystrix-configurator:jar:0.0.13:compile
[INFO] |  +- org.zapodot:hystrix-dropwizard-bundle:jar:1.0.2:compile
[INFO] |  |  +- com.netflix.hystrix:hystrix-metrics-event-stream:jar:1.5.12:compile
[INFO] |  |  |  \- com.netflix.hystrix:hystrix-serialization:jar:1.5.12:runtime
[INFO] |  |  \- com.netflix.hystrix:hystrix-codahale-metrics-publisher:jar:1.5.12:compile
[INFO] |  +- org.apache.zookeeper:zookeeper:jar:3.4.6:compile
[INFO] |  |  +- jline:jline:jar:0.9.94:compile
[INFO] |  |  \- io.netty:netty:jar:3.7.0.Final:compile
[INFO] |  +- org.apache.curator:curator-framework:jar:4.2.0:compile
[INFO] |  |  \- org.apache.curator:curator-client:jar:4.2.0:compile
[INFO] |  +- com.phonepe.platform:executors:jar:0.0.5:compile
[INFO] |  +- com.phonepe.platform.http.server.metrics:api-metrics:jar:0.0.16:compile
[INFO] |  +- com.raskasa.metrics:metrics-okhttp:jar:0.5.5-PP:compile
[INFO] |  +- io.appform.functionmetrics:function-metrics:jar:1.0.13:compile
[INFO] |  |  \- org.aspectj:aspectjweaver:jar:1.9.8:compile
[INFO] |  \- com.phonepe.platform:metric-ingestion-bundle:jar:1.70:compile
[INFO] |     +- com.phonepe.platform:metric-ingestion-models:jar:1.70:compile
[INFO] |     +- com.phonepe.platform:metric-ingestion-client:jar:1.70:compile
[INFO] |     |  \- com.squareup.okhttp3:logging-interceptor:jar:4.9.0:compile
[INFO] |     \- io.micrometer:micrometer-core:jar:1.11.5:compile
[INFO] |        +- io.micrometer:micrometer-commons:jar:1.11.5:compile
[INFO] |        +- io.micrometer:micrometer-observation:jar:1.11.5:compile
[INFO] |        \- org.latencyutils:LatencyUtils:jar:2.0.3:runtime
[INFO] +- com.phonepe.services:crixus-core-models:jar:1.0.42:compile
[INFO] |  +- dom4j:dom4j:jar:1.1:compile
[INFO] |  \- com.phonepe.platform:event-ingestion-models:jar:2.0.69:compile
[INFO] +- org.mockito:mockito-junit-jupiter:jar:4.3.1:test
[INFO] |  +- org.mockito:mockito-core:jar:4.3.1:test
[INFO] |  |  +- net.bytebuddy:byte-buddy:jar:1.12.7:compile
[INFO] |  |  +- net.bytebuddy:byte-buddy-agent:jar:1.12.7:test
[INFO] |  |  \- org.objenesis:objenesis:jar:3.2:test
[INFO] |  \- org.junit.jupiter:junit-jupiter-api:jar:5.7.1:test
[INFO] |     +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |     \- org.junit.platform:junit-platform-commons:jar:1.7.1:test
[INFO] +- org.projectlombok:lombok:jar:1.18.22:compile
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.7.1:test
[INFO] |  +- org.apiguardian:apiguardian-api:jar:1.1.0:compile
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.7.1:test
[INFO] +- org.junit.jupiter:junit-jupiter-params:jar:5.7.1:test
[INFO] \- com.phonepe.verified:kaizen-core:jar:0.0.5:compile
[INFO]    +- com.phonepe.verified:kaizen-models:jar:0.0.5:compile
[INFO]    |  \- com.phonepe.shadow:shadow-v2-models:jar:1.0.104:compile
[INFO]    +- io.dropwizard:dropwizard-forms:jar:2.0.23:compile
[INFO]    |  \- org.glassfish.jersey.media:jersey-media-multipart:jar:2.33:compile
[INFO]    |     \- org.jvnet.mimepull:mimepull:jar:1.9.13:compile
[INFO]    +- com.phonepe.platform:requestinfo-bundle:jar:2.0.23-36:compile
[INFO]    |  +- com.phonepe.platform:requestinfo-core:jar:2.0.23-36:compile
[INFO]    |  \- com.phonepe.platform:atlas-client:jar:2.1.143:compile
[INFO]    |     +- com.phonepe.dataplatform:mvel-common-functions:jar:0.1.15:compile
[INFO]    |     |  \- com.github.davidmoten:geo:jar:0.7.1:compile
[INFO]    |     |     \- com.github.davidmoten:grumpy-core:jar:0.2.2:compile
[INFO]    |     |        \- org.apache.commons:commons-math3:jar:3.2:compile
[INFO]    |     +- io.sgr:s2-geometry-library-java:jar:1.0.0:compile
[INFO]    |     \- com.phonepe.platform:spyglass-core:jar:2.0.21:compile
[INFO]    +- com.phonepe.platform:requestinfo-models:jar:2.0.23-23:compile
[INFO]    |  \- io.dropwizard:dropwizard-maxmind:jar:1.3.7-1:compile
[INFO]    +- org.apache.tika:tika-core:jar:2.0.0:compile
[INFO]    +- io.appform.hope:hope-lang:jar:2.0.3:compile
[INFO]    |  +- io.appform.hope:hope-core:jar:2.0.3:compile
[INFO]    |  +- com.jayway.jsonpath:json-path:jar:2.7.0:compile
[INFO]    |  \- net.minidev:json-smart:jar:2.4.11:compile
[INFO]    |     \- net.minidev:accessors-smart:jar:2.4.11:compile
[INFO]    |        \- org.ow2.asm:asm:jar:9.3:compile
[INFO]    +- com.github.jknack:handlebars:jar:4.3.0:compile
[INFO]    +- com.github.jknack:handlebars-jackson2:jar:4.3.0:compile
[INFO]    +- io.appform.dropwizard.actors:dropwizard-rabbitmq-actors:jar:2.0.28-2:compile
[INFO]    |  +- org.apache.commons:commons-lang3:jar:3.12.0:compile
[INFO]    |  \- org.junit.vintage:junit-vintage-engine:jar:5.8.1:compile
[INFO]    +- com.rabbitmq:amqp-client:jar:5.12.0:compile
[INFO]    +- com.phonepe.platform.docstore:docstore-client:jar:2.9:compile
[INFO]    |  \- com.phonepe.platform:docstore-common:jar:5.2.15:compile
[INFO]    +- com.phonepe.gandalf:gandalf-client:jar:2.0.40:compile
[INFO]    |  +- com.phonepe.gandalf:gandalf-core:jar:2.0.40:compile
[INFO]    |  |  \- com.phonepe.gandalf:gandalf-models:jar:2.0.40:compile
[INFO]    |  +- org.glassfish.jersey.bundles.repackaged:jersey-guava:jar:2.6:compile
[INFO]    |  \- org.glassfish.jersey.containers:jersey-container-grizzly2-http:jar:2.32:compile
[INFO]    |     \- org.glassfish.grizzly:grizzly-http-server:jar:2.4.4:compile
[INFO]    |        \- org.glassfish.grizzly:grizzly-http:jar:2.4.4:compile
[INFO]    |           \- org.glassfish.grizzly:grizzly-framework:jar:2.4.4:compile
[INFO]    +- com.phonepe.verified:oncall-dropwizard-bundle:jar:1.0.3:compile
[INFO]    +- com.phonepe.platform:vision-models:jar:2.1.106:compile
[INFO]    +- com.phonepe.shadow:shadow-v2-services:jar:1.0.104:compile
[INFO]    +- com.phonepe.shadow:shadow-v2-core:jar:1.0.104:compile
[INFO]    +- com.phonepe.verified:drishti-models:jar:1.0.8:compile
[INFO]    +- com.phonepe.platform:heimdall-models:jar:1.0.73:compile
[INFO]    |  \- org.freemarker:freemarker:jar:2.3.30:compile
[INFO]    +- com.phonepe.platform:sentinel-client:jar:1.161:compile
[INFO]    |  +- com.phonepe.platform:sentinel-models:jar:1.119:compile
[INFO]    |  |  +- com.phonepe.frontend:consent-models:jar:1.0.24:compile
[INFO]    |  |  \- com.phonepe.data.selector:vmn-data-selector:jar:1.16:compile
[INFO]    |  |     +- com.phonepe.data.selector:data-selector-core:jar:1.16:compile
[INFO]    |  |     \- com.flipkart.foxtrot:foxtrot-common:jar:6.3.1-138:compile
[INFO]    |  |        +- com.smoketurner:dropwizard-swagger:jar:1.3.7-1:compile
[INFO]    |  |        \- io.raven.dropwizard:dropwizard-riemann:jar:1.3.13-1:compile
[INFO]    |  |           \- io.riemann:metrics3-riemann-reporter:jar:0.4.5:compile
[INFO]    |  |              +- com.codahale.metrics:metrics-core:jar:3.0.1:compile
[INFO]    |  |              \- io.riemann:riemann-java-client:jar:0.4.5:compile
[INFO]    |  +- org.junit.jupiter:junit-jupiter-migrationsupport:jar:5.6.0:compile
[INFO]    |  \- com.squareup.okio:okio:jar:2.2.2:compile
[INFO]    +- com.phonepe.platform.filters:api-killer-core:jar:1.26:compile
[INFO]    +- com.phonepe.platform.filters:api-killer-killswitch:jar:1.26:compile
[INFO]    +- com.phonepe.platform.killswitch:killswitch-client:jar:2.0.7:compile
[INFO]    |  \- com.phonepe.platform.killswitch:killswitch-common:jar:2.0.7:compile
[INFO]    +- com.phonepe.platform:columbus-core-models:jar:1.0.4:compile
[INFO]    +- com.phonepe.platform:atlas-model:jar:2.1.148:compile
[INFO]    +- com.phonepe.platform:clockwork-models:jar:2.3.14:compile
[INFO]    +- com.phonepe.platform:rosey-dropwizard-config:jar:2.0.86:compile
[INFO]    |  +- com.phonepe.platform:rosey-client-models:jar:2.0.86:compile
[INFO]    |  \- org.apache.commons:commons-text:jar:1.9:compile
[INFO]    +- com.phonepe.dataplatform:event-ingestion-client:jar:2.1.0-12-SNAPSHOT:compile
[INFO]    |  +- org.clojars.nitishgoyal13:bigqueue:jar:0.7.8:compile
[INFO]    |  +- com.phonepe.platform:checkmate-lib:jar:0.1.4:compile
[INFO]    |  \- com.phonepe.platform:requestinfo-utils:jar:2.0.23-37:compile
[INFO]    +- org.springframework.data:spring-data-commons:jar:2.7.5:compile
[INFO]    +- com.fasterxml.jackson.dataformat:jackson-dataformat-csv:jar:2.13.1:compile
[INFO]    +- io.github.openfeign:feign-core:jar:11.2:compile
[INFO]    +- io.github.openfeign:feign-jackson:jar:11.2:compile
[INFO]    +- io.github.openfeign:feign-httpclient:jar:11.2:compile
[INFO]    +- io.github.openfeign:feign-okhttp:jar:11.2:compile
[INFO]    +- guru.nidi:graphviz-java:jar:0.18.1:compile
[INFO]    |  +- org.webjars.npm:viz.js-graphviz-java:jar:2.1.3:compile
[INFO]    |  +- guru.nidi.com.kitfox:svgSalamander:jar:1.1.3:compile
[INFO]    |  +- net.arnx:nashorn-promise:jar:0.1.1:compile
[INFO]    |  +- org.apache.commons:commons-exec:jar:1.3:compile
[INFO]    |  +- org.slf4j:jcl-over-slf4j:jar:1.7.30:compile
[INFO]    |  +- org.slf4j:jul-to-slf4j:jar:1.7.30:compile
[INFO]    |  \- guru.nidi.com.eclipsesource.j2v8:j2v8_macosx_x86_64:jar:4.6.0:compile
[INFO]    +- com.phonepe.growth:mustang:jar:2.3.0:compile
[INFO]    |  \- org.apache.maven:maven-artifact:jar:3.8.5:compile
[INFO]    +- com.phonepe.platform.bullhorn:bullhorn-models:jar:1.0.132:compile
[INFO]    +- com.phonepe.growth:zencast-model:jar:1.1.79:compile
[INFO]    +- com.phonepe.growth:campaign-store-models:jar:2.0.29:compile
[INFO]    |  +- com.flipkart.flipcast:flipcast-models:jar:3.1.26:compile
[INFO]    |  +- com.phonepe.platform:validation-bundle:jar:********:compile
[INFO]    |  +- com.flipkart.zjsonpatch:zjsonpatch:jar:0.4.3:compile
[INFO]    |  +- javax.xml.bind:jaxb-api:jar:2.3.0:compile
[INFO]    |  \- com.github.phaneesh:sonar-break-maven-plugin:jar:1.2.7:compile
[INFO]    +- io.appform.rules:json-rules:jar:1.0.12:compile
[INFO]    +- com.openhtmltopdf:openhtmltopdf-pdfbox:jar:1.0.6:compile
[INFO]    |  +- org.apache.pdfbox:pdfbox:jar:2.0.22:compile
[INFO]    |  |  \- org.apache.pdfbox:fontbox:jar:2.0.22:compile
[INFO]    |  +- org.apache.pdfbox:xmpbox:jar:2.0.22:compile
[INFO]    |  +- com.openhtmltopdf:openhtmltopdf-core:jar:1.0.6:compile
[INFO]    |  \- de.rototor.pdfbox:graphics2d:jar:0.30:compile
[INFO]    +- com.phonepe.growth:hawkeye-model:jar:1.2.244:compile
[INFO]    +- com.phonepe.growth:hawkeye-common:jar:1.2.244:compile
[INFO]    +- com.phonepe.growth:hawkeye-client:jar:1.2.244:compile
[INFO]    \- com.phonepe.platform:db-sandbox-bundle:jar:1.0.3:compile
[INFO]       +- com.phonepe.platform:db-sandbox-model:jar:1.0.3:compile
[INFO]       \- com.phonepe.platform:db-sandbox-core:jar:1.0.3:compile
[INFO]          +- org.liquibase:liquibase-core:jar:3.6.3:compile
[INFO]          +- org.jgrapht:jgrapht-core:jar:1.4.0:compile
[INFO]          |  \- org.jheaps:jheaps:jar:0.11:compile
[INFO]          +- org.jooq:jooq:jar:3.14.7:compile
[INFO]          |  \- org.reactivestreams:reactive-streams:jar:1.0.2:compile
[INFO]          +- org.jooq:jooq-meta:jar:3.14.7:compile
[INFO]          \- org.jooq:jooq-codegen:jar:3.14.7:compile
[INFO] 
[INFO] ------------< com.phonepe.merchant.platform:stratos-server >------------
[INFO] Building stratos-server 2.0.67-SNAPSHOT                            [3/3]
[INFO]   from stratos-server/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[WARNING] commons-io:commons-io/maven-metadata.xml failed to transfer from http://0.0.0.0/ during a previous attempt. This failure was cached in the local repository and resolution will not be reattempted until the update interval of maven-default-http-blocker has elapsed or updates are forced. Original error: Could not transfer metadata commons-io:commons-io/maven-metadata.xml from/to maven-default-http-blocker (http://0.0.0.0/): Blocked mirror for repositories: [phonepe-releases (http://artifactory.phonepe.com/content/repositories/releases, default, releases+snapshots), phonepe-snapshots (http://artifactory.phonepe.com/content/repositories/snapshots, default, releases+snapshots)]
[WARNING] commons-io:commons-io/maven-metadata.xml failed to transfer from http://0.0.0.0/ during a previous attempt. This failure was cached in the local repository and resolution will not be reattempted until the update interval of maven-default-http-blocker has elapsed or updates are forced. Original error: Could not transfer metadata commons-io:commons-io/maven-metadata.xml from/to maven-default-http-blocker (http://0.0.0.0/): Blocked mirror for repositories: [phonepe-releases (http://artifactory.phonepe.com/content/repositories/releases, default, releases+snapshots), phonepe-snapshots (http://artifactory.phonepe.com/content/repositories/snapshots, default, releases+snapshots)]
[WARNING] commons-io:commons-io/maven-metadata.xml failed to transfer from http://0.0.0.0/ during a previous attempt. This failure was cached in the local repository and resolution will not be reattempted until the update interval of maven-default-http-blocker has elapsed or updates are forced. Original error: Could not transfer metadata commons-io:commons-io/maven-metadata.xml from/to maven-default-http-blocker (http://0.0.0.0/): Blocked mirror for repositories: [phonepe-releases (http://artifactory.phonepe.com/content/repositories/releases, default, releases+snapshots), phonepe-snapshots (http://artifactory.phonepe.com/content/repositories/snapshots, default, releases+snapshots)]
[WARNING] The POM for com.phonepe.dataplatform:kratos-stratos-model:jar:0.3-PROD is invalid, transitive dependencies (if any) will not be available: 2 problems were encountered while building the effective model for com.phonepe.dataplatform:kratos-stratos-model:
[ERROR] Resolving expression: '${project.version}': Detected the following recursive expression cycle in 'project.version': [version] @ com.phonepe.dataplatform:kratos-stratos-model:${project.version}
[ERROR] 'version' is missing. @ com.phonepe.dataplatform:kratos-stratos-model:[unknown-version]

[WARNING] commons-io:commons-io/maven-metadata.xml failed to transfer from http://0.0.0.0/ during a previous attempt. This failure was cached in the local repository and resolution will not be reattempted until the update interval of maven-default-http-blocker has elapsed or updates are forced. Original error: Could not transfer metadata commons-io:commons-io/maven-metadata.xml from/to maven-default-http-blocker (http://0.0.0.0/): Blocked mirror for repositories: [phonepe-releases (http://artifactory.phonepe.com/content/repositories/releases, default, releases+snapshots), phonepe-snapshots (http://artifactory.phonepe.com/content/repositories/snapshots, default, releases+snapshots)]
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ stratos-server ---
[WARNING] commons-io:commons-io/maven-metadata.xml failed to transfer from http://0.0.0.0/ during a previous attempt. This failure was cached in the local repository and resolution will not be reattempted until the update interval of maven-default-http-blocker has elapsed or updates are forced. Original error: Could not transfer metadata commons-io:commons-io/maven-metadata.xml from/to maven-default-http-blocker (http://0.0.0.0/): Blocked mirror for repositories: [phonepe-releases (http://artifactory.phonepe.com/content/repositories/releases, default, releases+snapshots), phonepe-snapshots (http://artifactory.phonepe.com/content/repositories/snapshots, default, releases+snapshots)]
[WARNING] commons-io:commons-io/maven-metadata.xml failed to transfer from http://0.0.0.0/ during a previous attempt. This failure was cached in the local repository and resolution will not be reattempted until the update interval of maven-default-http-blocker has elapsed or updates are forced. Original error: Could not transfer metadata commons-io:commons-io/maven-metadata.xml from/to maven-default-http-blocker (http://0.0.0.0/): Blocked mirror for repositories: [phonepe-releases (http://artifactory.phonepe.com/content/repositories/releases, default, releases+snapshots), phonepe-snapshots (http://artifactory.phonepe.com/content/repositories/snapshots, default, releases+snapshots)]
[WARNING] commons-io:commons-io/maven-metadata.xml failed to transfer from http://0.0.0.0/ during a previous attempt. This failure was cached in the local repository and resolution will not be reattempted until the update interval of maven-default-http-blocker has elapsed or updates are forced. Original error: Could not transfer metadata commons-io:commons-io/maven-metadata.xml from/to maven-default-http-blocker (http://0.0.0.0/): Blocked mirror for repositories: [phonepe-releases (http://artifactory.phonepe.com/content/repositories/releases, default, releases+snapshots), phonepe-snapshots (http://artifactory.phonepe.com/content/repositories/snapshots, default, releases+snapshots)]
[WARNING] commons-io:commons-io/maven-metadata.xml failed to transfer from http://0.0.0.0/ during a previous attempt. This failure was cached in the local repository and resolution will not be reattempted until the update interval of maven-default-http-blocker has elapsed or updates are forced. Original error: Could not transfer metadata commons-io:commons-io/maven-metadata.xml from/to maven-default-http-blocker (http://0.0.0.0/): Blocked mirror for repositories: [phonepe-releases (http://artifactory.phonepe.com/content/repositories/releases, default, releases+snapshots), phonepe-snapshots (http://artifactory.phonepe.com/content/repositories/snapshots, default, releases+snapshots)]
[INFO] com.phonepe.merchant.platform:stratos-server:jar:2.0.67-SNAPSHOT
[INFO] +- com.phonepe.merchant.platform:stratos-models:jar:2.0.67-SNAPSHOT:compile
[INFO] |  +- javax.validation:validation-api:jar:2.0.1.Final:compile
[INFO] |  +- org.hibernate.validator:hibernate-validator:jar:6.1.7.Final:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.12.1:compile
[INFO] |  +- com.phonepe:error-configurator:jar:1.3:compile
[INFO] |  |  \- org.apache.commons:commons-collections4:jar:4.4:compile
[INFO] |  +- com.phonepe.services:crixus-core:jar:1.0.42:compile
[INFO] |  |  +- com.phonepe.services:zapier:jar:1.0:compile
[INFO] |  |  +- com.flipkart.databuilderframework:databuilderframework:jar:0.5.11:compile
[INFO] |  |  +- com.phonepe.payments:databuilder-execgraph-plugin:jar:1.0.7:compile
[INFO] |  |  |  +- org.apache.maven:maven-core:jar:3.5.4:compile
[INFO] |  |  |  |  +- org.apache.maven:maven-settings-builder:jar:3.5.4:compile
[INFO] |  |  |  |  |  \- org.sonatype.plexus:plexus-sec-dispatcher:jar:1.4:compile
[INFO] |  |  |  |  |     \- org.sonatype.plexus:plexus-cipher:jar:1.4:compile
[INFO] |  |  |  |  +- org.apache.maven:maven-builder-support:jar:3.5.4:compile
[INFO] |  |  |  |  +- org.apache.maven:maven-repository-metadata:jar:3.5.4:compile
[INFO] |  |  |  |  +- org.apache.maven:maven-model-builder:jar:3.5.4:compile
[INFO] |  |  |  |  +- org.apache.maven:maven-resolver-provider:jar:3.5.4:compile
[INFO] |  |  |  |  +- org.apache.maven.resolver:maven-resolver-impl:jar:1.1.1:compile
[INFO] |  |  |  |  +- org.apache.maven.resolver:maven-resolver-api:jar:1.1.1:compile
[INFO] |  |  |  |  +- org.apache.maven.resolver:maven-resolver-spi:jar:1.1.1:compile
[INFO] |  |  |  |  +- org.apache.maven.resolver:maven-resolver-util:jar:1.1.1:compile
[INFO] |  |  |  |  +- org.apache.maven.shared:maven-shared-utils:jar:3.2.1:compile
[INFO] |  |  |  |  +- org.eclipse.sisu:org.eclipse.sisu.inject:jar:0.3.3:compile
[INFO] |  |  |  |  \- org.codehaus.plexus:plexus-component-annotations:jar:1.7.1:compile
[INFO] |  |  |  \- com.google.code.gson:gson:jar:2.8.2:compile
[INFO] |  |  \- com.phonepe.services:circumscribe-aerospike:jar:1.0.1:compile
[INFO] |  |     \- com.phonepe.services:circumscribe-commons:jar:1.0.1:compile
[INFO] |  \- com.phonepe.services:crixus-core-models:jar:1.0.42:compile
[INFO] |     \- dom4j:dom4j:jar:1.1:compile
[INFO] +- io.hypersistence:hypersistence-utils-hibernate-55:jar:3.2.0:compile
[INFO] |  \- io.hypersistence:hypersistence-tsid:jar:2.0.0:compile
[INFO] +- org.projectlombok:lombok:jar:1.18.22:compile
[INFO] +- com.google.guava:guava:jar:30.1.1-jre:compile
[INFO] |  +- com.google.guava:failureaccess:jar:1.0.1:compile
[INFO] |  +- com.google.guava:listenablefuture:jar:9999.0-empty-to-avoid-conflict-with-guava:compile
[INFO] |  +- com.google.code.findbugs:jsr305:jar:3.0.2:compile
[INFO] |  +- org.checkerframework:checker-qual:jar:3.8.0:compile
[INFO] |  +- com.google.errorprone:error_prone_annotations:jar:2.5.1:compile
[INFO] |  \- com.google.j2objc:j2objc-annotations:jar:1.3:compile
[INFO] +- org.reflections:reflections:jar:0.9.11:compile
[INFO] |  \- org.javassist:javassist:jar:3.28.0-GA:compile
[INFO] +- com.fasterxml.jackson.core:jackson-databind:jar:2.12.1:compile
[INFO] |  \- com.fasterxml.jackson.core:jackson-core:jar:2.12.1:compile
[INFO] +- io.dropwizard:dropwizard-jackson:jar:2.0.23:compile
[INFO] |  +- com.github.ben-manes.caffeine:caffeine:jar:2.9.1:compile
[INFO] |  +- io.dropwizard:dropwizard-util:jar:2.0.23:compile
[INFO] |  +- com.fasterxml.jackson.datatype:jackson-datatype-guava:jar:2.10.5:compile
[INFO] |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.10.5:compile
[INFO] |  +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.10.5:compile
[INFO] |  +- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.10.5:compile
[INFO] |  +- com.fasterxml.jackson.module:jackson-module-afterburner:jar:2.10.5:compile
[INFO] |  +- com.fasterxml.jackson.datatype:jackson-datatype-joda:jar:2.10.5:compile
[INFO] |  \- org.slf4j:slf4j-api:jar:1.7.31:compile
[INFO] +- io.dropwizard:dropwizard-core:jar:2.0.23:compile
[INFO] |  +- io.dropwizard:dropwizard-configuration:jar:2.0.23:compile
[INFO] |  |  \- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.10.5:compile
[INFO] |  |     \- org.yaml:snakeyaml:jar:1.26:compile
[INFO] |  +- io.dropwizard:dropwizard-logging:jar:2.0.23:compile
[INFO] |  |  +- io.dropwizard.metrics:metrics-logback:jar:4.1.23:compile
[INFO] |  |  +- ch.qos.logback:logback-core:jar:1.2.3:compile
[INFO] |  |  +- io.dropwizard.logback:logback-throttling-appender:jar:1.1.0:compile
[INFO] |  |  \- org.slf4j:log4j-over-slf4j:jar:1.7.31:runtime
[INFO] |  +- io.dropwizard:dropwizard-jersey:jar:2.0.23:compile
[INFO] |  |  +- org.glassfish.jersey.ext:jersey-metainf-services:jar:2.33:runtime
[INFO] |  |  +- org.glassfish.jersey.inject:jersey-hk2:jar:2.33:runtime
[INFO] |  |  |  \- org.glassfish.hk2:hk2-locator:jar:2.6.1:runtime
[INFO] |  |  +- io.dropwizard.metrics:metrics-jersey2:jar:4.1.23:compile
[INFO] |  |  +- jakarta.annotation:jakarta.annotation-api:jar:1.3.5:compile
[INFO] |  |  +- org.glassfish.hk2:hk2-api:jar:2.6.1:compile
[INFO] |  |  |  +- org.glassfish.hk2:hk2-utils:jar:2.6.1:compile
[INFO] |  |  |  \- org.glassfish.hk2.external:aopalliance-repackaged:jar:2.6.1:compile
[INFO] |  |  \- org.glassfish.jersey.containers:jersey-container-servlet:jar:2.33:runtime
[INFO] |  +- io.dropwizard:dropwizard-servlets:jar:2.0.23:compile
[INFO] |  +- io.dropwizard:dropwizard-jetty:jar:2.0.23:compile
[INFO] |  |  +- org.eclipse.jetty:jetty-servlets:jar:9.4.42.v20210604:compile
[INFO] |  |  |  \- org.eclipse.jetty:jetty-continuation:jar:9.4.42.v20210604:compile
[INFO] |  |  \- org.eclipse.jetty:jetty-http:jar:9.4.42.v20210604:compile
[INFO] |  +- io.dropwizard:dropwizard-lifecycle:jar:2.0.23:compile
[INFO] |  +- io.dropwizard.metrics:metrics-core:jar:4.1.23:compile
[INFO] |  +- io.dropwizard.metrics:metrics-jetty9:jar:4.1.23:compile
[INFO] |  +- io.dropwizard.metrics:metrics-jvm:jar:4.1.23:compile
[INFO] |  +- io.dropwizard.metrics:metrics-jmx:jar:4.1.23:compile
[INFO] |  +- io.dropwizard.metrics:metrics-servlets:jar:4.1.23:compile
[INFO] |  |  +- io.dropwizard.metrics:metrics-json:jar:4.1.23:compile
[INFO] |  |  \- com.helger:profiler:jar:1.1.1:compile
[INFO] |  +- io.dropwizard.metrics:metrics-healthchecks:jar:4.1.23:compile
[INFO] |  +- io.dropwizard:dropwizard-request-logging:jar:2.0.23:compile
[INFO] |  |  \- ch.qos.logback:logback-access:jar:1.2.3:compile
[INFO] |  +- ch.qos.logback:logback-classic:jar:1.2.3:compile
[INFO] |  +- jakarta.servlet:jakarta.servlet-api:jar:4.0.4:compile
[INFO] |  +- jakarta.validation:jakarta.validation-api:jar:2.0.2:compile
[INFO] |  +- jakarta.ws.rs:jakarta.ws.rs-api:jar:2.1.6:compile
[INFO] |  +- net.sourceforge.argparse4j:argparse4j:jar:0.8.1:compile
[INFO] |  +- org.eclipse.jetty:jetty-security:jar:9.4.42.v20210604:compile
[INFO] |  +- org.eclipse.jetty:jetty-server:jar:9.4.42.v20210604:compile
[INFO] |  +- org.eclipse.jetty:jetty-servlet:jar:9.4.42.v20210604:compile
[INFO] |  |  \- org.eclipse.jetty:jetty-util-ajax:jar:9.4.42.v20210604:compile
[INFO] |  +- org.eclipse.jetty:jetty-util:jar:9.4.42.v20210604:compile
[INFO] |  +- org.eclipse.jetty.toolchain.setuid:jetty-setuid-java:jar:1.0.4:compile
[INFO] |  +- org.glassfish.hk2.external:jakarta.inject:jar:2.6.1:compile
[INFO] |  +- org.glassfish.jersey.core:jersey-common:jar:2.33:compile
[INFO] |  |  \- org.glassfish.hk2:osgi-resource-locator:jar:1.0.3:compile
[INFO] |  \- org.glassfish.jersey.ext:jersey-bean-validation:jar:2.33:compile
[INFO] +- net.java.dev.jna:jna:jar:5.7.0:test
[INFO] +- io.dropwizard:dropwizard-validation:jar:2.0.23:compile
[INFO] |  +- com.fasterxml:classmate:jar:1.5.1:compile
[INFO] |  \- org.glassfish:jakarta.el:jar:3.0.3:compile
[INFO] +- io.dropwizard:dropwizard-metrics:jar:2.0.23:compile
[INFO] +- io.dropwizard:dropwizard-forms:jar:2.0.23:compile
[INFO] |  \- org.glassfish.jersey.media:jersey-media-multipart:jar:2.33:compile
[INFO] |     \- org.jvnet.mimepull:mimepull:jar:1.9.13:compile
[INFO] +- io.dropwizard:dropwizard-testing:jar:2.0.23:test
[INFO] |  +- io.dropwizard.metrics:metrics-annotation:jar:4.1.23:compile
[INFO] |  +- com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider:jar:2.10.5:compile
[INFO] |  |  +- com.fasterxml.jackson.jaxrs:jackson-jaxrs-base:jar:2.10.5:compile
[INFO] |  |  \- com.fasterxml.jackson.module:jackson-module-jaxb-annotations:jar:2.10.5:compile
[INFO] |  +- org.eclipse.jetty:jetty-io:jar:9.4.42.v20210604:compile
[INFO] |  +- org.glassfish.jersey.containers:jersey-container-servlet-core:jar:2.33:compile
[INFO] |  +- org.glassfish.jersey.core:jersey-client:jar:2.33:compile
[INFO] |  +- org.glassfish.jersey.core:jersey-server:jar:2.33:compile
[INFO] |  +- org.glassfish.jersey.test-framework:jersey-test-framework-core:jar:2.33:test
[INFO] |  |  \- org.glassfish.jersey.media:jersey-media-jaxb:jar:2.33:test
[INFO] |  +- jakarta.activation:jakarta.activation-api:jar:1.2.2:compile
[INFO] |  \- jakarta.xml.bind:jakarta.xml.bind-api:jar:2.3.3:compile
[INFO] +- ru.vyarus:dropwizard-guicey:jar:5.4.0:compile
[INFO] |  +- com.google.inject:guice:jar:5.0.1:compile
[INFO] |  |  +- javax.inject:javax.inject:jar:1:compile
[INFO] |  |  \- aopalliance:aopalliance:jar:1.0:compile
[INFO] |  +- com.google.inject.extensions:guice-servlet:jar:5.0.1:compile
[INFO] |  \- ru.vyarus:generics-resolver:jar:3.0.3:compile
[INFO] +- io.appform.ranger:ranger-discovery-bundle:jar:1.1.1:compile
[INFO] |  +- dev.failsafe:failsafe:jar:3.2.4:compile
[INFO] |  +- io.appform.ranger:ranger-zk-client:jar:1.1.1:compile
[INFO] |  |  \- io.appform.ranger:ranger-zookeeper:jar:1.1.1:compile
[INFO] |  +- io.appform.ranger:ranger-server-common:jar:1.1.1:compile
[INFO] |  +- com.google.code.findbugs:annotations:jar:3.0.1u2:compile
[INFO] |  |  \- net.jcip:jcip-annotations:jar:1.0:compile
[INFO] |  \- com.github.rholder:guava-retrying:jar:2.0.0:compile
[INFO] +- com.phonepe.platform.http.v2:http-feign:jar:4.0.44:compile
[INFO] |  +- com.phonepe.platform.http.v2:http-common:jar:4.0.44:compile
[INFO] |  +- com.phonepe.platform.http.v2:http-discovery:jar:4.0.44:compile
[INFO] |  |  +- com.phonepe.platform:zeus-provider:jar:2.0.73:compile
[INFO] |  |  +- com.phonepe.platform:zeus-client:jar:2.0.73:compile
[INFO] |  |  +- io.appform.ranger:ranger-http-client:jar:1.1.1:compile
[INFO] |  |  |  +- io.appform.ranger:ranger-http:jar:1.1.1:compile
[INFO] |  |  |  |  +- io.appform.ranger:ranger-core:jar:1.1.1:compile
[INFO] |  |  |  |  +- io.appform.ranger:ranger-http-model:jar:1.1.1:compile
[INFO] |  |  |  |  \- io.appform.ranger:ranger-core:test-jar:tests:1.1.1:compile
[INFO] |  |  |  \- io.appform.ranger:ranger-client:jar:1.1.1:compile
[INFO] |  |  \- org.apache.curator:curator-recipes:jar:4.2.0:compile
[INFO] |  +- com.phonepe.platform.http.v2:http-client:jar:4.0.44:compile
[INFO] |  |  +- com.phonepe.platform.http.v2:http-tracing:jar:4.0.44:compile
[INFO] |  |  |  \- io.opentracing.contrib:opentracing-okhttp3:jar:3.0.0:compile
[INFO] |  |  |     \- io.opentracing.contrib:opentracing-concurrent:jar:0.4.0:compile
[INFO] |  |  +- com.phonepe.platform.http.v2:http-spyglass:jar:4.0.44:compile
[INFO] |  |  \- com.phonepe.platform.http.v2:http-common:test-jar:tests:4.0.44:compile
[INFO] |  +- com.phonepe.platform:zeus-router-registry:jar:2.0.73:compile
[INFO] |  |  \- org.oxerr.commons:commons-jax-rs-exception-mapper:jar:2.3.1:compile
[INFO] |  \- org.bouncycastle:bcprov-jdk15on:jar:1.70:compile
[INFO] +- com.vladmihalcea:hibernate-types-52:jar:2.21.1:compile
[INFO] +- com.phonepe.dataplatform:kratos-client:jar:0.12.8:compile
[INFO] |  +- com.phonepe.dataplatform:kratos-model:jar:0.12.8:compile
[INFO] |  |  \- com.jayway.jsonpath:json-path:jar:2.7.0:compile
[INFO] |  +- com.phonepe.dataplatform:kratos-reporter:jar:0.12.8:compile
[INFO] |  +- com.phonepe.platform:sentinel-client:jar:1.161:compile
[INFO] |  |  \- com.squareup.okio:okio:jar:2.2.2:compile
[INFO] |  +- com.phonepe.user:userservice-model:jar:2.0.71:compile
[INFO] |  +- org.apache.commons:commons-math3:jar:3.6.1:compile
[INFO] |  +- org.slf4j:slf4j-simple:jar:1.7.25:compile
[INFO] |  +- io.raven.dropwizard:dropwizard-riemann:jar:2.0.21-1:compile
[INFO] |  |  \- io.riemann:metrics3-riemann-reporter:jar:0.5.1:compile
[INFO] |  |     \- io.riemann:riemann-java-client:jar:0.5.1:compile
[INFO] |  |        +- com.google.protobuf:protobuf-java:jar:3.7.1:compile
[INFO] |  |        +- io.netty:netty-codec:jar:4.1.28.Final:compile
[INFO] |  |        +- io.netty:netty-handler:jar:4.1.28.Final:compile
[INFO] |  |        |  \- io.netty:netty-buffer:jar:4.1.28.Final:compile
[INFO] |  |        |     \- io.netty:netty-common:jar:4.1.28.Final:compile
[INFO] |  |        \- io.netty:netty-transport:jar:4.1.28.Final:compile
[INFO] |  |           \- io.netty:netty-resolver:jar:4.1.28.Final:compile
[INFO] |  +- io.appform.rules:json-rules:jar:1.0.14:compile
[INFO] |  +- com.phonepe.platform:spyglass-core:jar:2.0.21:compile
[INFO] |  +- io.appform.opentracing.annotations:opentracing-annotations:jar:1.0.2:compile
[INFO] |  |  +- io.opentracing:opentracing-api:jar:0.33.0:compile
[INFO] |  |  \- io.opentracing:opentracing-util:jar:0.33.0:compile
[INFO] |  |     \- io.opentracing:opentracing-noop:jar:0.33.0:compile
[INFO] |  \- com.phonepe.platform:spyglass-common:jar:2.0.21:compile
[INFO] +- com.phonepe.dataplatform:profile-model:jar:1.7.36:compile
[INFO] +- com.phonepe.platform:zeus-models:jar:2.0.73:compile
[INFO] |  +- io.swagger.core.v3:swagger-core:jar:2.0.10:compile
[INFO] |  |  +- javax.xml.bind:jaxb-api:jar:2.3.0:compile
[INFO] |  |  +- io.swagger.core.v3:swagger-annotations:jar:2.0.10:compile
[INFO] |  |  \- io.swagger.core.v3:swagger-models:jar:2.0.10:compile
[INFO] |  +- javax.ws.rs:javax.ws.rs-api:jar:2.1.1:compile
[INFO] |  \- com.github.koushikr:fsm:jar:1.0.2:compile
[INFO] +- com.phonepe.services.warden:warden-models:jar:2.0.3:compile
[INFO] |  +- com.phonepe.services:warden-core-models:jar:2.0.3:compile
[INFO] |  |  +- com.phonepe.services:warden-workflow-models:jar:2.0.3:compile
[INFO] |  |  \- com.phonepe.services:warden-workflow:jar:2.0.3:compile
[INFO] |  +- com.squareup.okhttp3:okhttp:jar:4.11.0-PPE:compile
[INFO] |  |  +- com.squareup.okio:okio-jvm:jar:3.0.0:compile
[INFO] |  |  |  +- org.jetbrains.kotlin:kotlin-stdlib-jdk8:jar:1.5.31:compile
[INFO] |  |  |  |  \- org.jetbrains.kotlin:kotlin-stdlib-jdk7:jar:1.5.31:compile
[INFO] |  |  |  \- org.jetbrains.kotlin:kotlin-stdlib-common:jar:1.5.31:compile
[INFO] |  |  \- org.jetbrains.kotlin:kotlin-stdlib:jar:1.6.20:compile
[INFO] |  |     \- org.jetbrains:annotations:jar:13.0:compile
[INFO] |  +- io.swagger:swagger-jersey2-jaxrs:jar:1.6.5:compile
[INFO] |  +- io.swagger:swagger-annotations:jar:1.6.5:compile
[INFO] |  +- com.phonepe.services:guardian-models:jar:1.1.19:compile
[INFO] |  +- io.raven.dropwizard:dropwizard-jmxmp:jar:2.0.21-1:compile
[INFO] |  |  \- org.glassfish.main.external:jmxremote_optional-repackaged:jar:5.0:compile
[INFO] |  +- com.phonepe.merchants.platform:varys:jar:1.0.6:compile
[INFO] |  |  \- com.sun.mail:javax.mail:jar:1.5.5:compile
[INFO] |  |     \- javax.activation:activation:jar:1.1:compile
[INFO] |  +- com.raskasa.metrics:metrics-okhttp:jar:0.5.5-PP:compile
[INFO] |  \- org.apache.httpcomponents:httpclient:jar:4.5.13:compile
[INFO] |     +- org.apache.httpcomponents:httpcore:jar:4.4.13:compile
[INFO] |     +- commons-logging:commons-logging:jar:1.2:compile
[INFO] |     \- commons-codec:commons-codec:jar:1.11:compile
[INFO] +- com.hystrix:hystrix-configurator:jar:0.0.8:compile
[INFO] +- com.phonepe.dataplatform:kratos-stratos-model:jar:0.3-PROD:compile
[INFO] +- com.phonepe.platform:requestinfo-bundle:jar:2.0.23-36:compile
[INFO] |  +- com.phonepe.platform:requestinfo-core:jar:2.0.23-36:compile
[INFO] |  \- com.phonepe.platform:atlas-client:jar:2.1.143:compile
[INFO] |     +- com.phonepe.dataplatform:mvel-common-functions:jar:0.1.15:compile
[INFO] |     |  \- com.github.davidmoten:geo:jar:0.7.1:compile
[INFO] |     |     \- com.github.davidmoten:grumpy-core:jar:0.2.2:compile
[INFO] |     \- io.sgr:s2-geometry-library-java:jar:1.0.0:compile
[INFO] +- com.phonepe.platform:metric-ingestion-bundle:jar:1.80:compile
[INFO] |  +- com.phonepe.platform:metric-ingestion-models:jar:1.80:compile
[INFO] |  +- com.phonepe.platform:metric-ingestion-client:jar:1.80:compile
[INFO] |  |  \- com.squareup.okhttp3:logging-interceptor:jar:4.9.0:compile
[INFO] |  \- io.micrometer:micrometer-core:jar:1.11.5:compile
[INFO] |     +- io.micrometer:micrometer-commons:jar:1.11.5:compile
[INFO] |     +- io.micrometer:micrometer-observation:jar:1.11.5:compile
[INFO] |     \- org.latencyutils:LatencyUtils:jar:2.0.3:runtime
[INFO] +- io.raven.dropwizard:dropwizard-oor:jar:2.0.24-1:compile
[INFO] +- in.vectorpro.dropwizard:dropwizard-swagger:jar:2.0.23-1:compile
[INFO] |  +- io.dropwizard:dropwizard-views:jar:2.0.23:compile
[INFO] |  +- io.dropwizard:dropwizard-assets:jar:2.0.23:compile
[INFO] |  +- io.dropwizard:dropwizard-auth:jar:2.0.23:compile
[INFO] |  +- io.dropwizard:dropwizard-views-freemarker:jar:2.0.23:compile
[INFO] |  |  \- org.freemarker:freemarker:jar:2.3.31:compile
[INFO] |  +- in.vectorpro:dropwizard-swagger-ui:jar:3.51.1:compile
[INFO] |  \- io.swagger.core.v3:swagger-jaxrs2:jar:2.0.10:compile
[INFO] |     \- io.swagger.core.v3:swagger-integration:jar:2.0.10:compile
[INFO] +- io.dropwizard:dropwizard-hibernate:jar:2.0.23:compile
[INFO] |  +- io.dropwizard:dropwizard-db:jar:2.0.23:compile
[INFO] |  +- com.fasterxml.jackson.datatype:jackson-datatype-hibernate5:jar:2.10.5:compile
[INFO] |  |  \- javax.transaction:javax.transaction-api:jar:1.3:compile
[INFO] |  +- javax.persistence:javax.persistence-api:jar:2.2:compile
[INFO] |  +- joda-time:joda-time:jar:2.10.10:compile
[INFO] |  +- org.apache.tomcat:tomcat-***********************
[INFO] |  |  \- org.apache.tomcat:tomcat-juli:jar:9.0.48:compile
[INFO] |  +- org.jadira.usertype:usertype.core:jar:7.0.0.CR1:runtime
[INFO] |  |  \- org.jadira.usertype:usertype.spi:jar:7.0.0.CR1:runtime
[INFO] |  \- org.hibernate:hibernate-core:jar:5.4.30.Final:compile
[INFO] |     +- antlr:antlr:jar:2.7.7:compile
[INFO] |     +- org.jboss.spec.javax.transaction:jboss-transaction-api_1.2_spec:jar:1.1.1.Final:compile
[INFO] |     +- org.jboss:jandex:jar:2.2.3.Final:compile
[INFO] |     +- org.dom4j:dom4j:jar:2.1.3:compile
[INFO] |     +- org.hibernate.common:hibernate-commons-annotations:jar:5.1.2.Final:compile
[INFO] |     \- org.glassfish.jaxb:jaxb-runtime:jar:2.3.1:compile
[INFO] |        +- org.glassfish.jaxb:txw2:jar:2.3.1:compile
[INFO] |        +- com.sun.istack:istack-commons-runtime:jar:3.0.7:compile
[INFO] |        +- org.jvnet.staxex:stax-ex:jar:1.8:compile
[INFO] |        \- com.sun.xml.fastinfoset:FastInfoset:jar:1.2.15:compile
[INFO] +- org.hibernate:hibernate-envers:jar:5.4.30.Final:compile
[INFO] +- io.appform.dropwizard.sharding:db-sharding-bundle:jar:2.0.28-9:compile
[INFO] |  \- cglib:cglib-nodep:jar:3.3.0:compile
[INFO] +- com.phonepe.data.provider:rosey-data-provider-bundle:jar:2.15:compile
[INFO] |  +- com.phonepe.platform:rosey-standanlone-config:jar:2.0.84:compile
[INFO] |  |  \- com.fasterxml.jackson.dataformat:jackson-dataformat-xml:jar:2.10.1:compile
[INFO] |  |     +- org.codehaus.woodstox:stax2-api:jar:4.2:compile
[INFO] |  |     \- com.fasterxml.woodstox:woodstox-core:jar:6.0.2:compile
[INFO] |  \- com.phonepe.data.provider:data-provider-core:jar:2.15:compile
[INFO] +- com.phonepe.platform:tstore-client-bundle:jar:5.3.28:compile
[INFO] |  +- com.phonepe.platform:tstore-client:jar:5.3.28:compile
[INFO] |  |  +- org.apache.avro:avro:jar:1.8.3-ppe-9.10:compile
[INFO] |  |  |  +- org.codehaus.jackson:jackson-core-asl:jar:1.9.13:compile
[INFO] |  |  |  +- org.codehaus.jackson:jackson-mapper-asl:jar:1.9.13:compile
[INFO] |  |  |  +- com.thoughtworks.paranamer:paranamer:jar:2.7:compile
[INFO] |  |  |  +- org.xerial.snappy:snappy-java:jar:1.1.1.3:compile
[INFO] |  |  |  \- org.tukaani:xz:jar:1.5:compile
[INFO] |  |  +- com.phonepe.platform:schema-client:jar:3.1.5:compile
[INFO] |  |  |  +- com.phonepe.platform:schema-core:jar:3.1.5:compile
[INFO] |  |  |  |  \- com.phonepe.platform:serde-core:jar:3.1.5:compile
[INFO] |  |  |  \- com.phonepe.platform:schema-client-async:jar:3.1.5:compile
[INFO] |  |  |     \- io.vertx:vertx-web-client:jar:3.8.5:compile
[INFO] |  |  |        +- io.vertx:vertx-web-common:jar:3.8.5:compile
[INFO] |  |  |        \- io.vertx:vertx-core:jar:3.8.5:compile
[INFO] |  |  +- org.apache.avro:avro-compiler:jar:1.8.3-ppe-9.10:compile
[INFO] |  |  |  \- org.apache.velocity:velocity:jar:1.7:compile
[INFO] |  |  \- com.phonepe.platform:scroll-model:jar:5.3.28:compile
[INFO] |  \- com.phonepe.platform:scroll-entities-base:jar:5.3.28:compile
[INFO] +- com.phonepe.growth:zencast-model:jar:1.1.25:compile
[INFO] |  +- commons-lang:commons-lang:jar:2.6:compile
[INFO] |  +- commons-validator:commons-validator:jar:1.7:compile
[INFO] |  |  +- commons-beanutils:commons-beanutils:jar:1.9.4:compile
[INFO] |  |  \- commons-digester:commons-digester:jar:2.1:compile
[INFO] |  +- com.googlecode.libphonenumber:libphonenumber:jar:8.12.10:compile
[INFO] |  +- org.apache.commons:commons-text:jar:1.8:compile
[INFO] |  +- org.glassfish.jersey.bundles.repackaged:jersey-guava:jar:2.6:compile
[INFO] |  \- com.phonepe.platform:validation-bundle:jar:********:compile
[INFO] +- io.appform.dropwizard.actors:dropwizard-rabbitmq-actors:jar:2.0.23-4:compile
[INFO] |  +- org.junit.jupiter:junit-jupiter-api:jar:5.7.1:test
[INFO] |  |  +- org.opentest4j:opentest4j:jar:1.2.0:compile
[INFO] |  |  \- org.junit.platform:junit-platform-commons:jar:1.7.1:compile
[INFO] |  \- org.junit.vintage:junit-vintage-engine:jar:5.8.1:compile
[INFO] +- com.rabbitmq:amqp-client:jar:5.17.0:compile
[INFO] +- com.phonepe.platform:aerospike-bundle:jar:2.0.18-1:compile
[INFO] |  +- com.aerospike:aerospike-client:jar:4.4.18:compile
[INFO] |  |  +- org.gnu:gnu-crypto:jar:2.0.1:compile
[INFO] |  |  +- org.luaj:luaj-jse:jar:3.0:compile
[INFO] |  |  \- org.mindrot:jbcrypt:jar:0.4:compile
[INFO] |  \- com.github.sgoertzen:sonar-break-maven-plugin:jar:*******:compile
[INFO] |     +- org.apache.maven:maven-plugin-api:jar:3.5.2:compile
[INFO] |     |  +- org.apache.maven:maven-model:jar:3.5.2:compile
[INFO] |     |  +- org.eclipse.sisu:org.eclipse.sisu.plexus:jar:0.3.3:compile
[INFO] |     |  |  \- javax.enterprise:cdi-api:jar:1.0:compile
[INFO] |     |  |     \- javax.annotation:jsr250-api:jar:1.0:compile
[INFO] |     |  +- org.codehaus.plexus:plexus-utils:jar:3.1.0:compile
[INFO] |     |  \- org.codehaus.plexus:plexus-classworlds:jar:2.5.2:compile
[INFO] |     +- org.apache.maven:maven-project:jar:2.2.1:compile
[INFO] |     |  +- org.apache.maven:maven-settings:jar:2.2.1:compile
[INFO] |     |  +- org.apache.maven:maven-profile:jar:2.2.1:compile
[INFO] |     |  +- org.apache.maven:maven-artifact-manager:jar:2.2.1:compile
[INFO] |     |  |  +- org.apache.maven.wagon:wagon-provider-api:jar:1.0-beta-6:compile
[INFO] |     |  |  \- backport-util-concurrent:backport-util-concurrent:jar:3.1:compile
[INFO] |     |  +- org.apache.maven:maven-plugin-registry:jar:2.2.1:compile
[INFO] |     |  +- org.codehaus.plexus:plexus-interpolation:jar:1.11:compile
[INFO] |     |  \- org.codehaus.plexus:plexus-container-default:jar:1.0-alpha-9-stable-1:compile
[INFO] |     |     \- classworlds:classworlds:jar:1.1-alpha-2:compile
[INFO] |     \- org.apache.maven.plugin-tools:maven-plugin-annotations:jar:3.5:compile
[INFO] +- com.netflix.hystrix:hystrix-core:jar:1.5.18:compile
[INFO] |  +- com.netflix.archaius:archaius-core:jar:0.4.1:compile
[INFO] |  |  \- commons-configuration:commons-configuration:jar:1.8:compile
[INFO] |  +- io.reactivex:rxjava:jar:1.2.0:compile
[INFO] |  \- org.hdrhistogram:HdrHistogram:jar:2.1.9:compile
[INFO] +- io.appform.core:hystrix-function-wrapper:jar:1.0.0:compile
[INFO] |  \- junit:junit:jar:4.12:compile
[INFO] +- org.zapodot:hystrix-dropwizard-bundle:jar:0.4:compile
[INFO] |  +- com.netflix.hystrix:hystrix-metrics-event-stream:jar:1.4.21:compile
[INFO] |  \- com.netflix.hystrix:hystrix-codahale-metrics-publisher:jar:1.4.21:compile
[INFO] +- com.phonepe.models:phonepe-model:jar:2.1.570-STAGE-SNAPSHOT:compile
[INFO] |  +- com.phonepe.models:merchant-service-model:jar:1.3:compile
[INFO] |  +- com.phonepe.models:merchant-onboarding-model:jar:1.1:compile
[INFO] |  +- com.phonepe.merchant:khata-models:jar:1.4.5:compile
[INFO] |  +- com.phonepe.payments:mandate-model:jar:0.0.21:compile
[INFO] |  +- com.phonepe.payments:payment-model:jar:1.1.890-STAGE-SNAPSHOT:compile
[INFO] |  +- com.phonepe.payments:giftcard-provider-model:jar:1.0.7:compile
[INFO] |  +- com.phonepe.backend:app-service-models:jar:1.2.49:compile
[INFO] |  +- com.phonepe.payments:pg-transport-model:jar:0.1.126:compile
[INFO] |  |  \- com.phonepe.payments:vault-model:jar:0.0.92:compile
[INFO] |  +- com.phonepe.platform:sentinel-models:jar:1.119:compile
[INFO] |  |  +- com.phonepe.frontend:consent-models:jar:1.0.24:compile
[INFO] |  |  +- com.phonepe.data.selector:vmn-data-selector:jar:1.16:compile
[INFO] |  |  |  +- com.phonepe.data.selector:data-selector-core:jar:1.16:compile
[INFO] |  |  |  \- com.flipkart.foxtrot:foxtrot-common:jar:6.3.1-138:compile
[INFO] |  |  \- org.junit.jupiter:junit-jupiter-migrationsupport:jar:5.6.0:compile
[INFO] |  +- com.phonepe.services:checkout-model:jar:0.0.1:compile
[INFO] |  +- com.phonepe.payments:ybl-fundstransfer-model:jar:0.0.4:compile
[INFO] |  +- com.phonepe.fs:mutual-fund-models:jar:0.9.149:compile
[INFO] |  +- com.phonepe.dataplatform:fabric-alert-models:jar:1.2:compile
[INFO] |  +- com.phonepe.frontend:connection-id-core:jar:1.6:compile
[INFO] |  |  +- com.phonepe.frontend:connection-id-models:jar:1.6:compile
[INFO] |  |  \- com.phonepe.folios:folios-performance-evaluator:jar:1.4:compile
[INFO] |  +- com.phonepe.platform:phone-validator:jar:0.0.5:compile
[INFO] |  +- com.phonepe.services:discovery-models:jar:1.1.47-SNAPSHOT:compile
[INFO] |  +- com.phonepe.services:yeti-models:jar:1.0.0-stage-SNAPSHOT:compile
[INFO] |  +- com.phonepe.platform:brickbat-models:jar:1.1.21:compile
[INFO] |  \- com.phonepe.merchant:pixel-models:jar:1.0-SNAPSHOT:compile
[INFO] +- com.phonepe.payments:upi-client-model:jar:0.2.110:compile
[INFO] +- com.phonepe.platform.http.v2:http-client-all:jar:4.0.44:compile
[INFO] |  \- com.phonepe.platform.http.v2:http-executor:jar:4.0.44:compile
[INFO] +- com.phonepe.platform:docstore-common:jar:5.2.15:compile
[INFO] +- com.phonepe.olympus-im:olympus-im-client:jar:1.2.29:compile
[INFO] |  +- com.phonepe.olympus-im:olympus-im-core:jar:1.2.29:compile
[INFO] |  |  +- com.phonepe.olympus-im:olympus-im-models:jar:1.2.29:compile
[INFO] |  |  +- org.bitbucket.b_c:jose4j:jar:0.7.7:compile
[INFO] |  |  \- com.auth0:java-jwt:jar:3.3.0:compile
[INFO] |  +- com.phonepe.olympus-im:olympus-im-routing:jar:1.2.29:compile
[INFO] |  |  \- com.phonepe.platform:dropwizard-primer-bundle:jar:1.0.1:compile
[INFO] |  +- io.github.openfeign:feign-slf4j:jar:11.0:compile
[INFO] |  +- com.phonepe.platform:audit-logger-client:jar:1.4:compile
[INFO] |  |  \- com.phonepe.platform:audit-logger-models:jar:1.4:compile
[INFO] |  +- commons-collections:commons-collections:jar:3.2.2:compile
[INFO] |  +- com.fasterxml.uuid:java-uuid-generator:jar:3.1.4:compile
[INFO] |  +- com.google.api-client:google-api-client-java6:jar:1.24.1:compile
[INFO] |  |  +- com.google.api-client:google-api-client:jar:1.24.1:compile
[INFO] |  |  |  +- com.google.oauth-client:google-oauth-client:jar:1.24.1:compile
[INFO] |  |  |  |  \- com.google.http-client:google-http-client:jar:1.24.1:compile
[INFO] |  |  |  \- com.google.http-client:google-http-client-jackson2:jar:1.24.1:compile
[INFO] |  |  \- com.google.oauth-client:google-oauth-client-java6:jar:1.24.1:compile
[INFO] |  +- com.google.apis:google-api-services-oauth2:jar:v2-rev141-1.25.0:compile
[INFO] |  +- commons-io:commons-io:jar:2.15.0:compile
[INFO] |  +- io.appform.commonutils:common-utils:jar:1.4:compile
[INFO] |  |  +- org.functionaljava:functionaljava:jar:3.0:compile
[INFO] |  |  \- org.mongodb:bson:jar:2.3:compile
[INFO] |  \- com.phonepe.platform:eventbus-recipes:jar:2.4:compile
[INFO] |     \- com.slack.api:slack-api-client:jar:1.27.2:compile
[INFO] |        \- com.slack.api:slack-api-model:jar:1.27.2:compile
[INFO] +- com.phonepe.platform:rosey-dropwizard-config:jar:2.0.74:compile
[INFO] |  \- com.phonepe.platform:rosey-client-models:jar:2.0.74:compile
[INFO] +- com.phonepe.dataplatform:event-ingestion-client:jar:2.1.0-12-SNAPSHOT:compile
[INFO] |  +- org.clojars.nitishgoyal13:bigqueue:jar:0.7.8:compile
[INFO] |  +- com.phonepe.platform:event-ingestion-models:jar:2.0.102:compile
[INFO] |  +- com.phonepe.platform:checkmate-lib:jar:0.1.4:compile
[INFO] |  \- com.phonepe.platform:requestinfo-utils:jar:2.0.23-37:compile
[INFO] +- com.phonepe.platform.http.server.metrics:api-metrics:jar:0.0.11:compile
[INFO] +- com.phonepe.merchants.platform:primus-core:jar:1.35.1-SNAPSHOT:compile
[INFO] |  +- com.phonepe.merchants.platform:primus-model:jar:1.35.1-SNAPSHOT:compile
[INFO] |  +- com.opencsv:opencsv:jar:5.4:compile
[INFO] |  +- com.phonepe.services:handlebar-helpers:jar:1.0.0:compile
[INFO] |  +- commons-net:commons-net:jar:3.7.2:compile
[INFO] |  +- org.apache.poi:poi-ooxml:jar:3.17:compile
[INFO] |  |  +- org.apache.poi:poi:jar:3.17:compile
[INFO] |  |  +- org.apache.poi:poi-ooxml-schemas:jar:3.17:compile
[INFO] |  |  |  \- org.apache.xmlbeans:xmlbeans:jar:2.6.0:compile
[INFO] |  |  |     \- stax:stax-api:jar:1.0.1:compile
[INFO] |  |  \- com.github.virtuald:curvesapi:jar:1.04:compile
[INFO] |  +- com.phonepe.platform.loki:loki-core:jar:1.15:compile
[INFO] |  |  +- com.phonepe.platform.loki:loki-client:jar:1.15:compile
[INFO] |  |  \- com.phonepe.payments.keyman:keyman-client-okhttp:jar:0.0.5:compile
[INFO] |  |     \- com.phonepe.payments.keyman:keyman-model:jar:0.0.5:compile
[INFO] |  +- com.github.java-json-tools:json-schema-validator:jar:2.2.14:compile
[INFO] |  |  +- com.github.java-json-tools:jackson-coreutils-equivalence:jar:1.0:compile
[INFO] |  |  |  \- com.github.java-json-tools:jackson-coreutils:jar:2.0:compile
[INFO] |  |  |     \- com.github.java-json-tools:msg-simple:jar:1.2:compile
[INFO] |  |  |        \- com.github.java-json-tools:btf:jar:1.3:compile
[INFO] |  |  +- com.github.java-json-tools:json-schema-core:jar:1.2.14:compile
[INFO] |  |  |  +- com.github.java-json-tools:uri-template:jar:0.10:compile
[INFO] |  |  |  \- org.mozilla:rhino:jar:*******:compile
[INFO] |  |  \- com.sun.mail:mailapi:jar:1.6.2:compile
[INFO] |  +- com.jcraft:jsch:jar:0.1.55:compile
[INFO] |  \- com.phonepe.platform:release-scripts:zip:bin:0.1:compile
[INFO] +- com.phonepe.platform.filters:api-killer-core:jar:1.32:compile
[INFO] |  \- io.dropwizard.metrics:metrics-caffeine:jar:4.1.23:compile
[INFO] +- com.github.jknack:handlebars:jar:4.2.0:compile
[INFO] +- com.github.jknack:handlebars-jackson2:jar:4.2.0:compile
[INFO] +- com.phonepe.platform:executors:jar:0.0.5:compile
[INFO] +- com.phonepe.growth:neuron-client:jar:1.1.29:compile
[INFO] |  +- com.phonepe.growth:neuron-model:jar:1.1.29:compile
[INFO] |  +- com.smoketurner:dropwizard-swagger:jar:1.0.0-2-auth:compile
[INFO] |  |  \- xml-apis:xml-apis:jar:1.4.01:compile
[INFO] |  \- com.flipkart.zjsonpatch:zjsonpatch:jar:0.4.3:compile
[INFO] +- com.phonepe.platform.killswitch:killswitch-common:jar:2.0.6:compile
[INFO] +- com.platform:validation-bundle:jar:********:compile
[INFO] +- org.jboss.logging:jboss-logging:jar:3.3.0.Final:compile
[INFO] +- org.aspectj:aspectjrt:jar:1.9.8:compile
[INFO] +- io.appform.functionmetrics:function-metrics:jar:1.0.1:compile
[INFO] |  \- org.aspectj:aspectjweaver:jar:1.8.13:compile
[INFO] +- org.apache.zookeeper:zookeeper:jar:3.4.6:compile
[INFO] |  +- jline:jline:jar:0.9.94:compile
[INFO] |  \- io.netty:netty:jar:3.7.0.Final:compile
[INFO] +- org.apache.curator:curator-framework:jar:4.2.0:compile
[INFO] |  \- org.apache.curator:curator-client:jar:4.2.0:compile
[INFO] +- org.mariadb.jdbc:mariadb-java-client:jar:2.7.2:compile
[INFO] +- org.springframework.statemachine:spring-statemachine-core:jar:2.4.0:compile
[INFO] |  +- org.springframework:spring-tx:jar:5.3.3:compile
[INFO] |  \- org.springframework:spring-messaging:jar:5.3.3:compile
[INFO] +- org.springframework:spring-context:jar:5.3.3:compile
[INFO] |  +- org.springframework:spring-aop:jar:5.3.3:compile
[INFO] |  +- org.springframework:spring-beans:jar:5.3.3:compile
[INFO] |  +- org.springframework:spring-core:jar:5.3.3:compile
[INFO] |  |  \- org.springframework:spring-jcl:jar:5.3.3:compile
[INFO] |  \- org.springframework:spring-expression:jar:5.3.3:compile
[INFO] +- com.fasterxml.jackson.dataformat:jackson-dataformat-csv:jar:2.12.1:compile
[INFO] +- io.github.openfeign:feign-core:jar:11.2:compile
[INFO] +- io.github.openfeign:feign-jackson:jar:11.2:compile
[INFO] +- io.github.openfeign:feign-httpclient:jar:11.2:compile
[INFO] +- io.github.openfeign:feign-okhttp:jar:11.2:compile
[INFO] +- guru.nidi:graphviz-java:jar:0.18.1:compile
[INFO] |  +- org.webjars.npm:viz.js-graphviz-java:jar:2.1.3:compile
[INFO] |  +- guru.nidi.com.kitfox:svgSalamander:jar:1.1.3:compile
[INFO] |  +- net.arnx:nashorn-promise:jar:0.1.1:compile
[INFO] |  +- org.apache.commons:commons-exec:jar:1.3:compile
[INFO] |  +- org.slf4j:jcl-over-slf4j:jar:1.7.30:compile
[INFO] |  +- org.slf4j:jul-to-slf4j:jar:1.7.30:compile
[INFO] |  \- guru.nidi.com.eclipsesource.j2v8:j2v8_macosx_x86_64:jar:4.6.0:compile
[INFO] +- net.bytebuddy:byte-buddy:jar:1.10.21:compile
[INFO] +- org.apache.commons:commons-lang3:jar:3.12.0:compile
[INFO] +- uk.org.webcompere:system-stubs-jupiter:jar:2.1.7:compile
[INFO] |  \- uk.org.webcompere:system-stubs-core:jar:2.1.7:compile
[INFO] +- org.mockito:mockito-core:jar:4.3.1:test
[INFO] |  +- net.bytebuddy:byte-buddy-agent:jar:1.12.7:compile
[INFO] |  \- org.objenesis:objenesis:jar:3.2:test
[INFO] +- org.mockito:mockito-junit-jupiter:jar:4.3.1:test
[INFO] +- org.mockito:mockito-inline:jar:4.3.1:test
[INFO] +- org.awaitility:awaitility:jar:3.1.6:test
[INFO] |  +- org.hamcrest:hamcrest-library:jar:1.3:test
[INFO] |  \- org.hamcrest:hamcrest-core:jar:1.3:test
[INFO] +- com.github.tomakehurst:wiremock:jar:1.58:test
[INFO] |  +- xmlunit:xmlunit:jar:1.6:test
[INFO] |  +- org.mortbay.jetty:jetty:jar:6.1.26:test
[INFO] |  |  +- org.mortbay.jetty:jetty-util:jar:6.1.26:test
[INFO] |  |  \- org.mortbay.jetty:servlet-api:jar:2.5-20081211:test
[INFO] |  +- org.skyscreamer:jsonassert:jar:1.2.3:test
[INFO] |  |  \- org.json:json:jar:20090211:compile
[INFO] |  \- net.sf.jopt-simple:jopt-simple:jar:4.9:compile
[INFO] +- org.hamcrest:hamcrest-all:jar:1.3:test
[INFO] +- com.h2database:h2:jar:1.4.200:test
[INFO] +- io.appform.testcontainer:junit-testcontainer-aerospike:jar:1.0.6:test
[INFO] |  \- io.appform.testcontainer:junit-testcontainer-commons:jar:1.0.6:test
[INFO] |     +- org.testcontainers:testcontainers:jar:1.15.3:test
[INFO] |     |  +- org.apache.commons:commons-compress:jar:1.20:test
[INFO] |     |  +- org.rnorth.duct-tape:duct-tape:jar:1.0.8:test
[INFO] |     |  +- org.rnorth.visible-assertions:visible-assertions:jar:2.1.2:test
[INFO] |     |  +- com.github.docker-java:docker-java-api:jar:3.2.8:test
[INFO] |     |  \- com.github.docker-java:docker-java-transport-zerodep:jar:3.2.8:test
[INFO] |     \- com.github.docker-java:docker-java-core:jar:3.2.8:test
[INFO] |        +- com.github.docker-java:docker-java-transport:jar:3.2.8:test
[INFO] |        \- org.bouncycastle:bcpkix-jdk15on:jar:1.64:test
[INFO] +- io.appform.testcontainer:junit-testcontainer-rabbitmq:jar:1.0.6:test
[INFO] +- io.appform.testcontainer:junit-testcontainer-mariadb:jar:1.0.6:test
[INFO] +- com.phonepe.services:merchant-mandates-model:jar:0.0.56:compile
[INFO] |  +- com.phonepe.merchants:neo-models:jar:1.0.1:compile
[INFO] |  +- com.phonepe.payments.intent:upi-intent-utils:jar:1.0.5:compile
[INFO] |  \- com.phonepe.nexus:nexus-models:jar:1.0.4:compile
[INFO] +- com.phonepe:edc-model:jar:1.0.71:compile
[INFO] |  +- com.phonepe.merchant:gladius-models:jar:1.0.223:compile
[INFO] |  |  +- io.github.fsm:fsm:jar:0.0.2-1:compile
[INFO] |  |  +- com.phonepe.merchant:legion-models:jar:1.0.288:compile
[INFO] |  |  +- org.elasticsearch:elasticsearch:jar:7.17.8:compile
[INFO] |  |  |  +- org.elasticsearch:elasticsearch-core:jar:7.17.8:compile
[INFO] |  |  |  +- org.elasticsearch:elasticsearch-secure-sm:jar:7.17.8:compile
[INFO] |  |  |  +- org.elasticsearch:elasticsearch-x-content:jar:7.17.8:compile
[INFO] |  |  |  |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-smile:jar:2.10.4:compile
[INFO] |  |  |  |  \- com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:jar:2.10.4:compile
[INFO] |  |  |  +- org.elasticsearch:elasticsearch-geo:jar:7.17.8:compile
[INFO] |  |  |  +- org.elasticsearch:elasticsearch-lz4:jar:7.17.8:compile
[INFO] |  |  |  |  \- org.lz4:lz4-java:jar:1.8.0:compile
[INFO] |  |  |  +- org.apache.lucene:lucene-core:jar:8.11.1:compile
[INFO] |  |  |  +- org.apache.lucene:lucene-analyzers-common:jar:8.11.1:compile
[INFO] |  |  |  +- org.apache.lucene:lucene-backward-codecs:jar:8.11.1:compile
[INFO] |  |  |  +- org.apache.lucene:lucene-grouping:jar:8.11.1:compile
[INFO] |  |  |  +- org.apache.lucene:lucene-highlighter:jar:8.11.1:compile
[INFO] |  |  |  +- org.apache.lucene:lucene-join:jar:8.11.1:compile
[INFO] |  |  |  +- org.apache.lucene:lucene-memory:jar:8.11.1:compile
[INFO] |  |  |  +- org.apache.lucene:lucene-misc:jar:8.11.1:compile
[INFO] |  |  |  +- org.apache.lucene:lucene-queries:jar:8.11.1:compile
[INFO] |  |  |  +- org.apache.lucene:lucene-queryparser:jar:8.11.1:compile
[INFO] |  |  |  +- org.apache.lucene:lucene-sandbox:jar:8.11.1:compile
[INFO] |  |  |  +- org.apache.lucene:lucene-spatial3d:jar:8.11.1:compile
[INFO] |  |  |  +- org.apache.lucene:lucene-suggest:jar:8.11.1:compile
[INFO] |  |  |  +- org.elasticsearch:elasticsearch-cli:jar:7.17.8:compile
[INFO] |  |  |  +- com.carrotsearch:hppc:jar:0.8.1:compile
[INFO] |  |  |  +- com.tdunning:t-digest:jar:3.2:compile
[INFO] |  |  |  +- org.apache.logging.log4j:log4j-api:jar:2.17.1:compile
[INFO] |  |  |  \- org.elasticsearch:elasticsearch-plugin-classloader:jar:7.17.8:runtime
[INFO] |  |  +- com.phonepe.merchants:odin-ace-models:jar:1.63:compile
[INFO] |  |  |  +- com.phonepe.frontend:chimera-lite:jar:2.175:compile
[INFO] |  |  |  |  +- com.phonepe.frontend:chimera-bonsai:jar:2.175:compile
[INFO] |  |  |  |  |  +- com.phonepe.platform:bonsai-core:jar:1.0.38:compile
[INFO] |  |  |  |  |  |  +- com.phonepe.platform:bonsai-json-eval:jar:1.0.38:compile
[INFO] |  |  |  |  |  |  |  \- com.phonepe.platform:query-dsl:jar:1.3:compile
[INFO] |  |  |  |  |  |  \- com.phonepe.platform:bonsai-models:jar:1.0.38:compile
[INFO] |  |  |  |  |  |     \- com.phonepe.folios:folios-condition-engine:jar:1.5:compile
[INFO] |  |  |  |  |  +- com.phonepe.frontend:chimera-models:jar:2.175:compile
[INFO] |  |  |  |  |  +- com.phonepe.platform:tracing-bundle-core:jar:1.0.5:compile
[INFO] |  |  |  |  |  |  \- com.phonepe.platform:tracing-bundle-brave-zipkin:jar:1.0.5:compile
[INFO] |  |  |  |  |  |     +- com.platform.zipkin.zipkin2:zipkin-sender-http:jar:2.23.3-13:compile
[INFO] |  |  |  |  |  |     |  \- com.platform.zipkin.zipkin2:zipkin:jar:2.23.3-13:compile
[INFO] |  |  |  |  |  |     +- com.phonepe.platform:tracing-bundle-instrumentation:jar:1.0.5:compile
[INFO] |  |  |  |  |  |     |  \- io.opentracing.contrib:opentracing-jaxrs2-discovery:jar:1.0.0:compile
[INFO] |  |  |  |  |  |     |     \- io.opentracing.contrib:opentracing-jaxrs2:jar:1.0.0:compile
[INFO] |  |  |  |  |  |     |        +- io.opentracing.contrib:opentracing-web-servlet-filter:jar:0.4.0:compile
[INFO] |  |  |  |  |  |     |        \- org.eclipse.microprofile.opentracing:microprofile-opentracing-api:jar:1.3:compile
[INFO] |  |  |  |  |  |     |           \- org.osgi:org.osgi.annotation.versioning:jar:1.0.0:compile
[INFO] |  |  |  |  |  |     +- io.opentracing.brave:brave-opentracing:jar:0.37.5:compile
[INFO] |  |  |  |  |  |     |  \- io.zipkin.brave:brave:jar:5.13.1:compile
[INFO] |  |  |  |  |  |     |     \- io.zipkin.reporter2:zipkin-reporter-brave:jar:2.16.0:compile
[INFO] |  |  |  |  |  |     \- io.zipkin.reporter2:zipkin-sender-kafka:jar:2.16.2:compile
[INFO] |  |  |  |  |  |        +- io.zipkin.reporter2:zipkin-reporter:jar:2.16.2:compile
[INFO] |  |  |  |  |  |        |  \- io.zipkin.zipkin2:zipkin:jar:2.23.1:compile
[INFO] |  |  |  |  |  |        \- org.apache.kafka:kafka-clients:jar:2.6.0:compile
[INFO] |  |  |  |  |  |           \- com.github.luben:zstd-jni:jar:1.4.4-7:compile
[INFO] |  |  |  |  |  \- com.phonepe.platform:tracing-bundle-models:jar:1.0.5:compile
[INFO] |  |  |  |  \- com.phonepe.frontend:chimera-client:jar:2.175:compile
[INFO] |  |  |  +- com.phonepe.merchants:paradox-models:jar:2.17:compile
[INFO] |  |  |  |  \- com.phonepe.merchants:tesseract-models:jar:1.13:compile
[INFO] |  |  |  +- com.phonepe.accounting:fortuna-model:jar:1.0.40:compile
[INFO] |  |  |  |  +- com.phonepe.growth:pegasus-model:jar:1.2.17:compile
[INFO] |  |  |  |  |  \- mysql:mysql-connector-java:jar:8.0.20:compile
[INFO] |  |  |  |  \- com.phonepe.accounting:cheetah-client-models:jar:1.0.42:compile
[INFO] |  |  |  |     +- com.phonepe.accounting:raptor-service-models:jar:1.0.73:compile
[INFO] |  |  |  |     \- com.phonepe.accounting:plutus-database-common:jar:3.0.207:compile
[INFO] |  |  |  |        \- org.hibernate:hibernate-hikaricp:jar:5.4.28.Final:compile
[INFO] |  |  |  |           \- com.zaxxer:HikariCP:jar:3.2.0:compile
[INFO] |  |  |  \- com.phonepe.dataplatform:profile-client:jar:1.7.92:compile
[INFO] |  |  |     \- com.phonepe.core:dropwizard-jwt-integration:jar:2.0.39-FRA:compile
[INFO] |  |  \- com.phonepe.merchant:filtercraft-client:jar:0.0.1-SNAPSHOT:compile
[INFO] |  |     \- com.phonepe.merchant:filtercraft-internal:jar:0.0.1-SNAPSHOT:compile
[INFO] |  |        \- org.elasticsearch.client:elasticsearch-rest-high-level-client:jar:7.12.1:compile
[INFO] |  |           +- org.elasticsearch.client:elasticsearch-rest-client:jar:7.12.1:compile
[INFO] |  |           |  +- org.apache.httpcomponents:httpasyncclient:jar:4.1.4:compile
[INFO] |  |           |  \- org.apache.httpcomponents:httpcore-nio:jar:4.4.12:compile
[INFO] |  |           +- org.elasticsearch.plugin:mapper-extras-client:jar:7.12.1:compile
[INFO] |  |           +- org.elasticsearch.plugin:parent-join-client:jar:7.12.1:compile
[INFO] |  |           +- org.elasticsearch.plugin:aggs-matrix-stats-client:jar:7.12.1:compile
[INFO] |  |           +- org.elasticsearch.plugin:rank-eval-client:jar:7.12.1:compile
[INFO] |  |           \- org.elasticsearch.plugin:lang-mustache-client:jar:7.12.1:compile
[INFO] |  |              \- com.github.spullara.mustache.java:compiler:jar:0.9.6:compile
[INFO] |  \- io.github.classgraph:classgraph:jar:4.8.138:compile
[INFO] +- com.phonepe.payments:netpe-model:jar:1.0.56:compile
[INFO] +- com.phonepe.payments:ft-client-model:jar:4.2.8:compile
[INFO] +- com.phonepe.platform:clockwork-models:jar:3.1.1:compile
[INFO] |  +- com.phonepe.user:dejavu-replay:jar:1.0:compile
[INFO] |  |  \- com.phonepe.user:dejavu-compare:jar:1.0:compile
[INFO] |  \- org.apache.curator:curator-test:jar:4.0.1:compile
[INFO] +- com.phonepe.services:refund-orchestrator-models:jar:1.2.0:compile
[INFO] |  +- com.phonepe.pegasus:pegasus-pro-model:jar:1.2.99:compile
[INFO] |  +- com.phonepe.growth:offerengine-model:jar:4.2.43:compile
[INFO] |  +- com.phonepe.growth:cumulus:jar:1.1.2:compile
[INFO] |  \- com.phonepe.platform:consent-model:jar:1.0.1:compile
[INFO] +- com.phonepe.merchant.platform:pipeline:jar:1.0.51:compile
[INFO] +- com.phonepe.cp:caishen-api:jar:3.0.111:compile
[INFO] |  +- io.dropwizard:dropwizard-jdbi:jar:2.0.0-rc9:compile
[INFO] |  |  +- org.jdbi:jdbi:jar:2.78:compile
[INFO] |  |  \- io.dropwizard.metrics:metrics-jdbi:jar:4.1.0:compile
[INFO] |  +- com.phonepe.platform.killswitch:killswitch-client:jar:2.0.7:compile
[INFO] |  +- com.phonepe.platform.filters:api-killer-killswitch:jar:1.9:compile
[INFO] |  +- com.phonepe.platform:dropwizard-warmup-core:jar:2.0.30:compile
[INFO] |  +- com.phonepe.verified:pvc-models:jar:1.3.296:compile
[INFO] |  +- com.phonepe.platform:vision-models:jar:2.1.140:compile
[INFO] |  \- com.phonepe.platform:db-sandbox-bundle:jar:1.0.3:compile
[INFO] |     +- com.phonepe.platform:db-sandbox-model:jar:1.0.3:compile
[INFO] |     \- com.phonepe.platform:db-sandbox-core:jar:1.0.3:compile
[INFO] |        +- org.liquibase:liquibase-core:jar:3.6.3:compile
[INFO] |        +- org.jgrapht:jgrapht-core:jar:1.4.0:compile
[INFO] |        |  \- org.jheaps:jheaps:jar:0.11:compile
[INFO] |        +- org.jooq:jooq:jar:3.14.7:compile
[INFO] |        |  \- org.reactivestreams:reactive-streams:jar:1.0.2:compile
[INFO] |        +- org.jooq:jooq-meta:jar:3.14.7:compile
[INFO] |        \- org.jooq:jooq-codegen:jar:3.14.7:compile
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.7.1:test
[INFO] |  +- org.apiguardian:apiguardian-api:jar:1.1.0:compile
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.7.1:compile
[INFO] +- org.junit.jupiter:junit-jupiter-params:jar:5.7.1:test
[INFO] \- com.phonepe.verified:kaizen-core:jar:0.0.5:compile
[INFO]    +- com.phonepe.verified:kaizen-models:jar:0.0.5:compile
[INFO]    |  \- com.phonepe.shadow:shadow-v2-models:jar:1.0.104:compile
[INFO]    +- org.apache.commons:commons-jcs-core:jar:2.2.2:compile
[INFO]    +- com.phonepe.platform:requestinfo-models:jar:2.0.23-23:compile
[INFO]    |  \- io.dropwizard:dropwizard-maxmind:jar:1.3.7-1:compile
[INFO]    +- org.apache.tika:tika-core:jar:2.0.0:compile
[INFO]    +- io.appform.hope:hope-lang:jar:2.0.3:compile
[INFO]    |  +- io.appform.hope:hope-core:jar:2.0.3:compile
[INFO]    |  \- net.minidev:json-smart:jar:2.4.11:compile
[INFO]    |     \- net.minidev:accessors-smart:jar:2.4.11:compile
[INFO]    |        \- org.ow2.asm:asm:jar:9.3:compile
[INFO]    +- com.phonepe.platform.docstore:docstore-client:jar:2.9:compile
[INFO]    +- com.phonepe.gandalf:gandalf-client:jar:2.0.40:compile
[INFO]    |  +- com.phonepe.gandalf:gandalf-core:jar:2.0.40:compile
[INFO]    |  |  \- com.phonepe.gandalf:gandalf-models:jar:2.0.40:compile
[INFO]    |  \- org.glassfish.jersey.containers:jersey-container-grizzly2-http:jar:2.32:compile
[INFO]    |     \- org.glassfish.grizzly:grizzly-http-server:jar:2.4.4:compile
[INFO]    |        \- org.glassfish.grizzly:grizzly-http:jar:2.4.4:compile
[INFO]    |           \- org.glassfish.grizzly:grizzly-framework:jar:2.4.4:compile
[INFO]    +- com.phonepe.verified:oncall-dropwizard-bundle:jar:1.0.3:compile
[INFO]    +- com.phonepe.shadow:shadow-v2-services:jar:1.0.104:compile
[INFO]    +- com.phonepe.shadow:shadow-v2-core:jar:1.0.104:compile
[INFO]    +- com.phonepe.verified:drishti-models:jar:1.0.8:compile
[INFO]    +- com.phonepe.platform:heimdall-models:jar:1.0.73:compile
[INFO]    +- com.phonepe.platform:columbus-core-models:jar:1.0.4:compile
[INFO]    +- com.phonepe.platform:atlas-model:jar:2.1.148:compile
[INFO]    +- org.springframework.data:spring-data-commons:jar:2.7.5:compile
[INFO]    +- com.phonepe.growth:mustang:jar:2.3.0:compile
[INFO]    |  \- org.apache.maven:maven-artifact:jar:3.8.5:compile
[INFO]    +- com.phonepe.platform.bullhorn:bullhorn-models:jar:1.0.132:compile
[INFO]    +- com.phonepe.growth:campaign-store-models:jar:2.0.29:compile
[INFO]    |  +- com.flipkart.flipcast:flipcast-models:jar:3.1.26:compile
[INFO]    |  \- com.github.phaneesh:sonar-break-maven-plugin:jar:1.2.7:compile
[INFO]    +- com.openhtmltopdf:openhtmltopdf-pdfbox:jar:1.0.6:compile
[INFO]    |  +- org.apache.pdfbox:pdfbox:jar:2.0.22:compile
[INFO]    |  |  \- org.apache.pdfbox:fontbox:jar:2.0.22:compile
[INFO]    |  +- org.apache.pdfbox:xmpbox:jar:2.0.22:compile
[INFO]    |  +- com.openhtmltopdf:openhtmltopdf-core:jar:1.0.6:compile
[INFO]    |  \- de.rototor.pdfbox:graphics2d:jar:0.30:compile
[INFO]    +- com.phonepe.growth:hawkeye-model:jar:1.2.244:compile
[INFO]    +- com.phonepe.growth:hawkeye-common:jar:1.2.244:compile
[INFO]    \- com.phonepe.growth:hawkeye-client:jar:1.2.244:compile
[INFO] ------------------------------------------------------------------------
[INFO] Reactor Summary for stratos 2.0.67-SNAPSHOT:
[INFO] 
[INFO] stratos ............................................ SUCCESS [  2.514 s]
[INFO] stratos-models ..................................... SUCCESS [  0.971 s]
[INFO] stratos-server ..................................... SUCCESS [  2.599 s]
[INFO] ------------------------------------------------------------------------
[INFO] BUILD SUCCESS
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  6.388 s
[INFO] Finished at: 2025-09-08T18:29:22+05:30
[INFO] ------------------------------------------------------------------------
