#!/usr/bin/env bash
export MAVEN_OPTS="-Xms6096m -Xmx6096m -Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=5006"
if [[ "$1" = 'd' ]]
then cd .. && mvn clean install -DskipTests -Plocal -pl !stratos-server && cd stratos-server
fi
mvn compile -Plocal exec:java -Dexec.mainClass="com.phonepe.merchant.platform.server.StratosApplication" -Duser.timezone=IST -DlocalConfig=true -Dexec.args="server config/local.yml"
#mvn clean install -DskipTests -Plocal && java -jar -XX:+UseG1GC -Xms1g -Xmx1g -DlocalConfig=true -Ddb.shards=1 target/stratos-server-1.0-SNAPSHOT.jar server config/local.yml