#!/bin/bash
set -x

pid=0

# SIGUSR1-handler
my_handler() {
  echo "my_handler"
}

# SIGTERM-handler
term_handler() {
  if [ $pid -ne 0 ]; then
    kill -SIGTERM "$pid"
    wait "$pid"
  fi
  exit 143; # 128 + 15 -- SIGTERM
}

# setup handlers
# on callback, kill the last background process, which is `tail -f /dev/null` and execute the specified handler
trap 'kill ${!}; my_handler' SIGUSR1
trap 'kill ${!}; term_handler' SIGTERM

# run application
export DNS_HOST=`ip r | awk '/default/{print $3}'`
printf "nameserver $DNS_HOST\n" > /etc/resolv.conf
printenv
java -jar --add-opens java.base/java.lang=ALL-UNNAMED -XX:+${GC_ALGO-UseG1GC} -Xms${JAVA_PROCESS_MIN_HEAP-1g} -Xmx${JAVA_PROCESS_MAX_HEAP-1g} ${JAVA_OPTS} stratos-server.jar server /rosey/config.yml &

pid="$"

# wait forever
while true
do
  tail -f /dev/null & wait ${!}
done