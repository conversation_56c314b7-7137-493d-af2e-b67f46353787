package com.phonepe.central.dms.server.mariadb.repository;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.phonepe.central.dispute.request.tenant.TenantSearchRequest;
import com.phonepe.central.dispute.tenant.TenantState;
import com.phonepe.central.dms.server.mariadb.entities.TenantEntity;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.ErrorConfiguratorBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import io.appform.dropwizard.sharding.dao.RelationalDao;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.criterion.DetachedCriteria;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@Slf4j
@ExtendWith(MockitoExtension.class)
public class TenantRepositoryTest extends ErrorConfiguratorBaseTest {

    @Mock
    private RelationalDao<TenantEntity> relationalDao;

    private TenantRepository tenantRepository;

    @BeforeEach
    public void setup() throws MalformedURLException, URISyntaxException {
        tenantRepository = new TenantRepository(relationalDao);
        DisputeExceptionUtil.init(getResourceErrorService());
    }

    @Test
    void testGetTenantList_ReturnsEntities() throws Exception {
        TenantEntity mockEntity = TenantEntity.builder()
                .tenantId("TNT123456")
                .name("Tenant Name")
                .subCategory("Category")
                .build();

        TenantSearchRequest request = TenantSearchRequest.builder()
                .tenantId("TNT123456")
                .name("Tenant Name")
                .subCategory("Category")
                .build();

        when(relationalDao.select(eq("TNT123456"), any(DetachedCriteria.class), eq(0), eq(2000))).thenReturn(
                Collections.singletonList(mockEntity));

        List<TenantEntity> result = tenantRepository.getTenantList(request);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(mockEntity, result.get(0));
    }

    @Test
    void testGetTenantList_ThrowsException() throws Exception {
        TenantSearchRequest request = TenantSearchRequest.builder()
                .tenantId("TNT_FAIL")
                .build();

        when(relationalDao.select(eq("TNT_FAIL"), any(DetachedCriteria.class), eq(0), eq(2000))).thenThrow(
                new RuntimeException("Simulated DB failure"));

        DisputeException ex = assertThrows(DisputeException.class, () -> tenantRepository.getTenantList(request));

        assertEquals(StratosErrorCodeKey.DB_ERROR, ex.getErrorCode());
    }

    @Test
    void testFindByNameAndSubCategory_ReturnsEntity() throws Exception {
        String name = "Tenant Name";
        String subCategory = "Category";

        TenantEntity mockEntity = TenantEntity.builder()
                .tenantId("TNT987654")
                .name(name)
                .subCategory(subCategory)
                .build();

        when(relationalDao.scatterGather(any(DetachedCriteria.class), eq(0), eq(2000))).thenReturn(
                List.of(mockEntity));

        Optional<TenantEntity> result = tenantRepository.findByNameAndSubCategory(name, subCategory);

        assertTrue(result.isPresent());
        assertEquals(mockEntity, result.get());
    }

    @Test
    void testFindByNameAndSubCategory_ReturnsEmpty() throws Exception {
        String name = "Nonexistent";
        String subCategory = "Unknown";

        when(relationalDao.scatterGather(any(DetachedCriteria.class), eq(0), eq(2000))).thenReturn(
                Collections.emptyList());

        Optional<TenantEntity> result = tenantRepository.findByNameAndSubCategory(name, subCategory);

        assertFalse(result.isPresent());
    }

    @Test
    void testFindByNameAndSubCategory_ThrowsException() throws Exception {
        String name = "FailName";
        String subCategory = "FailCategory";

        when(relationalDao.scatterGather(any(DetachedCriteria.class), eq(0), eq(2000))).thenThrow(
                new RuntimeException("Simulated DB failure"));

        DisputeException ex = assertThrows(DisputeException.class,
                () -> tenantRepository.findByNameAndSubCategory(name, subCategory));

        assertEquals(StratosErrorCodeKey.DB_ERROR, ex.getErrorCode());
    }

    @Test
    void testGetTenantList_UsesScatterGather() throws Exception {
        TenantEntity mockEntity = TenantEntity.builder()
                .tenantId("TNT999")
                .name("Tenant SG")
                .subCategory("SGCategory")
                .build();

        TenantSearchRequest request = TenantSearchRequest.builder()
                .name("Tenant SG")
                .subCategory("SGCategory")
                .state(TenantState.ACTIVE)
                .build();

        when(relationalDao.scatterGather(any(DetachedCriteria.class), eq(0), eq(2000))).thenReturn(
                Collections.singletonList(mockEntity));

        List<TenantEntity> result = tenantRepository.getTenantList(request);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(mockEntity, result.get(0));
    }

    @Test
    void testGetTenantList_ScatterGatherThrowsException() throws Exception {
        TenantSearchRequest request = TenantSearchRequest.builder()
                .name("ErrorName")
                .build();

        when(relationalDao.scatterGather(any(DetachedCriteria.class), eq(0), eq(2000))).thenThrow(
                new RuntimeException("Simulated scatter failure"));

        DisputeException ex = assertThrows(DisputeException.class, () -> tenantRepository.getTenantList(request));

        assertEquals(StratosErrorCodeKey.DB_ERROR, ex.getErrorCode());
    }

}
