package com.phonepe.central.dms.server.integrationTests.tenant;


import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.phonepe.central.dispute.request.tenant.TenantCreateRequest;
import com.phonepe.central.dispute.request.tenant.TenantSearchRequest;
import com.phonepe.central.dispute.request.tenant.TenantUpdateRequest;
import com.phonepe.central.dispute.tenant.Tenant;
import com.phonepe.central.dms.server.mariadb.repository.TenantRepository;
import com.phonepe.central.dms.server.resources.TenantResource;
import com.phonepe.central.dms.server.service.TenantService;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.LoadOnlyOnClassLevelBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import io.dropwizard.testing.junit5.DropwizardExtensionsSupport;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import javax.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@Slf4j
@ExtendWith(DropwizardExtensionsSupport.class)
public class TenantResourceIntegrationTest extends LoadOnlyOnClassLevelBaseTest {

    private final ServiceUserPrincipal serviceUserPrincipal = TestDataUtils.getOlympusUser();

    private TenantResource tenantResource;
    private TenantService tenantService;
    private TenantRepository tenantRepository;

    @BeforeEach
    public void setUpInjectionAndInitialization() {
        tenantRepository = guiceInjector.getInstance(TenantRepository.class);
        tenantService = guiceInjector.getInstance(TenantService.class);
        tenantResource = guiceInjector.getInstance(TenantResource.class);
        truncateDb();
    }

    @Test
    void testCreateTenantSuccess() {
        TenantCreateRequest createRequest = TenantCreateRequest.builder()
                .name("TestTenant")
                .description("Test description")
                .subCategory("TEST_SUB_CAT")
                .emailIds(List.of("<EMAIL>"))
                .build();

        Response response = tenantResource.createTenant(serviceUserPrincipal, createRequest);

        assertEquals(200, response.getStatus());

        Tenant createdTenant = (Tenant) response.getEntity();
        assertNotNull(createdTenant);
        assertEquals("TestTenant", createdTenant.getName());
        assertEquals("TEST_SUB_CAT", createdTenant.getSubCategory());
        assertEquals(Set.of("<EMAIL>"), createdTenant.getEmailIds());
        assertTrue(createdTenant.getTenantId().startsWith("TNT"), "Tenant ID should start with 'TNT'");
    }

    @Test
    void testCreateTenantDuplicate() {
        mockTenantCreation();

        TenantCreateRequest duplicateRequest = TenantCreateRequest.builder()
                .name("TestTenant")  // same name
                .subCategory("TEST_SUB_CAT")  // same subCategory
                .description("Duplicate desc")
                .emailIds(List.of("<EMAIL>"))
                .build();

        DisputeException ex = assertThrows(DisputeException.class,
                () -> tenantResource.createTenant(serviceUserPrincipal, duplicateRequest));

        assertEquals(StratosErrorCodeKey.TENANT_ALREADY_EXISTS, ex.getErrorCode());
    }


    @Test
    void testSearchTenantSuccess() {

        mockTenantCreation();

        TenantSearchRequest searchRequest = TenantSearchRequest.builder()
                .name("TestTenant")
                .build();

        Response response = tenantResource.searchTenants(serviceUserPrincipal, searchRequest);

        assertEquals(200, response.getStatus());

        List<Tenant> results = (List<Tenant>) response.getEntity();
        assertNotNull(results);
        assertEquals(1, results.size());
        assertEquals("TestTenant", results.get(0)
                .getName());
    }

    @Test
    void testUpdateTenantEmailAndState() {

        mockTenantCreation();

        TenantSearchRequest searchRequest = TenantSearchRequest.builder()
                .name("TestTenant")
                .build();
        List<Tenant> tenants = tenantService.searchForTenants(searchRequest);
        String tenantId = tenants.get(0)
                .getTenantId();

        TenantUpdateRequest updateRequest = TenantUpdateRequest.builder()
                .emailIds(Set.of("<EMAIL>"))
                .build();

        Response response = tenantResource.updateTenantDetails(serviceUserPrincipal, tenantId, updateRequest);
        assertEquals(200, response.getStatus());

        Tenant updatedTenant = (Tenant) response.getEntity();
        assertNotNull(updatedTenant);
        assertEquals(Set.of("<EMAIL>"), updatedTenant.getEmailIds());

    }

    @Test
    void testDeleteTenantSuccess() {
        // Create a tenant
        mockTenantCreation();

        // Fetch tenantId
        TenantSearchRequest searchRequest = TenantSearchRequest.builder()
                .name("TestTenant")
                .build();
        List<Tenant> tenants = tenantService.searchForTenants(searchRequest);
        String tenantId = tenants.get(0)
                .getTenantId();

        Response response = tenantResource.removeTenant(serviceUserPrincipal, tenantId);
        assertEquals(200, response.getStatus());

        String responseMessage = (String) response.getEntity();
        assertTrue(responseMessage.contains("deleted successfully"));

        TenantSearchRequest searchRequest1 = TenantSearchRequest.builder()
                .tenantId(tenantId)
                .build();
        List<Tenant> tenants1 = tenantService.searchForTenants(searchRequest1);
        Optional<Tenant> tenantOptional = tenants1.stream()
                .findFirst();

        assertNotNull(tenantOptional);
        tenantOptional.ifPresent(tenant -> assertEquals("DELETED", tenant.getState()
                .name()));
    }

    @Test
    void testDeleteTenantNotFound() {
        DisputeException ex = assertThrows(DisputeException.class,
                () -> tenantResource.removeTenant(serviceUserPrincipal, "NON_EXISTENT"));

        assertEquals(StratosErrorCodeKey.TENANT_NOT_FOUND, ex.getErrorCode());
    }

    private void mockTenantCreation() {
        TenantCreateRequest createRequest = TenantCreateRequest.builder()
                .name("TestTenant")
                .description("Test description")
                .subCategory("TEST_SUB_CAT")
                .emailIds(List.of("<EMAIL>"))
                .build();

        tenantResource.createTenant(serviceUserPrincipal, createRequest);
    }
}