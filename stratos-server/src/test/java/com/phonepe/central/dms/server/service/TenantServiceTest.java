package com.phonepe.central.dms.server.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.phonepe.central.dispute.request.tenant.TenantCreateRequest;
import com.phonepe.central.dispute.request.tenant.TenantSearchRequest;
import com.phonepe.central.dispute.request.tenant.TenantUpdateRequest;
import com.phonepe.central.dispute.tenant.Tenant;
import com.phonepe.central.dispute.tenant.TenantState;
import com.phonepe.central.dms.server.mariadb.entities.TenantEntity;
import com.phonepe.central.dms.server.mariadb.repository.TenantRepository;
import com.phonepe.central.dms.server.utils.TenantConvertorUtils;
import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import com.phonepe.error.configurator.model.service.ResourceErrorService;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.ErrorConfiguratorBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@Slf4j
@ExtendWith(MockitoExtension.class)
public class TenantServiceTest extends ErrorConfiguratorBaseTest {

    private final LocalDateTime now = LocalDateTime.now();

    @Mock
    private TenantRepository tenantRepository;

    @Mock
    private ResourceErrorService<StratosErrorCodeKey> resourceErrorService;

    private TenantService tenantService;

    @BeforeEach
    void setup() throws Exception {
        tenantService = new TenantService(tenantRepository);
        DisputeExceptionUtil.init(getResourceErrorService());
    }

    @Test
    void testCreateNewTenant_Success() {
        TenantCreateRequest request = TenantCreateRequest.builder()
                .name("TestTenant")
                .description("Test Description")
                .subCategory("CATEGORY")
                .emailIds(List.of("<EMAIL>"))
                .build();

        TenantEntity entity = TenantConvertorUtils.tenantCreateRequestToTenantEntity(request);

        when(tenantRepository.save(any())).thenReturn(Optional.of(entity));

        Tenant expected = TenantConvertorUtils.tenantEntityToTenant(entity);

        Tenant actual = tenantService.createNewTenant(request);

        assertEquals(expected, actual);
        assertEquals("TestTenant", actual.getName());
        assertEquals(Set.of("<EMAIL>"), actual.getEmailIds());
    }

    @Test
    void testCreateNewTenant_SaveFails_ThrowsException() {
        TenantCreateRequest request = TenantCreateRequest.builder()
                .name("FailCase")
                .emailIds(List.of("<EMAIL>"))
                .build();

        TenantEntity entity = TenantConvertorUtils.tenantCreateRequestToTenantEntity(request);

        when(tenantRepository.save(any())).thenReturn(Optional.empty());

        DisputeException ex = assertThrows(DisputeException.class, () -> tenantService.createNewTenant(request));
        assertEquals(StratosErrorCodeKey.UNABLE_TO_CREATE_TENANT, ex.getErrorCode());
    }

    @Test
    void testCreateNewTenant_DBException_ThrowsException() {
        TenantCreateRequest request = TenantCreateRequest.builder()
                .name("DBFail")
                .emailIds(List.of("<EMAIL>"))
                .build();

        when(tenantRepository.save(any())).thenThrow(DisputeExceptionUtil.error(StratosErrorCodeKey.DB_ERROR));

        DisputeException ex = assertThrows(DisputeException.class, () -> tenantService.createNewTenant(request));
        assertEquals(StratosErrorCodeKey.DB_ERROR, ex.getErrorCode());
    }

    @Test
    void testSearchForTenants_Success() {
        TenantSearchRequest request = TenantSearchRequest.builder()
                .tenantId("TNT123")
                .build();

        TenantEntity entity = TenantEntity.builder()
                .tenantId("TNT123")
                .name("SearchTenant")
                .emailIds("<EMAIL>,<EMAIL>")
                .createdAt(now)
                .updatedAt(now)
                .build();

        when(tenantRepository.getTenantList(request)).thenReturn(List.of(entity));

        List<Tenant> result = tenantService.searchForTenants(request);

        assertEquals(1, result.size());
        assertEquals("SearchTenant", result.get(0)
                .getName());
        assertEquals(Set.of("<EMAIL>", "<EMAIL>"), result.get(0)
                .getEmailIds());
    }

    @Test
    void testUpdateTenant_Success() {
        String tenantId = "TNT001";
        Set<String> updatedEmails = Set.of("<EMAIL>");

        TenantEntity entity = TenantEntity.builder()
                .tenantId(tenantId)
                .emailIds("<EMAIL>")
                .createdAt(now)
                .updatedAt(now)
                .build();

        TenantUpdateRequest updateRequest = TenantUpdateRequest.builder()
                .emailIds(updatedEmails)
                .build();

        when(tenantRepository.getTenantList(any())).thenReturn(List.of(entity));
        when(tenantRepository.save(entity)).thenReturn(Optional.of(entity));

        Tenant updated = tenantService.updateTenant(tenantId, updateRequest);

        assertEquals(tenantId, updated.getTenantId());
        assertEquals(updatedEmails, updated.getEmailIds());
    }

    @Test
    void testUpdateTenant_NotFound_ThrowsException() {
        when(tenantRepository.getTenantList(any())).thenThrow(
                DisputeExceptionUtil.error(StratosErrorCodeKey.TENANT_NOT_FOUND));

        TenantUpdateRequest updateRequest = TenantUpdateRequest.builder()
                .emailIds(Set.of("<EMAIL>"))
                .build();

        DisputeException ex = assertThrows(DisputeException.class,
                () -> tenantService.updateTenant("TNT404", updateRequest));

        assertEquals(StratosErrorCodeKey.TENANT_NOT_FOUND, ex.getErrorCode());
    }

    @Test
    void testUpdateTenant_SaveFails_ThrowsException() {
        String tenantId = "TNT001";
        TenantEntity entity = TenantEntity.builder()
                .tenantId(tenantId)
                .emailIds("<EMAIL>")
                .build();

        TenantUpdateRequest updateRequest = TenantUpdateRequest.builder()
                .emailIds(Set.of("<EMAIL>"))
                .build();

        when(tenantRepository.getTenantList(any())).thenReturn(List.of(entity));
        when(tenantRepository.save(entity)).thenReturn(Optional.empty());

        DisputeException ex = assertThrows(DisputeException.class,
                () -> tenantService.updateTenant(tenantId, updateRequest));

        assertEquals(StratosErrorCodeKey.UNABLE_TO_UPDATE_TENANT, ex.getErrorCode());
    }

    @Test
    void testDeleteTenant_Success() {
        String tenantId = "TNT001";
        TenantEntity entity = TenantEntity.builder()
                .tenantId(tenantId)
                .state(TenantState.ACTIVE)
                .createdAt(now)
                .updatedAt(now)
                .build();

        when(tenantRepository.getTenantList(any())).thenReturn(List.of(entity));
        when(tenantRepository.save(entity)).thenReturn(Optional.of(entity));

        tenantService.deleteTenant(tenantId);

        assertEquals(TenantState.DELETED, entity.getState());
    }

    @Test
    void testDeleteTenant_NotFound_ThrowsException() {
        when(tenantRepository.getTenantList(any())).thenThrow(
                DisputeExceptionUtil.error(StratosErrorCodeKey.TENANT_NOT_FOUND));

        DisputeException ex = assertThrows(DisputeException.class, () -> tenantService.deleteTenant("TNT404"));

        assertEquals(StratosErrorCodeKey.TENANT_NOT_FOUND, ex.getErrorCode());
    }

    @Test
    void testDeleteTenant_DBError_ThrowsException() {
        String tenantId = "TNT001";
        TenantEntity entity = TenantEntity.builder()
                .tenantId(tenantId)
                .state(TenantState.ACTIVE)
                .build();

        when(tenantRepository.getTenantList(any())).thenThrow(
                DisputeExceptionUtil.error(StratosErrorCodeKey.TENANT_NOT_FOUND));

        DisputeException ex = assertThrows(DisputeException.class, () -> tenantService.deleteTenant(tenantId));

        assertEquals(StratosErrorCodeKey.TENANT_NOT_FOUND, ex.getErrorCode());
    }

    @Test
    void testGetTenantIdFromTenantInfo_Success() {
        TenantInfo tenantInfo = TenantInfo.builder()
                .name("TestTenant")
                .subCategory("CAT")
                .build();

        TenantEntity entity = TenantEntity.builder()
                .tenantId("TNT_ID_001")
                .name("TestTenant")
                .subCategory("CAT")
                .build();

        when(tenantRepository.findByNameAndSubCategory("TestTenant", "CAT")).thenReturn(Optional.of(entity));

        String result = tenantService.getTenantId(tenantInfo.getName(), tenantInfo.getSubCategory());

        assertEquals("TNT_ID_001", result);
    }

    @Test
    void testGetTenantIdFromTenantInfo_NotFound() {
        TenantInfo tenantInfo = TenantInfo.builder()
                .name("MissingTenant")
                .subCategory("CAT")
                .build();

        when(tenantRepository.findByNameAndSubCategory("MissingTenant", "CAT")).thenReturn(Optional.empty());

        DisputeException ex = assertThrows(DisputeException.class, () -> tenantService.getTenantId(tenantInfo.getName(), tenantInfo.getSubCategory()));

        assertEquals(StratosErrorCodeKey.TENANT_NOT_FOUND, ex.getErrorCode());
    }

    @Test
    void testGetTenantInfoFromTenantId_Success() {
        String tenantId = "TNT002";
        TenantEntity entity = TenantEntity.builder()
                .tenantId(tenantId)
                .name("InfoTenant")
                .subCategory("SUB_CAT")
                .build();

        when(tenantRepository.getTenantList(any())).thenReturn(List.of(entity));

        Tenant tenant = tenantService.getTenantByTenantId(tenantId);

        TenantInfo info = TenantInfo.builder()
                .name(tenant.getName())
                .subCategory(tenant.getSubCategory())
                .build();

        assertEquals("InfoTenant", info.getName());
        assertEquals("SUB_CAT", info.getSubCategory());
    }

    @Test
    void testGetTenantInfoFromTenantId_NotFound() {
        when(tenantRepository.getTenantList(any())).thenReturn(List.of());

        DisputeException ex = assertThrows(DisputeException.class,
                () -> tenantService.getTenantByTenantId("UNKNOWN_ID"));

        assertEquals(StratosErrorCodeKey.TENANT_NOT_FOUND, ex.getErrorCode());
    }

    @Test
    void testUpdateTenant_DeletedTenant_ThrowsException() {
        String tenantId = "TNT_DELETED";

        TenantEntity deletedEntity = TenantEntity.builder()
                .tenantId(tenantId)
                .state(TenantState.DELETED)
                .build();

        TenantUpdateRequest updateRequest = TenantUpdateRequest.builder()
                .emailIds(Set.of("<EMAIL>"))
                .build();

        when(tenantRepository.getTenantList(any())).thenReturn(List.of(deletedEntity));

        DisputeException ex = assertThrows(DisputeException.class,
                () -> tenantService.updateTenant(tenantId, updateRequest));

        assertEquals(StratosErrorCodeKey.TENANT_NOT_FOUND, ex.getErrorCode());
    }

    @Test
    void testDeleteTenant_AlreadyDeleted_ThrowsException() {
        String tenantId = "TNT_DELETED";

        TenantEntity entity = TenantEntity.builder()
                .tenantId(tenantId)
                .state(TenantState.DELETED)
                .build();

        when(tenantRepository.getTenantList(any())).thenReturn(List.of(entity));

        DisputeException ex = assertThrows(DisputeException.class, () -> tenantService.deleteTenant(tenantId));

        assertEquals(StratosErrorCodeKey.TENANT_NOT_FOUND, ex.getErrorCode());
    }

    @Test
    void testCreateNewTenant_TenantAlreadyExists() {
        TenantCreateRequest request = TenantCreateRequest.builder()
                .name("DuplicateTenant")
                .subCategory("CAT")
                .emailIds(List.of("<EMAIL>"))
                .build();

        TenantEntity existing = TenantEntity.builder()
                .tenantId("TNT_DUP")
                .name("DuplicateTenant")
                .subCategory("CAT")
                .build();

        when(tenantRepository.findByNameAndSubCategory("DuplicateTenant", "CAT")).thenReturn(Optional.of(existing));

        DisputeException ex = assertThrows(DisputeException.class, () -> tenantService.createNewTenant(request));

        assertEquals(StratosErrorCodeKey.TENANT_ALREADY_EXISTS, ex.getErrorCode());
    }

    @Test
    void testSearchForTenants_EmptyList_ThrowsException() {
        TenantSearchRequest request = TenantSearchRequest.builder()
                .tenantId("TNT_NO_MATCH")
                .build();

        when(tenantRepository.getTenantList(request)).thenThrow(
                DisputeExceptionUtil.error(StratosErrorCodeKey.TENANT_NOT_FOUND));

        DisputeException ex = assertThrows(DisputeException.class, () -> tenantService.searchForTenants(request));

        assertEquals(StratosErrorCodeKey.TENANT_NOT_FOUND, ex.getErrorCode());
    }

}
