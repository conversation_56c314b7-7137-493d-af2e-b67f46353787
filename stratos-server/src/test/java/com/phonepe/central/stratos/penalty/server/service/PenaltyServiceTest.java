package com.phonepe.central.stratos.penalty.server.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.phonepe.central.stratos.notification.EmailCommunicationRequest;
import com.phonepe.central.stratos.penalty.Penalty;
import com.phonepe.central.stratos.penalty.disbursement.PenaltyDisbursementState;
import com.phonepe.central.stratos.penalty.probables.PenaltyProbable;
import com.phonepe.central.stratos.penalty.request.DateRangeRequest;
import com.phonepe.central.stratos.penalty.request.penalty.PenaltyBasicSearchRequest;
import com.phonepe.central.stratos.penalty.request.penalty.PenaltyDateRangeSearchRequest;
import com.phonepe.central.stratos.penalty.response.PenaltyResponse;
import com.phonepe.central.stratos.penalty.server.PenaltyTestObjectUtil;
import com.phonepe.central.stratos.penalty.server.convertor.PenaltyDisbursementConvertors;
import com.phonepe.central.stratos.penalty.server.generator.PenaltyGrowthGenerator;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.PenaltyDisbursementEntity;
import com.phonepe.central.stratos.penalty.server.mariadb.repository.PenaltyRepository;
import com.phonepe.merchant.platform.stratos.server.LoadOnlyOnClassLevelBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;


@ExtendWith(MockitoExtension.class)
class PenaltyServiceTest extends LoadOnlyOnClassLevelBaseTest {


    private PenaltyService penaltyService;

    @Mock
    private PenaltyRepository penaltyRepository;
    @Mock
    private PenaltyGrowthGenerator penaltyGrowthGenerator;
    @Mock
    private PenaltyClientService penaltyClientService;
    @Mock
    private PenaltyDisbursementService penaltyDisbursementService;
    @Mock
    private ClockworkService clockworkService;
    @Mock
    private PenaltyEvaluationService penaltyEvaluationService;
    @Mock
    private PenaltyClassService penaltyClassService;
    @Mock
    private NotificationService notificationService;
    @Mock
    private EventIngester eventIngester;


    @BeforeEach
    public void setup() throws MalformedURLException, URISyntaxException {
        MockitoAnnotations.openMocks(this);
        penaltyService = new PenaltyService(penaltyRepository, penaltyGrowthGenerator, penaltyClientService,
                penaltyDisbursementService, clockworkService, penaltyEvaluationService, penaltyClassService, notificationService, eventIngester);
    }

    @Test
    public void testCreateNewPenaltyWhenAlreadyPresent() {
        PenaltyProbable penaltyProbable = PenaltyTestObjectUtil.getPenaltyProbable();
        Mockito.when(penaltyRepository.getPenaltyForProbable(penaltyProbable.getPenaltyClassId(),
                        penaltyProbable.getProbableId()))
                .thenReturn(Optional.of(PenaltyTestObjectUtil.getPenaltyEntity()));
        Assertions.assertThrows(RuntimeException.class, () -> {
            penaltyService.createNewPenalty(penaltyProbable);
        });
    }

    @Test
    public void testCreateNewPenaltyNotEligibleForGrowthCallback() {
        PenaltyProbable penaltyProbable = PenaltyTestObjectUtil.getPenaltyProbable();
        Mockito.when(penaltyRepository.getPenaltyForProbable(penaltyProbable.getPenaltyClassId(),
                        penaltyProbable.getProbableId()))
                .thenReturn(Optional.empty());
        Mockito.when(penaltyRepository.save(Mockito.any()))
                .thenReturn(Optional.of(PenaltyTestObjectUtil.getPenaltyEntity()));
        Mockito.when(penaltyGrowthGenerator.generateGrowthAmount(Mockito.any()))
                .thenReturn(0L);
        Mockito.when(penaltyEvaluationService.isQualifiedForCriteria(Mockito.any(Penalty.class)))
                .thenReturn(false);
        penaltyService.createNewPenalty(penaltyProbable);
        Mockito.verify(clockworkService, Mockito.times(0))
                .schedulePenaltyEntityAtGrowthTime(Mockito.any(Penalty.class), Mockito.any(Date.class));

    }

    @Test
    public void testCreateNewPenaltyEligibleForGrowthCallback() {
        PenaltyProbable penaltyProbable = PenaltyTestObjectUtil.getPenaltyProbable();
        Mockito.when(penaltyRepository.getPenaltyForProbable(penaltyProbable.getPenaltyClassId(),
                        penaltyProbable.getProbableId()))
                .thenReturn(Optional.empty());
        Mockito.when(penaltyRepository.save(Mockito.any()))
                .thenReturn(Optional.of(PenaltyTestObjectUtil.getPenaltyEntity()));
        Mockito.when(penaltyGrowthGenerator.generateGrowthAmount(Mockito.any()))
                .thenReturn(0L);
        Mockito.when(penaltyEvaluationService.isQualifiedForCriteria(Mockito.any(Penalty.class)))
                .thenReturn(true);
        penaltyService.createNewPenalty(penaltyProbable);
        Mockito.verify(clockworkService, Mockito.times(1))
                .schedulePenaltyEntityAtGrowthTime(Mockito.any(Penalty.class), Mockito.any());

    }

    @Test
    public void testReconcileWhenPenaltyIsDisqualified() {
        Penalty penaltyInstance = PenaltyTestObjectUtil.getPenaltyInstance();
        Mockito.when(penaltyRepository.findReconEligiblePenalties(Mockito.any(PenaltyDateRangeSearchRequest.class)))
                .thenReturn(List.of(PenaltyTestObjectUtil.getPenaltyEntity()));
        Mockito.when(penaltyRepository.findPenalties(Mockito.any(PenaltyBasicSearchRequest.class)))
                .thenReturn(List.of(PenaltyTestObjectUtil.getPenaltyEntity()));
        Mockito.when(penaltyEvaluationService.isQualifiedForDisQualifiedCriteria(Mockito.any(Penalty.class)))
                .thenReturn(true);
        penaltyService.reconcile(PenaltyDateRangeSearchRequest.builder()
                .penaltyClassId(penaltyInstance.getPenaltyClassId())
                .dateRangeRequest(DateRangeRequest.builder()
                        .fromDate(penaltyInstance.getCreatedAt())
                        .toDate(penaltyInstance.getCreatedAt())
                        .build())
                .build());
        Mockito.verify(clockworkService, Mockito.times(0))
                .schedulePenaltyEntityAtGrowthTime(Mockito.any(Penalty.class), Mockito.any(Date.class));

    }


    @Test
    public void testReconcileWhenPenaltyIsResolved() {
        Penalty penaltyInstance = PenaltyTestObjectUtil.getPenaltyInstance();
        Mockito.when(penaltyRepository.findReconEligiblePenalties(Mockito.any(PenaltyDateRangeSearchRequest.class)))
                .thenReturn(List.of(PenaltyTestObjectUtil.getPenaltyEntity()));
        Mockito.when(penaltyRepository.findPenalties(Mockito.any(PenaltyBasicSearchRequest.class)))
                .thenReturn(List.of(PenaltyTestObjectUtil.getPenaltyEntity()));
        Mockito.when(penaltyEvaluationService.isQualifiedForDisQualifiedCriteria(Mockito.any(Penalty.class)))
                .thenReturn(false);
        Mockito.when(penaltyClientService.isPenaltyResolved(Mockito.anyString(), Mockito.anyString())).thenReturn(true);
        PenaltyDisbursementEntity disbursementEntity = PenaltyTestObjectUtil.getPenaltyDisbursementEntity();
        disbursementEntity.setStatus(PenaltyDisbursementState.COMPLETED);

        Mockito.when(penaltyDisbursementService.initiateDisbursement(Mockito.any(Penalty.class)))
                .thenReturn(PenaltyDisbursementConvertors.convert(disbursementEntity));
        penaltyService.reconcile(PenaltyDateRangeSearchRequest.builder()
                .penaltyClassId(penaltyInstance.getPenaltyClassId())
                .dateRangeRequest(DateRangeRequest.builder()
                        .fromDate(penaltyInstance.getCreatedAt())
                        .toDate(penaltyInstance.getCreatedAt())
                        .build())
                .build());
        Mockito.verify(penaltyRepository, Mockito.times(1))
                .updatePenaltyDisbursementStatus(Mockito.anyString(), Mockito.anyString(),Mockito.any());
        Mockito.verify(clockworkService, Mockito.times(0))
            .schedulePenaltyEntityAtGrowthTime(Mockito.any(Penalty.class), Mockito.any(Date.class));
    }
    @Test
    public void testReconcileWhenPenaltyIsNotResolvedScheduleForNextGrowthTime() {
        Penalty penaltyInstance = PenaltyTestObjectUtil.getPenaltyInstance();
        Mockito.when(penaltyRepository.findReconEligiblePenalties(Mockito.any(PenaltyDateRangeSearchRequest.class)))
                .thenReturn(List.of(PenaltyTestObjectUtil.getPenaltyEntity()));
        Mockito.when(penaltyRepository.findPenalties(Mockito.any(PenaltyBasicSearchRequest.class)))
                .thenReturn(List.of(PenaltyTestObjectUtil.getPenaltyEntity()));
        Mockito.when(penaltyEvaluationService.isQualifiedForDisQualifiedCriteria(Mockito.any(Penalty.class)))
                .thenReturn(false);
        Mockito.when(penaltyEvaluationService.isQualifiedForCriteria(Mockito.any(Penalty.class)))
                .thenReturn(true);
        Mockito.when(penaltyClientService.isPenaltyResolved(Mockito.anyString(), Mockito.anyString())).thenReturn(false);
        PenaltyDisbursementEntity disbursementEntity = PenaltyTestObjectUtil.getPenaltyDisbursementEntity();
        disbursementEntity.setStatus(PenaltyDisbursementState.COMPLETED);

        penaltyService.reconcile(PenaltyDateRangeSearchRequest.builder()
                .penaltyClassId(penaltyInstance.getPenaltyClassId())
                .dateRangeRequest(DateRangeRequest.builder()
                        .fromDate(penaltyInstance.getCreatedAt())
                        .toDate(penaltyInstance.getCreatedAt())
                        .build())
                .build());
        Mockito.verify(penaltyRepository, Mockito.times(0))
                .updatePenaltyDisbursementStatus(Mockito.anyString(), Mockito.anyString(),Mockito.any());
        Mockito.verify(clockworkService, Mockito.times(1))
                .schedulePenaltyEntityAtGrowthTime(Mockito.any(Penalty.class), Mockito.any());
        Mockito.verify(penaltyRepository, Mockito.times(1))
                .updatePenaltyFinalAmount(Mockito.anyString(), Mockito.anyString(), Mockito.anyLong());

    }

    @Test
    public void testFetchTenantPenalties(){
        Mockito.when(penaltyRepository.findPenalties(Mockito.any(PenaltyDateRangeSearchRequest.class)))
                .thenReturn(List.of(PenaltyTestObjectUtil.getPenaltyEntity()));
        Mockito.when(penaltyClassService.getClassFor(Mockito.anyString()))
                .thenReturn(Optional.of(PenaltyTestObjectUtil.getPenaltyClass()));
        List<PenaltyResponse> listOfPenalty = penaltyService.fetchTenantPenalties(
                PenaltyDateRangeSearchRequest.builder()
                        .penaltyClassId(PenaltyTestObjectUtil.getPenaltyInstance()
                                .getPenaltyClassId())
                        .dateRangeRequest(DateRangeRequest.builder()
                                .fromDate(PenaltyTestObjectUtil.getPenaltyInstance()
                                        .getCreatedAt())
                                .toDate(PenaltyTestObjectUtil.getPenaltyInstance()
                                        .getCreatedAt())
                                .build())
                        .build());
        Assertions.assertFalse(listOfPenalty.isEmpty());
    }

    @Test
    public void testIfActivePenaltyExist(){
        boolean result = penaltyService.isActivePenaltyExistAgainstClass("testClassId");
        Assertions.assertFalse(result);
    }


    @Test
    void testPenaltyCreationNotification(){
        Mockito.when(penaltyClassService.getClassFor(Mockito.anyString()))
            .thenReturn(Optional.of(PenaltyTestObjectUtil.getPenaltyClass()));
        Penalty penalty = PenaltyTestObjectUtil.getPenaltyInstance();
        Assertions.assertDoesNotThrow(()->penaltyService.sendNotificationForPenaltiesCreation(List.of(penalty),
            EmailCommunicationRequest.builder()
                .emailIDs(Set.of("phonepe.com"))
                .build(), DateRangeRequest.builder()
                    .fromDate(new Date())
                    .toDate(new Date())
                .build()));
    }

    @Test
    void testPenaltyGrowingNotification(){
        Mockito.when(penaltyClassService.getClassFor(Mockito.anyString()))
            .thenReturn(Optional.of(PenaltyTestObjectUtil.getPenaltyClass()));
        Penalty penalty = PenaltyTestObjectUtil.getPenaltyInstance();
        Assertions.assertDoesNotThrow(()->penaltyService.sendNotificationForPenaltiesCreation(List.of(penalty),
            EmailCommunicationRequest.builder()
                .emailIDs(Set.of("phonepe.com"))
                .build(), DateRangeRequest.builder()
                .fromDate(new Date())
                .toDate(new Date())
                .build()));
    }

    @Test
    void testPenaltySendNotificationForPenaltyClassNotPresent() throws JsonProcessingException {
        Penalty penalty = PenaltyTestObjectUtil.getPenaltyInstance();
        MockingUtils.setUpZencastApiForFailing();
        final var errorCode = Assertions.assertThrows(DisputeException.class, ()->penaltyService.sendNotificationForPenaltiesCreation(List.of(penalty),
            EmailCommunicationRequest.builder()
                .emailIDs(Set.of("phonepe.com"))
                .build(), DateRangeRequest.builder()
                .fromDate(new Date())
                .toDate(new Date())
                .build()));
        Assertions.assertEquals("penalty class not present",errorCode.getMessage());
    }

    @Test
    void testPenaltySendNotificationForZencastApiFailure(){
        Mockito.when(penaltyClassService.getClassFor(Mockito.anyString()))
            .thenReturn(Optional.of(PenaltyTestObjectUtil.getPenaltyClass()));
        Penalty penalty = PenaltyTestObjectUtil.getPenaltyInstance();
        Mockito.doThrow(DisputeException.class).when(notificationService).sendNotification(Mockito.any(),
            Mockito.any(), Mockito.any(), Mockito.any());
        Assertions.assertDoesNotThrow(()->penaltyService.sendNotificationForPenaltiesCreation(List.of(penalty),
            EmailCommunicationRequest.builder()
                .emailIDs(Set.of("phonepe.com"))
                .build(), DateRangeRequest.builder()
                .fromDate(new Date())
                .toDate(new Date())
                .build()));
        Mockito.verify(eventIngester, Mockito.times(1)).generateEvent(
            Mockito.any()
        );
    }

    @Test
    void testPenaltySendNotificationForRuntimeException(){
        Mockito.when(penaltyClassService.getClassFor(Mockito.anyString()))
            .thenReturn(Optional.of(PenaltyTestObjectUtil.getPenaltyClass()));
        Penalty penalty = PenaltyTestObjectUtil.getPenaltyInstance();
        Mockito.doThrow(RuntimeException.class).when(notificationService).sendNotification(Mockito.any(),
            Mockito.any(), Mockito.any(), Mockito.any());
        Assertions.assertDoesNotThrow(()->penaltyService.sendNotificationForPenaltiesCreation(List.of(penalty),
            EmailCommunicationRequest.builder()
                .emailIDs(Set.of("phonepe.com"))
                .build(), DateRangeRequest.builder()
                .fromDate(new Date())
                .toDate(new Date())
                .build()));
        Mockito.verify(eventIngester, Mockito.times(1)).generateEvent(
            Mockito.any());
    }
}