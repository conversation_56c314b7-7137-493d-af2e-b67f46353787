package com.phonepe.central.stratos.penalty.server.calculation.strategy;

import static java.time.Duration.ZERO;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.phonepe.central.stratos.penalty.growth.GrowthUnit;
import com.phonepe.central.stratos.penalty.growth.PenaltyGrowthRate;
import com.phonepe.central.stratos.penalty.growth.calculation.impl.PenaltyConversionDateFixedAmountCalculation;
import com.phonepe.central.stratos.penalty.growth.calculation.impl.PenaltyConversionDateFixedPercentageCalculation;
import com.phonepe.central.stratos.penalty.growth.calculation.impl.TransactionDateFixedAmountCalculation;
import com.phonepe.central.stratos.penalty.growth.calculation.impl.TransactionDateFixedPercentageCalculation;
import com.phonepe.central.stratos.penalty.meta.PenaltyClassDetail;
import com.phonepe.central.stratos.penalty.meta.PenaltyCriteria;
import com.phonepe.central.stratos.penalty.server.PenaltyTestObjectUtil;
import com.phonepe.central.stratos.penalty.server.calculation.strategy.impl.PenaltyConversionDateFixedAmountCalculationStrategy;
import com.phonepe.central.stratos.penalty.server.calculation.strategy.impl.PenaltyConversionDateFixedPercentageCalculationStrategy;
import com.phonepe.central.stratos.penalty.server.calculation.strategy.impl.TransactionDateFixedAmountCalculationStrategy;
import com.phonepe.central.stratos.penalty.server.calculation.strategy.impl.TransactionDateFixedPercentageCalculationStrategy;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.PenaltyEntity;
import com.phonepe.central.stratos.penalty.server.util.PenaltyUtil;
import com.phonepe.error.configurator.model.service.ResourceErrorService;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.ErrorConfiguratorBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.clients.PaymentsTxnlClient;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.models.payments.pay.ReceivedPayment;
import com.phonepe.models.payments.pay.SentPayment;
import com.phonepe.models.payments.pay.TransactionDetail;
import com.phonepe.models.response.GenericResponse;
import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

@Slf4j
public class PenaltyCalculationStrategyTest extends ErrorConfiguratorBaseTest {

    private final PenaltyClassDetail classDetail = Mockito.mock(PenaltyClassDetail.class);
    private final PenaltyEntity entity = Mockito.mock(PenaltyEntity.class);

    @BeforeEach
    public void setUp() throws MalformedURLException, URISyntaxException {
        Mockito.when(entity.getTransactionAmount())
                .thenReturn(200L);
        Mockito.when(entity.getInitialPenaltyAmount())
                .thenReturn(100L);
        Mockito.when(entity.getQualifiedAt())
                .thenReturn(LocalDateTime.now()
                        .minusDays(2));
        Mockito.when(classDetail.getGrowthRate())
                .thenReturn(PenaltyGrowthRate.builder()
                        .unit(GrowthUnit.DAY)
                        .build());
        Mockito.when(classDetail.getCriteria())
                .thenReturn(PenaltyCriteria.builder()
                        .leeway(ZERO)
                        .build());
        ResourceErrorService<StratosErrorCodeKey> resourceErrorService = getResourceErrorService();
        log.error("resourceErrorService  : {}", resourceErrorService);
        DisputeExceptionUtil.init(resourceErrorService);
    }

    @Test
    void testPenaltyConversionDateFixedPercentageCalculationStrategy() {
        PenaltyConversionDateFixedPercentageCalculation calculation = PenaltyConversionDateFixedPercentageCalculation.builder()
                .percentage(BigDecimal.valueOf(10))
                .build();
        PenaltyCalculationStrategy strategy = new PenaltyConversionDateFixedPercentageCalculationStrategy();

        Long penaltyAmount = strategy.calculate(classDetail, entity, calculation);
        assertEquals(140L, penaltyAmount);
    }

    @Test
    void testPenaltyConversionDateFixedAmountCalculationStrategy() {
        PenaltyConversionDateFixedAmountCalculation calculation = PenaltyConversionDateFixedAmountCalculation.builder()
                .amount(BigDecimal.valueOf(50))
                .build();
        PenaltyCalculationStrategy strategy = new PenaltyConversionDateFixedAmountCalculationStrategy();

        Long penaltyAmount = strategy.calculate(classDetail, entity, calculation);
        assertEquals(200L, penaltyAmount);
    }

    @Test
    void testTransactionDateFixedPercentageCalculationStrategyWithException() {
        PaymentsTxnlClient client = Mockito.mock(PaymentsTxnlClient.class);
        TransactionDateFixedPercentageCalculation calculation = TransactionDateFixedPercentageCalculation.builder()
                .percentage(BigDecimal.valueOf(5))
                .build();
        PenaltyCalculationStrategy strategy = new TransactionDateFixedPercentageCalculationStrategy(client);
        Mockito.when(client.getTransactionDetails(Mockito.any()))
                .thenReturn(GenericResponse.<TransactionDetail>builder()
                        .success(false)
                        .build());

        Assertions.assertThrows(DisputeException.class, () -> {
            strategy.calculate(PenaltyTestObjectUtil.getPenaltyClassDetail(), PenaltyTestObjectUtil.getPenaltyEntity(),
                    calculation);
        });
    }

    @Test
    void testTransactionDateFixedPercentageCalculationStrategy() {
        PaymentsTxnlClient client = Mockito.mock(PaymentsTxnlClient.class);
        TransactionDateFixedPercentageCalculation calculation = TransactionDateFixedPercentageCalculation.builder()
                .percentage(BigDecimal.valueOf(5))
                .build();
        PenaltyCalculationStrategy strategy = new TransactionDateFixedPercentageCalculationStrategy(client);
        Date today = new Date();
        var receivedAt = PenaltyUtil.addDaystoDate(today, -3);
        Mockito.when(client.getTransactionDetails(Mockito.any()))
                .thenReturn(GenericResponse.<TransactionDetail>builder()
                        .success(true)
                        .data(TransactionDetail.builder()
                                .sentPayment(SentPayment.builder()
                                        .sentAt(receivedAt)
                                        .build())
                                .receivedPayment(ReceivedPayment.builder()
                                        .receivedAt(receivedAt)
                                        .build())
                                .build())
                        .build());
        Long penaltyAmount = strategy.calculate(classDetail, entity, calculation);
        assertEquals(130L, penaltyAmount);
    }

    @Test
    void testTransactionDateFixedAmountCalculationStrategyWithException() {
        PaymentsTxnlClient client = Mockito.mock(PaymentsTxnlClient.class);
        TransactionDateFixedAmountCalculation calculation = TransactionDateFixedAmountCalculation.builder()
                .amount(BigDecimal.valueOf(30))
                .build();
        PenaltyCalculationStrategy strategy = new TransactionDateFixedAmountCalculationStrategy(client);
        Mockito.when(client.getTransactionDetails(Mockito.any()))
                .thenReturn(GenericResponse.<TransactionDetail>builder()
                        .success(false)
                        .build());

        Assertions.assertThrows(DisputeException.class, () -> {
            strategy.calculate(PenaltyTestObjectUtil.getPenaltyClassDetail(), PenaltyTestObjectUtil.getPenaltyEntity(),
                    calculation);
        });
    }

    @Test
    void testTransactionDateFixedAmountCalculationStrategy() {
        PaymentsTxnlClient client = Mockito.mock(PaymentsTxnlClient.class);
        TransactionDateFixedAmountCalculation calculation = TransactionDateFixedAmountCalculation.builder()
                .amount(BigDecimal.valueOf(30))
                .build();
        PenaltyCalculationStrategy strategy = new TransactionDateFixedAmountCalculationStrategy(client);
        Date today = new Date();
        var receivedAt = PenaltyUtil.addDaystoDate(today, -4);
        Mockito.when(client.getTransactionDetails(Mockito.any()))
                .thenReturn(GenericResponse.<TransactionDetail>builder()
                        .success(true)
                        .data(TransactionDetail.builder()
                                .sentPayment(SentPayment.builder()
                                        .sentAt(receivedAt)
                                        .build())
                                .receivedPayment(ReceivedPayment.builder()

                                        .receivedAt(receivedAt)
                                        .build())
                                .build())
                        .build());

        Long penaltyAmount = strategy.calculate(classDetail, entity, calculation);
        assertEquals(220L, penaltyAmount);
    }
}