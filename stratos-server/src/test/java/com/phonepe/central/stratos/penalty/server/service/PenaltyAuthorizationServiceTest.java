package com.phonepe.central.stratos.penalty.server.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import com.phonepe.error.configurator.model.service.ResourceErrorService;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.ErrorConfiguratorBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.utils.PenaltyPermissionsConstants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.olympus.im.client.config.OlympusIMClientConfig;
import com.phonepe.olympus.im.client.config.OlympusIMClientConfig.AuthConfig;
import com.phonepe.olympus.im.models.security.ServiceUserPrincipal;
import com.phonepe.olympus.im.models.authz.enums.TenantType;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * <AUTHOR>
 */

@ExtendWith(MockitoExtension.class)
public class PenaltyAuthorizationServiceTest extends ErrorConfiguratorBaseTest {

    @Mock
    private OlympusIMClient olympusIMClient;

    @Mock
    private PenaltyClassService penaltyClassService;

    @Mock
    private ResourceErrorService<StratosErrorCodeKey> resourceErrorService;


    private PenaltyAuthorizationService penaltyAuthorizationService;

    @BeforeEach
    public void setup() throws MalformedURLException, URISyntaxException {
        MockitoAnnotations.openMocks(this);
        Mockito.when(olympusIMClient.getOlympusIMConfig())
                .thenReturn(OlympusIMClientConfig.builder().authConfig(AuthConfig.builder().componentInstanceId("STRATOS").build()).build());
        penaltyAuthorizationService = new PenaltyAuthorizationService(olympusIMClient, penaltyClassService);
        ResourceErrorService<StratosErrorCodeKey> resourceErrorService = getResourceErrorService();
        DisputeExceptionUtil.init(resourceErrorService);
    }

    @Test
    public void testGetAuthorizedTenantInfoWithValidPermissions() {
        UserAuthDetails userAuthDetails = mock(UserAuthDetails.class);
        List<TenantInfo> result = getAuthTenantInfoList(userAuthDetails,"permission");

        assertEquals(1, result.size());
        assertEquals("Tenant1", result.get(0).getName());
        assertEquals("SubCategory1", result.get(0).getSubCategory());
    }



    @Test
    public void testGetAuthorizedTenantInfoWithEmptyPermissions() {
        UserAuthDetails userAuthDetails = mock(UserAuthDetails.class);
        when(userAuthDetails.getEncodedTenantPermissionsForComponentInstances()).thenReturn(Collections.emptyMap());

        List<TenantInfo> result = penaltyAuthorizationService.getAuthorizedTenantInfo(userAuthDetails, "permission");

        assertEquals(0, result.size());
    }

    @Test
    public void testGetAuthorizedTenantInfoWithInvalidTenantInfoFormat() {
        UserAuthDetails userAuthDetails = mock(UserAuthDetails.class);
        Map<String, Map<TenantType, Map<String, String>>> encodedTenantPermission = new HashMap<>();
        Map<String, String> customPermission = new HashMap<>();
        customPermission.put("CIG_STRATOS_CIG_PENALTY_InvalidFormat", "permission");
        encodedTenantPermission.put("STRATOS", Map.of(TenantType.CUSTOM, customPermission));

        when(userAuthDetails.getEncodedTenantPermissionsForComponentInstances()).thenReturn(encodedTenantPermission);

        List<TenantInfo> result = penaltyAuthorizationService.getAuthorizedTenantInfo(userAuthDetails, "permission");

        assertEquals(0, result.size());
    }

    @Test
    public void testGetAuthorizedTenantInfoWithPermissionVerificationFailure() {
        UserAuthDetails userAuthDetails = mock(UserAuthDetails.class);
        Map<String, Map<TenantType, Map<String, String>>> encodedTenantPermission = new HashMap<>();
        Map<String, String> customPermission = new HashMap<>();
        customPermission.put("CIG_STRATOS_CIG_PENALTY_Tenant1_SubCategory1", "permission");
        encodedTenantPermission.put("STRATOS", Map.of(TenantType.CUSTOM, customPermission));

        when(userAuthDetails.getEncodedTenantPermissionsForComponentInstances()).thenReturn(encodedTenantPermission);
        when(olympusIMClient.verifyPermissionForTenant(userAuthDetails, "CIG_STRATOS_CIG_PENALTY_Tenant1_SubCategory1",
                TenantType.CUSTOM, "permission")).thenReturn(false);

        List<TenantInfo> result = penaltyAuthorizationService.getAuthorizedTenantInfo(userAuthDetails, "permission");

        assertEquals(0, result.size());
    }
    @Test
    public void testIsTenantAuthorizedToCreatePenaltyClassWithAuthorizedTenant() {
        UserAuthDetails userAuthDetails = mock(UserAuthDetails.class);
        List<TenantInfo> tenantInfoList = getAuthTenantInfoList(userAuthDetails, PenaltyPermissionsConstants.PENALTY_CLASS_CREATE_PERMISSION);
        boolean result = penaltyAuthorizationService.isTenantAuthorizedToCreatePenaltyClass(userAuthDetails, tenantInfoList.get(0));
        assertTrue(result);
    }

    @Test
    public void testIsTenantAuthorizedToCreatePenaltyClassWithUnauthorizedTenant() {
        UserAuthDetails userAuthDetails = mock(UserAuthDetails.class);
        List<TenantInfo> tenantInfoList = getAuthTenantInfoList(userAuthDetails,PenaltyPermissionsConstants.PENALTY_CLASS_CREATE_PERMISSION);
        boolean result = penaltyAuthorizationService.isTenantAuthorizedToCreatePenaltyClass(userAuthDetails, TenantInfo.builder().name("tenantName").subCategory("subcategory").build());
        assertFalse(result);
    }

    @Test
    public void testIsTenantAuthorizedToRegisterProbableWithAuthorizedTenant() {
        UserAuthDetails userAuthDetails = mock(UserAuthDetails.class);
        List<TenantInfo> tenantInfoList = getAuthTenantInfoList(userAuthDetails, PenaltyPermissionsConstants.PENALTY_PROBABLE_CREATE_PERMISSION);
        Mockito.when(penaltyClassService.getTenantInfo("penaltyClassId")).thenReturn(tenantInfoList.get(0));
        boolean result = penaltyAuthorizationService.isTenantAuthorizedToRegisterProbable(userAuthDetails, "penaltyClassId");
        assertTrue(result);
    }

    @Test
    public void testIsTenantAuthorizedToRegisterProbableWithUnauthorizedTenant() {
        UserAuthDetails userAuthDetails = mock(UserAuthDetails.class);
        List<TenantInfo> tenantInfoList = getAuthTenantInfoList(userAuthDetails, PenaltyPermissionsConstants.PENALTY_CLASS_CREATE_PERMISSION);
        boolean result = penaltyAuthorizationService.isTenantAuthorizedToRegisterProbable(userAuthDetails, "penaltyClassId");
        assertFalse(result);
    }



    @Test
    public void testIsTenantAuthorizedToViewProbableWithAuthorizedTenant() {
        UserAuthDetails userAuthDetails = mock(UserAuthDetails.class);
        List<TenantInfo> tenantInfoList = getAuthTenantInfoList(userAuthDetails, PenaltyPermissionsConstants.PENALTY_PROBABLE_VIEW_PERMISSION);
        Mockito.when(penaltyClassService.getTenantInfo("penaltyClassId")).thenReturn(tenantInfoList.get(0));
        boolean result = penaltyAuthorizationService.isTenantAuthorizedToViewProbable(userAuthDetails, "penaltyClassId");
        assertTrue(result);
    }

    @Test
    public void testIsTenantAuthorizedToViewProbableWithUnauthorizedTenant() {
        UserAuthDetails userAuthDetails = mock(UserAuthDetails.class);
        List<TenantInfo> tenantInfoList = getAuthTenantInfoList(userAuthDetails, PenaltyPermissionsConstants.PENALTY_CLASS_CREATE_PERMISSION);
        boolean result = penaltyAuthorizationService.isTenantAuthorizedToViewProbable(userAuthDetails, "penaltyClassId");
        assertFalse(result);
    }


    @Test
    public void testIsTenantAuthorizedToViewPenaltyInstanceWithAuthorizedTenant() {
        UserAuthDetails userAuthDetails = mock(UserAuthDetails.class);
        List<TenantInfo> tenantInfoList = getAuthTenantInfoList(userAuthDetails, PenaltyPermissionsConstants.PENALTY_INSTANCE_VIEW_PERMISSION);
        Mockito.when(penaltyClassService.getTenantInfo("penaltyClassId")).thenReturn(tenantInfoList.get(0));
        boolean result = penaltyAuthorizationService.isTenantAuthorizedToViewPenaltyInstance(userAuthDetails, "penaltyClassId");
        assertTrue(result);
    }

    @Test
    public void testIsTenantAuthorizedToViewPenaltyInstanceWithUnauthorizedTenant() {
        UserAuthDetails userAuthDetails = mock(UserAuthDetails.class);
        List<TenantInfo> tenantInfoList = getAuthTenantInfoList(userAuthDetails, PenaltyPermissionsConstants.PENALTY_CLASS_CREATE_PERMISSION);
        boolean result = penaltyAuthorizationService.isTenantAuthorizedToViewPenaltyInstance(userAuthDetails, "penaltyClassId");
        assertFalse(result);
    }

    @Test
    public void testGetPermissibleTenants() {
        UserAuthDetails userAuthDetails = mock(UserAuthDetails.class);
        ServiceUserPrincipal serviceUserPrincipal = mock(ServiceUserPrincipal.class);
        List<TenantInfo> tenantInfoList = getAuthTenantInfoList(userAuthDetails, PenaltyPermissionsConstants.PENALTY_INSTANCE_VIEW_PERMISSION);
        Mockito.when(serviceUserPrincipal.getUserAuthDetails()).thenReturn(userAuthDetails);
        List<TenantInfo> result = penaltyAuthorizationService.getPermissibleTenants(serviceUserPrincipal,
                tenantInfoList.get(0)
                        .getName(), tenantInfoList.get(0)
                        .getSubCategory(), PenaltyPermissionsConstants.PENALTY_INSTANCE_VIEW_PERMISSION);
        assertEquals(result,tenantInfoList);
    }

    private List<TenantInfo> getAuthTenantInfoList(UserAuthDetails userAuthDetails,String permission) {
        Map<String, Map<TenantType, Map<String, String>>> encodedTenantPermission = new HashMap<>();
        Map<String, String> customPermission = new HashMap<>();
        customPermission.put("CIG_STRATOS_CIG_PENALTY_Tenant1_SubCategory1", permission);
        encodedTenantPermission.put("STRATOS", Map.of(TenantType.CUSTOM, customPermission));

        when(userAuthDetails.getEncodedTenantPermissionsForComponentInstances()).thenReturn(encodedTenantPermission);
        when(olympusIMClient.verifyPermissionForTenant(userAuthDetails, "CIG_STRATOS_CIG_PENALTY_Tenant1_SubCategory1",
                TenantType.CUSTOM, permission)).thenReturn(true);

        List<TenantInfo> result = penaltyAuthorizationService.getAuthorizedTenantInfo(userAuthDetails, permission);
        return result;
    }

}
