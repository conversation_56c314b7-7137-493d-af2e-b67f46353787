package com.phonepe.central.stratos.penalty.server.resources;

import com.phonepe.central.stratos.penalty.request.penaltyclass.PenaltyClassBasicUpdateRequest;
import com.phonepe.central.stratos.penalty.request.penaltyclass.PenaltyClassCreateRequest;
import com.phonepe.central.stratos.penalty.request.penaltyclass.PenaltyClassSearchRequest;
import com.phonepe.central.stratos.penalty.server.PenaltyTestObjectUtil;
import com.phonepe.central.stratos.penalty.server.service.PenaltyAuthorizationService;
import com.phonepe.central.stratos.penalty.server.service.PenaltyClassService;
import com.phonepe.error.configurator.model.service.ResourceErrorService;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.ErrorConfiguratorBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Optional;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.Status;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class PenaltyClassResourceTest extends ErrorConfiguratorBaseTest {

    @Mock
    private ResourceErrorService<StratosErrorCodeKey> resourceErrorService;
    @Mock
    private PenaltyAuthorizationService penaltyAuthorizationService;

    @Mock
    private ServiceUserPrincipal serviceUserPrincipal;

    private PenaltyClassResource penaltyClassResource;
    @Mock
    private PenaltyClassService penaltyClassService;

    @BeforeEach
    public void setup() throws MalformedURLException, URISyntaxException {
        MockitoAnnotations.openMocks(this);
        penaltyClassResource = new PenaltyClassResource(penaltyClassService, penaltyAuthorizationService);
        ResourceErrorService<StratosErrorCodeKey> resourceErrorService = getResourceErrorService();
        DisputeExceptionUtil.init(resourceErrorService);
    }

    @Test
    public void testTenantNotAuthorizedToCreateClass() {
        Mockito.when(penaltyAuthorizationService.isTenantAuthorizedToCreatePenaltyClass(Mockito.any(), Mockito.any()))
                .thenReturn(false);
        PenaltyClassCreateRequest penaltyClassCreateRequest = PenaltyClassCreateRequest.builder()
                .details(List.of(PenaltyTestObjectUtil.getPenaltyClassDetail()))
                .tenant(PenaltyTestObjectUtil.getTenantInfo())
                .escalationMatrix(PenaltyTestObjectUtil.getEscalationMatrix())
                .build();
        Response result = penaltyClassResource.createPenaltyClassConfig(serviceUserPrincipal, "authToken",
                penaltyClassCreateRequest);
        Assertions.assertEquals(Response.Status.FORBIDDEN.getStatusCode(), result.getStatus());
    }

    @Test
    public void testTenantNotAuthorizedToCreateClassWithDisputeException() {
        Mockito.when(penaltyAuthorizationService.isTenantAuthorizedToCreatePenaltyClass(Mockito.any(), Mockito.any()))
                .thenThrow(DisputeException.builder()
                        .build());
        PenaltyClassCreateRequest penaltyClassCreateRequest = PenaltyClassCreateRequest.builder()
                .details(List.of(PenaltyTestObjectUtil.getPenaltyClassDetail()))
                .tenant(PenaltyTestObjectUtil.getTenantInfo())
                .escalationMatrix(PenaltyTestObjectUtil.getEscalationMatrix())
                .build();
        Assertions.assertThrows(DisputeException.class, () ->{
            penaltyClassResource.createPenaltyClassConfig(serviceUserPrincipal, "authToken", penaltyClassCreateRequest);
        });

    }

    @Test
    public void testTenantNotAuthorizedToCreateClassWithGenericException() {
        Mockito.when(penaltyAuthorizationService.isTenantAuthorizedToCreatePenaltyClass(Mockito.any(), Mockito.any()))
                .thenThrow(new RuntimeException("Generic Exception"));
        PenaltyClassCreateRequest penaltyClassCreateRequest = PenaltyClassCreateRequest.builder()
                .details(List.of(PenaltyTestObjectUtil.getPenaltyClassDetail()))
                .tenant(PenaltyTestObjectUtil.getTenantInfo())
                .escalationMatrix(PenaltyTestObjectUtil.getEscalationMatrix())
                .build();
        Assertions.assertThrows(DisputeException.class, () ->{
            penaltyClassResource.createPenaltyClassConfig(serviceUserPrincipal, "authToken", penaltyClassCreateRequest);
        });

    }

    @Test
    public void testTenantToCreateClassContent() {
        Mockito.when(penaltyAuthorizationService.isTenantAuthorizedToCreatePenaltyClass(Mockito.any(), Mockito.any()))
                .thenReturn(true);
        PenaltyClassCreateRequest penaltyClassCreateRequest = PenaltyClassCreateRequest.builder()
                .details(List.of(PenaltyTestObjectUtil.getPenaltyClassDetail()))
                .tenant(PenaltyTestObjectUtil.getTenantInfo())
                .escalationMatrix(PenaltyTestObjectUtil.getEscalationMatrix())
                .build();
        Mockito.when(penaltyClassService.createConfig(Mockito.any()))
                .thenReturn(Optional.of(PenaltyTestObjectUtil.getPenaltyClass()));
        Response result = penaltyClassResource.createPenaltyClassConfig(serviceUserPrincipal, "authToken",
                penaltyClassCreateRequest);
        Assertions.assertEquals(Response.Status.OK.getStatusCode(), result.getStatus());
    }

    @Test
    public void testTenantToCreateClassNoContent() {
        Mockito.when(penaltyAuthorizationService.isTenantAuthorizedToCreatePenaltyClass(Mockito.any(), Mockito.any()))
                .thenReturn(true);
        PenaltyClassCreateRequest penaltyClassCreateRequest = PenaltyClassCreateRequest.builder()
                .details(List.of(PenaltyTestObjectUtil.getPenaltyClassDetail()))
                .tenant(PenaltyTestObjectUtil.getTenantInfo())
                .escalationMatrix(PenaltyTestObjectUtil.getEscalationMatrix())
                .build();
        Mockito.when(penaltyClassService.createConfig(Mockito.any()))
                .thenReturn(Optional.empty());
        Response result = penaltyClassResource.createPenaltyClassConfig(serviceUserPrincipal, "authToken",
                penaltyClassCreateRequest);
        Assertions.assertEquals(Status.NO_CONTENT.getStatusCode(), result.getStatus());
    }

    @Test
    public void testGetPenaltyClassListNotAuthorizedTenant() {
        Response result = penaltyClassResource.getPenaltyClasses(serviceUserPrincipal,
                PenaltyClassSearchRequest.builder()
                        .build());
        Assertions.assertEquals(Response.Status.FORBIDDEN.getStatusCode(), result.getStatus());
    }

    @Test
    public void testGetPenaltyClassList() {
        Mockito.when(penaltyAuthorizationService.getPermissibleTenants(Mockito.any(), Mockito.any(),Mockito.any(),Mockito.anyString()))
                .thenReturn(List.of(PenaltyTestObjectUtil.getTenantInfo()));
        Response result = penaltyClassResource.getPenaltyClasses(serviceUserPrincipal,
                PenaltyClassSearchRequest.builder()
                        .build());
        Assertions.assertEquals(Response.Status.OK.getStatusCode(), result.getStatus());
    }

    @Test
    public void testGetPenaltyClassListWithException() {
        Mockito.when(penaltyAuthorizationService.getPermissibleTenants(Mockito.any(), Mockito.any(),Mockito.any(),Mockito.anyString()))
                .thenThrow(new RuntimeException());
        Assertions.assertThrows(RuntimeException.class, () -> {
            penaltyClassResource.getPenaltyClasses(serviceUserPrincipal,
                    PenaltyClassSearchRequest.builder()
                            .build());
        });
    }

    @Test
    public void testUpdatePenaltyClassNotAuthorizedForTenant(){
        Response result = penaltyClassResource.updatePenaltyClass(serviceUserPrincipal,
                PenaltyClassBasicUpdateRequest.builder()
                        .build());
        Assertions.assertEquals(Response.Status.FORBIDDEN.getStatusCode(), result.getStatus());
    }
    
    @Test
    public void testUpdateClassWithValidInput(){
        Mockito.when(penaltyAuthorizationService.getPermissibleTenants(Mockito.any(), Mockito.any(),Mockito.any(),Mockito.anyString()))
                .thenReturn(List.of(PenaltyTestObjectUtil.getTenantInfo()));
        Response result = penaltyClassResource.updatePenaltyClass(serviceUserPrincipal,
                PenaltyClassBasicUpdateRequest.builder()
                        .build());
        Assertions.assertEquals(Response.Status.OK.getStatusCode(), result.getStatus());
    }

    @Test
    public void testUpdateClassListWithException() {
        Mockito.when(penaltyAuthorizationService.getPermissibleTenants(Mockito.any(), Mockito.any(),Mockito.any(),Mockito.anyString()))
                .thenThrow(new RuntimeException());
        Assertions.assertThrows(RuntimeException.class, () -> {
            Response result = penaltyClassResource.updatePenaltyClass(serviceUserPrincipal,
                    PenaltyClassBasicUpdateRequest.builder()
                            .build());
        });
    }

    @Test
    public void testWardenCallbackConfigCreationWithNotAuthorizedTenant(){
        Mockito.when(penaltyAuthorizationService.isTenantAuthorizedToCreatePenaltyClass(Mockito.any(), Mockito.any()))
                .thenReturn(false);
        Response result = penaltyClassResource.createPenaltyClassConfigWorkflowConfig(serviceUserPrincipal,"authToken","tenantName","subTenantName",0,List.of());
        Assertions.assertEquals(Response.Status.FORBIDDEN.getStatusCode(), result.getStatus());
    }

    @Test
    public void testWardenCallbackConfigCreation(){
        Mockito.when(penaltyAuthorizationService.isTenantAuthorizedToCreatePenaltyClass(Mockito.any(), Mockito.any()))
                .thenReturn(true);
        Response result = penaltyClassResource.createPenaltyClassConfigWorkflowConfig(serviceUserPrincipal,"authToken","tenantName","subTenantName",0,List.of("<EMAIL>"));
        Assertions.assertEquals(Response.Status.OK.getStatusCode(), result.getStatus());
    }

    @Test
    public void testWardenCallbackConfigCreationWithException(){
        Mockito.when(penaltyAuthorizationService.isTenantAuthorizedToCreatePenaltyClass(Mockito.any(), Mockito.any()))
                .thenThrow(DisputeException.builder()
                        .build());
        Assertions.assertThrows(RuntimeException.class, () -> {
            Response result = penaltyClassResource.createPenaltyClassConfigWorkflowConfig(serviceUserPrincipal,"authToken","tenantName","subTenantName",0,List.of("<EMAIL>"));
        });
    }


    @Test
    public void testDuplicatePenaltyClassForbidden() {
        Mockito.when(penaltyAuthorizationService.getPermissibleTenants(Mockito.any(), Mockito.any(),Mockito.any(),Mockito.anyString()))
                .thenReturn(List.of());
        Response result = penaltyClassResource.duplicatePenaltyClass(serviceUserPrincipal, "classId");
        Assertions.assertEquals(Status.FORBIDDEN.getStatusCode(), result.getStatus());
    }

    @Test
    public void testDuplicatePenaltyClassSuccess() {
        Mockito.when(penaltyAuthorizationService.getPermissibleTenants(Mockito.any(), Mockito.any(),Mockito.any(),Mockito.anyString()))
                .thenReturn(List.of(PenaltyTestObjectUtil.getTenantInfo()));
        Response result = penaltyClassResource.duplicatePenaltyClass(serviceUserPrincipal, "classId");
        Assertions.assertEquals(Status.OK.getStatusCode(), result.getStatus());
    }

    @Test
    public void testDuplicatePenaltyClassWithException() {
        Mockito.when(penaltyAuthorizationService.getPermissibleTenants(Mockito.any(), Mockito.any(),Mockito.any(),Mockito.anyString()))
                .thenReturn(List.of(PenaltyTestObjectUtil.getTenantInfo()));

        Mockito.when(penaltyClassService.duplicate(Mockito.anyString())).thenThrow(new RuntimeException("Test Exception"));
        Assertions.assertThrows(RuntimeException.class, () -> {
            penaltyClassResource.duplicatePenaltyClass(serviceUserPrincipal, "classId");
        });
    }



}
