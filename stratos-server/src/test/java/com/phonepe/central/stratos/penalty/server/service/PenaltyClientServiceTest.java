package com.phonepe.central.stratos.penalty.server.service;

import com.phonepe.central.stratos.penalty.probables.PenaltyProbable;
import com.phonepe.central.stratos.penalty.server.config.client.NoOpsClientPenaltyResolutionConfig;
import com.phonepe.central.stratos.penalty.server.config.client.PassThroughProbableClientPenaltyResolutionConfig;
import com.phonepe.central.stratos.penalty.server.config.client.ServiceClientClientPenaltyResolutionConfig;
import com.phonepe.central.stratos.penalty.server.generator.DynamicHttpExecutorBuilderFactoryGenerator;
import com.phonepe.error.configurator.model.service.ResourceErrorService;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.ErrorConfiguratorBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.common.hub.RangerHubConfiguration;
import com.phonepe.platform.http.v2.executor.factory.HttpExecutorBuilderFactory;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.util.Set;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * <AUTHOR>
 */

@ExtendWith(MockitoExtension.class)
public class PenaltyClientServiceTest extends ErrorConfiguratorBaseTest {

    @Mock
    private PenaltyClassService penaltyClassService;
    @Mock
    private RangerHubConfiguration rangerHubConfiguration;
    @Mock
    private DynamicHttpExecutorBuilderFactoryGenerator httpExecutorBuilderFactoryGenerator;
    @Mock
    private OlympusIMClient olympusIMClient;

    private PenaltyClientService penaltyClientService;

    @Mock
    private ResourceErrorService<StratosErrorCodeKey> resourceErrorService;


    @BeforeEach
    public void setup() throws MalformedURLException, URISyntaxException {
        MockitoAnnotations.openMocks(this);
        penaltyClientService = new PenaltyClientService(penaltyClassService, rangerHubConfiguration,
                httpExecutorBuilderFactoryGenerator, olympusIMClient);
        ResourceErrorService<StratosErrorCodeKey> resourceErrorService = getResourceErrorService();
        DisputeExceptionUtil.init(resourceErrorService);
    }

    @Test
    public void testIsPenaltyResolvedWithException() {
        Mockito.when(penaltyClassService.getClientResolutionConfig(
                "penaltyClassId")).thenThrow(new RuntimeException("Test exception"));
        Assertions.assertThrows(RuntimeException.class, () -> {
            penaltyClientService.isPenaltyResolved("penaltyClassId", "transactionId");
        });
    }

    @Test
    public void testIsPenaltyResolvedWithExceptionWithServiceCall() {
        Mockito.when(penaltyClassService.getClientResolutionConfig(
                "penaltyClassId")).thenReturn(ServiceClientClientPenaltyResolutionConfig.builder().rangerHubClientId("clientId").apiPath("apiPath").build());
        Assertions.assertThrows(RuntimeException.class, () -> {
            penaltyClientService.isPenaltyResolved("penaltyClassId", "transactionId");
        });
    }

    @Test
    public void testIsPenaltyResolvedWithNoOps() {
        Mockito.when(penaltyClassService.getClientResolutionConfig(
                "penaltyClassId")).thenReturn(NoOpsClientPenaltyResolutionConfig.builder()
                .build());
        boolean isResolved = penaltyClientService.isPenaltyResolved("penaltyClassId", "transactionId");
        Assertions.assertTrue(isResolved);
    }


    @Test
    public void testIsPenaltyResolvedWithPassThrough() {
        Mockito.when(penaltyClassService.getClientResolutionConfig(
                "penaltyClassId")).thenReturn(PassThroughProbableClientPenaltyResolutionConfig.builder()
                .build());
        boolean isResolved = penaltyClientService.isPenaltyResolved("penaltyClassId", "transactionId");
        Assertions.assertTrue(isResolved);
    }

    @Test
    public void testIsPenaltyResolvedWithService() {
        Mockito.when(rangerHubConfiguration.getServices()).thenReturn(Set.of(HttpConfiguration.builder()
                .clientId("rangerHubClientId")
                .build()));
        Mockito.when(httpExecutorBuilderFactoryGenerator.generateHttpExecutorBuilderFactory(Mockito.any(HttpConfiguration.class)))
                .thenReturn(Mockito.mock(HttpExecutorBuilderFactory.class));
        Mockito.when(penaltyClassService.getClientResolutionConfig(
                "penaltyClassId")).thenReturn(ServiceClientClientPenaltyResolutionConfig.builder()
                        .apiPath("apiPath")
                        .rangerHubClientId("rangerHubClientId")
                .build());
        Assertions.assertThrows(RuntimeException.class, () -> {
            penaltyClientService.isPenaltyResolved("penaltyClassId", "transactionId");
        });
        Mockito.verify(httpExecutorBuilderFactoryGenerator, Mockito.times(1))
                .generateHttpExecutorBuilderFactory(Mockito.any(HttpConfiguration.class));
        Mockito.verify(rangerHubConfiguration, Mockito.times(1))
                .getServices();
    }


    //From Here probable

    @Test
    public void testIsProbableResolvedWithException() {
        Mockito.when(penaltyClassService.getClientResolutionConfig(
                "penaltyClassId")).thenThrow(new RuntimeException("Test exception"));
        boolean isResolved = penaltyClientService.isProbableResolved(PenaltyProbable.builder().penaltyClassId("penaltyClassId").build());
        Assertions.assertFalse(isResolved);
    }

    @Test
    public void testIsProbableResolvedWithNoOps() {
        Mockito.when(penaltyClassService.getClientResolutionConfig(
                "penaltyClassId")).thenReturn(NoOpsClientPenaltyResolutionConfig.builder()
                .build());
        boolean isResolved = penaltyClientService.isProbableResolved(PenaltyProbable.builder().penaltyClassId("penaltyClassId").build());
        Assertions.assertTrue(isResolved);
    }

    @Test
    public void testIsProbableResolvedWithPassThrough() {
        Mockito.when(penaltyClassService.getClientResolutionConfig(
                "penaltyClassId")).thenReturn(PassThroughProbableClientPenaltyResolutionConfig.builder()
                .build());
        boolean isResolved = penaltyClientService.isProbableResolved(PenaltyProbable.builder().penaltyClassId("penaltyClassId").build());
        Assertions.assertFalse(isResolved);
    }

    @Test
    public void testIsProbableResolvedWithService() {
        Mockito.when(rangerHubConfiguration.getServices()).thenReturn(Set.of(HttpConfiguration.builder()
                .clientId("rangerHubClientId")
                .build()));
        Mockito.when(httpExecutorBuilderFactoryGenerator.generateHttpExecutorBuilderFactory(Mockito.any(HttpConfiguration.class)))
                .thenReturn(Mockito.mock(HttpExecutorBuilderFactory.class));
        Mockito.when(penaltyClassService.getClientResolutionConfig(
                "penaltyClassId")).thenReturn(ServiceClientClientPenaltyResolutionConfig.builder()
                .apiPath("apiPath")
                .rangerHubClientId("rangerHubClientId")
                .build());
        boolean isResolved = penaltyClientService.isProbableResolved(PenaltyProbable.builder().penaltyClassId("penaltyClassId").build());
        Assertions.assertFalse(isResolved);
        Mockito.verify(httpExecutorBuilderFactoryGenerator, Mockito.times(1))
                .generateHttpExecutorBuilderFactory(Mockito.any(HttpConfiguration.class));
        Mockito.verify(rangerHubConfiguration, Mockito.times(1))
                .getServices();
    }

}
