package com.phonepe.central.stratos.penalty.server.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.phonepe.central.stratos.notification.EmailCommunicationRequest;
import com.phonepe.central.stratos.penalty.Penalty;
import com.phonepe.central.stratos.penalty.disbursement.PenaltyDisbursement;
import com.phonepe.central.stratos.penalty.disbursement.PenaltyDisbursementState;
import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import com.phonepe.central.stratos.penalty.server.PenaltyTestObjectUtil;
import com.phonepe.central.stratos.penalty.server.config.disbursement.toa.MerchantPenaltyToADisbursementConfig;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.PenaltyDisbursementEntity;
import com.phonepe.central.stratos.penalty.server.mariadb.repository.PenaltyDisbursementRepository;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.LoadOnlyOnClassLevelBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.clients.PaymentsTxnlClient;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.models.payments.pay.PaymentProcessorResult;
import com.phonepe.models.payments.pay.PaymentRequest;
import com.phonepe.models.payments.pay.SentPayment;
import com.phonepe.models.payments.pay.TransactionDetail;
import com.phonepe.models.payments.pay.context.TransferUseCase;
import com.phonepe.models.payments.pay.destination.impl.GiftCardDestination;
import com.phonepe.models.payments.pay.receivers.Receiver;
import com.phonepe.models.response.GenericResponse;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class PenaltyDisbursementServiceTest extends LoadOnlyOnClassLevelBaseTest {

    @Mock
    private PenaltyDisbursementRepository penaltyDisbursementRepository;

    @Mock
    private PaymentsTxnlClient paymentsTxnlClient;
    @Mock
    private PenaltyClassService penaltyClassService;

    @Mock
    private PenaltyRecoveryService penaltyRecoveryService;


    private PenaltyDisbursementService penaltyDisbursementService;

    @Mock
    private NotificationService notificationService;

    @Mock
    private EventIngester eventIngester;


    @BeforeEach
    public void setup() throws MalformedURLException, URISyntaxException {
        MockitoAnnotations.openMocks(this);
        penaltyDisbursementService = new PenaltyDisbursementService(penaltyDisbursementRepository, paymentsTxnlClient,
                penaltyClassService, notificationService, penaltyRecoveryService, eventIngester);
        Mockito.when(penaltyClassService.getTenantInfo("classId"))
                .thenReturn(TenantInfo.builder()
                        .name("testTenant")
                        .subCategory("testSubCategory")
                        .build());
    }

    @Test
    public void testInitiateDisbursementWhenException() {
        Penalty penalty = PenaltyTestObjectUtil.getPenaltyInstance();
        Mockito.when(
                        penaltyDisbursementRepository.getDisbursementForTransactionId(Mockito.anyString(), Mockito.anyString()))
                .thenThrow(DisputeException.builder()
                        .build());
        Assertions.assertThrows(RuntimeException.class, () -> {
            penaltyDisbursementService.initiateDisbursement(penalty);
        });
    }

    @Test
    public void testDisbursementAlreadyExist() {
        Penalty penalty = PenaltyTestObjectUtil.getPenaltyInstance();
        Mockito.when(
                        penaltyDisbursementRepository.getDisbursementForTransactionId(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(Optional.of(PenaltyTestObjectUtil.getPenaltyDisbursementEntity()));
        PenaltyDisbursement disburseEntity = penaltyDisbursementService.initiateDisbursement(penalty);
        Assertions.assertEquals(PenaltyDisbursementState.COMPLETED, disburseEntity.getStatus());
    }

    @Test
    public void processExistingNotCompletedDisbursementWhenDestinationISEmpty() {
        Penalty penalty = PenaltyTestObjectUtil.getPenaltyInstance();
        PenaltyDisbursementEntity disbursementEntity = PenaltyTestObjectUtil.getPenaltyDisbursementEntity();
        disbursementEntity.setStatus(PenaltyDisbursementState.PROCESSING);
        Mockito.when(
                        penaltyDisbursementRepository.getDisbursementForTransactionId(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(Optional.of(disbursementEntity));

        Mockito.when(penaltyClassService.getClassDisbursementConfig(penalty.getPenaltyClassId()))
                .thenReturn(MerchantPenaltyToADisbursementConfig.builder()
                        .transferUseCase(TransferUseCase.BBPS_PENALTY)
                        .build());
        Mockito.when(penaltyDisbursementRepository.save(Mockito.any(PenaltyDisbursementEntity.class)))
                .thenReturn(Optional.of(disbursementEntity));
        Mockito.when(paymentsTxnlClient.getTransactionDetails(Mockito.anyString()))
                .thenReturn(GenericResponse.<TransactionDetail>builder()
                        .data(TransactionDetail.builder()
                                .sentPayment(SentPayment.builder()
                                        .paidFrom(List.of())
                                        .to(List.of(Receiver.builder()
                                                .merchantId("testMerchantId")
                                                .build()))
                                        .build())
                                .build())
                        .build());
        Mockito.when(paymentsTxnlClient.initToaTransaction(Mockito.any(PaymentRequest.class), Mockito.anyString(),
                        Mockito.anyString()))
                .thenReturn(GenericResponse.<PaymentProcessorResult>builder()
                        .data(PaymentProcessorResult.builder()
                                .build())
                        .build());
        PenaltyDisbursement disburseEntity = penaltyDisbursementService.initiateDisbursement(penalty);
        Assertions.assertEquals(PenaltyDisbursementState.FAILED, disburseEntity.getStatus());
        Mockito.verify(penaltyRecoveryService, Mockito.times(0))
                .initiateRecovery(Mockito.anyString(), Mockito.any(Penalty.class), Mockito.anyLong());
    }

    @Test
    public void processExistingNotCompletedDisbursement() {
        Penalty penalty = PenaltyTestObjectUtil.getPenaltyInstance();
        PenaltyDisbursementEntity disbursementEntity = PenaltyTestObjectUtil.getPenaltyDisbursementEntity();
        disbursementEntity.setStatus(PenaltyDisbursementState.PROCESSING);
        Mockito.when(
                        penaltyDisbursementRepository.getDisbursementForTransactionId(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(Optional.of(disbursementEntity));

        Mockito.when(penaltyClassService.getClassDisbursementConfig(penalty.getPenaltyClassId()))
                .thenReturn(MerchantPenaltyToADisbursementConfig.builder()
                        .destination(GiftCardDestination.builder().userId("UserId").build())
                        .transferUseCase(TransferUseCase.BBPS_PENALTY)
                        .build());
        Mockito.when(penaltyDisbursementRepository.save(Mockito.any(PenaltyDisbursementEntity.class)))
                .thenReturn(Optional.of(disbursementEntity));
        Mockito.when(paymentsTxnlClient.getTransactionDetails(Mockito.anyString()))
                .thenReturn(GenericResponse.<TransactionDetail>builder()
                        .data(TransactionDetail.builder()
                                .sentPayment(SentPayment.builder()
                                        .to(List.of(Receiver.builder()
                                                .merchantId("testMerchantId")
                                                .build()))
                                        .build())
                                .build())
                        .build());
        Mockito.when(paymentsTxnlClient.initToaTransaction(Mockito.any(PaymentRequest.class), Mockito.anyString(),
                        Mockito.anyString()))
                .thenReturn(GenericResponse.<PaymentProcessorResult>builder()
                        .data(PaymentProcessorResult.builder()
                                .build())
                        .build());
        Mockito.when(penaltyClassService.getClassFor(Mockito.anyString()))
            .thenReturn(Optional.of(PenaltyTestObjectUtil.getPenaltyClass()));
        Mockito.doNothing().when(notificationService).sendNotification(Mockito.any(),
            Mockito.anyString(), Mockito.anyString(), Mockito.any());
        PenaltyDisbursement disburseEntity = penaltyDisbursementService.initiateDisbursement(penalty);
        Assertions.assertEquals(PenaltyDisbursementState.COMPLETED, disburseEntity.getStatus());
        Mockito.verify(penaltyRecoveryService, Mockito.times(1))
                .initiateRecovery(Mockito.anyString(), Mockito.any(Penalty.class), Mockito.anyLong());
    }


    @Test
    public void processAmountLessThanZeroNotCompletedDisbursement() {
        Penalty penalty = PenaltyTestObjectUtil.getPenaltyInstance();
        penalty.setInitialPenaltyAmount(0);
        penalty.setFinalPenaltyAmount(0);
        PenaltyDisbursementEntity disbursementEntity = PenaltyTestObjectUtil.getPenaltyDisbursementEntity();
        disbursementEntity.setStatus(PenaltyDisbursementState.PROCESSING);
        Mockito.when(
                        penaltyDisbursementRepository.getDisbursementForTransactionId(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(Optional.of(disbursementEntity));

        Mockito.when(penaltyClassService.getClassDisbursementConfig(penalty.getPenaltyClassId()))
                .thenReturn(MerchantPenaltyToADisbursementConfig.builder()
                        .destination(GiftCardDestination.builder().userId("UserId").build())
                        .transferUseCase(TransferUseCase.BBPS_PENALTY)
                        .build());
        Mockito.when(penaltyDisbursementRepository.save(Mockito.any(PenaltyDisbursementEntity.class)))
                .thenReturn(Optional.of(disbursementEntity));
        Mockito.when(paymentsTxnlClient.getTransactionDetails(Mockito.anyString()))
                .thenReturn(GenericResponse.<TransactionDetail>builder()
                        .data(TransactionDetail.builder()
                                .sentPayment(SentPayment.builder()
                                        .to(List.of(Receiver.builder()
                                                .merchantId("testMerchantId")
                                                .build()))
                                        .build())
                                .build())
                        .build());
        Mockito.when(paymentsTxnlClient.initToaTransaction(Mockito.any(PaymentRequest.class), Mockito.anyString(),
                        Mockito.anyString()))
                .thenReturn(GenericResponse.<PaymentProcessorResult>builder()
                        .data(PaymentProcessorResult.builder()
                                .build())
                        .build());
        PenaltyDisbursement disburseEntity = penaltyDisbursementService.initiateDisbursement(penalty);
        Assertions.assertEquals(PenaltyDisbursementState.FAILED, disburseEntity.getStatus());
        Mockito.verify(penaltyRecoveryService, Mockito.times(0))
                .initiateRecovery(Mockito.anyString(), Mockito.any(Penalty.class), Mockito.anyLong());
    }


    @Test
    public void processExistingNotCompletedDisbursementWithTransactionNull() {
        Penalty penalty = PenaltyTestObjectUtil.getPenaltyInstance();
        PenaltyDisbursementEntity disbursementEntity = PenaltyTestObjectUtil.getPenaltyDisbursementEntity();
        disbursementEntity.setStatus(PenaltyDisbursementState.PROCESSING);
        Mockito.when(
                        penaltyDisbursementRepository.getDisbursementForTransactionId(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(Optional.of(disbursementEntity));

        Mockito.when(penaltyClassService.getClassDisbursementConfig(penalty.getPenaltyClassId()))
                .thenReturn(MerchantPenaltyToADisbursementConfig.builder()
                        .destination(GiftCardDestination.builder().userId("UserId").build())
                        .transferUseCase(TransferUseCase.BBPS_PENALTY)
                        .build());
        Mockito.when(penaltyDisbursementRepository.save(Mockito.any(PenaltyDisbursementEntity.class)))
                .thenReturn(Optional.of(disbursementEntity));
        Mockito.when(paymentsTxnlClient.getTransactionDetails(Mockito.anyString()))
                .thenReturn(GenericResponse.<TransactionDetail>builder()
                        .build());
        Mockito.when(paymentsTxnlClient.initToaTransaction(Mockito.any(PaymentRequest.class), Mockito.anyString(),
                        Mockito.anyString()))
                .thenReturn(GenericResponse.<PaymentProcessorResult>builder()
                        .data(PaymentProcessorResult.builder()
                                .build())
                        .build());

        PenaltyDisbursement result = penaltyDisbursementService.initiateDisbursement(penalty);
        Assertions.assertEquals(result.getStatus(),PenaltyDisbursementState.FAILED);
        Mockito.verify(penaltyDisbursementRepository, Mockito.times(2)).save(Mockito.any(PenaltyDisbursementEntity.class));


    }

    @Test
    void testGetDisbursementWithException() {
        PenaltyTestObjectUtil.getPenaltyDisbursementEntity();
        Mockito.when(
                        penaltyDisbursementRepository.getDisbursementForPenaltyId(Mockito.anyString(), Mockito.anyString()))
                .thenThrow(DisputeException.builder()
                        .context(new HashMap<>())
                        .error("Error fetching disbursement")
                        .build());
        Assertions.assertThrows(RuntimeException.class, () -> {
            penaltyDisbursementService.getDisbursement("testPenaltyId", "testMerchantId");
        });
    }

    @Test
    void testGetDisbursement() {

        Mockito.when(
                        penaltyDisbursementRepository.getDisbursementForPenaltyId(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(Optional.of(PenaltyTestObjectUtil.getPenaltyDisbursementEntity()));
        PenaltyDisbursement result = penaltyDisbursementService.getDisbursement("testPenaltyId", "testMerchantId");
        Assertions.assertNotNull(result);
    }

    @Test
    void testSendNotificationForPenaltyDisbursement() throws JsonProcessingException {
        Mockito.when(penaltyClassService.getClassFor(Mockito.anyString())).thenReturn(
            Optional.of(PenaltyTestObjectUtil.getPenaltyClass()));
        Penalty penalty = PenaltyTestObjectUtil.getPenaltyInstance();
        Mockito.doNothing().when(notificationService).sendNotification(Mockito.any(),
            Mockito.anyString(), Mockito.anyString(), Mockito.any());
        Assertions.assertDoesNotThrow(()-> penaltyDisbursementService.sendNotification(penalty, EmailCommunicationRequest.builder()
            .emailIDs(Set.of("<EMAIL>"))
            .build()));
    }

    @Test
    void testSendNotificationForPenaltyDisbursementForFailure() throws JsonProcessingException {
        Mockito.when(penaltyClassService.getClassFor(Mockito.anyString())).thenReturn(
            Optional.of(PenaltyTestObjectUtil.getPenaltyClass()));
        Penalty penalty = PenaltyTestObjectUtil.getPenaltyInstance();
        Mockito.doThrow(DisputeException.class).when(notificationService).sendNotification(Mockito.any(),
            Mockito.any(), Mockito.any(), Mockito.any());
        Assertions.assertDoesNotThrow(()-> penaltyDisbursementService.sendNotification(penalty, EmailCommunicationRequest.builder()
            .emailIDs(Set.of("<EMAIL>"))
            .build()));
        Mockito.verify(eventIngester, Mockito.times(1)).generateEvent(
            Mockito.any());
    }

    @Test
    void testSendNotificationForPenaltyDisbursementForRuntimeFailure() throws JsonProcessingException {
        Mockito.when(penaltyClassService.getClassFor(Mockito.anyString())).thenReturn(
            Optional.of(PenaltyTestObjectUtil.getPenaltyClass()));
        Penalty penalty = PenaltyTestObjectUtil.getPenaltyInstance();
        Mockito.doThrow(RuntimeException.class).when(notificationService).sendNotification(Mockito.any(),
            Mockito.any(), Mockito.any(), Mockito.any());
        Assertions.assertDoesNotThrow(()-> penaltyDisbursementService.sendNotification(penalty, EmailCommunicationRequest.builder()
            .emailIDs(Set.of("<EMAIL>"))
            .build()));
        Mockito.verify(eventIngester, Mockito.times(1)).generateEvent(
            Mockito.any());
    }

    @Test
    void testSendNotificationForPenaltyDisbursementForPenaltyClassNotPresent() throws JsonProcessingException {
        Penalty penalty = PenaltyTestObjectUtil.getPenaltyInstance();
        Mockito.doThrow(DisputeException.class).when(notificationService).sendNotification(Mockito.any(),
            Mockito.any(), Mockito.any(), Mockito.any());
        DisputeException exception = Assertions.assertThrows(DisputeException.class, ()-> penaltyDisbursementService.sendNotification(penalty, EmailCommunicationRequest.builder()
            .emailIDs(Set.of("<EMAIL>"))
            .build()));
        Assertions.assertEquals(StratosErrorCodeKey.PENALTY_CLASS_NOT_PRESENT, exception.getErrorCode());
    }
}
