package com.phonepe.merchant.platform.stratos.server.integrationTests.file.upi;

import com.fasterxml.jackson.dataformat.csv.CsvMapper;
import com.phonepe.merchant.platform.stratos.models.commons.DateRange;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeTypeDto;
import com.phonepe.merchant.platform.stratos.models.files.FileDto;
import com.phonepe.merchant.platform.stratos.models.files.FileFormat;
import com.phonepe.merchant.platform.stratos.models.files.FileRow;
import com.phonepe.merchant.platform.stratos.models.files.FileTypeDto;
import com.phonepe.merchant.platform.stratos.models.files.filters.DisputeFileTypeDateRange;
import com.phonepe.merchant.platform.stratos.models.files.filters.FileNameFileFilter;
import com.phonepe.merchant.platform.stratos.models.files.responses.*;
import com.phonepe.merchant.platform.stratos.server.LoadOnlyOnClassLevelBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.File;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Row;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.FileRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.RowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.*;
import com.phonepe.merchant.platform.stratos.server.core.resources.FileResource;
import com.phonepe.merchant.platform.stratos.server.core.utils.StorageUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.file.upi.beans.UPIChargebackBean;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.olympus.im.models.security.ServiceUserPrincipal;
import com.phonepe.olympus.im.models.user.HumanUserDetails;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.junit.Assert.assertEquals;

public class FileMetaAPIsIntegrationTest extends LoadOnlyOnClassLevelBaseTest {

    private FileResource fileResource;
    private FileRepository fileRepository;
    private RowRepository rowRepository;
    private OlympusIMClient olympusIMClient;

    @BeforeEach
    void setUpGuiceInjection() {
        fileResource = guiceInjector.getInstance(FileResource.class);
        fileRepository = guiceInjector.getInstance(FileRepository.class);
        rowRepository = guiceInjector.getInstance(RowRepository.class);
        olympusIMClient = guiceInjector.getInstance(OlympusIMClient.class);
        truncateDb();
    }

    @Test
    public void testGetHistory() {
        fileRepository.saveFile("file1", File.builder()
            .disputeType(DisputeType.UPI_CHARGEBACK)
            .fileId("file1")
            .fileName("file1")
            .fileState(FileState.PROCESSED)
            .fileType(FileType.YES)
            .key(StorageUtils.primaryKey())
            .gandalfUserId("test-user")
            .rowCount(10)
            .userType(UserType.SYSTEM)
            .build());

        fileRepository.saveFile("file2", File.builder()
            .disputeType(DisputeType.UPI_CHARGEBACK)
            .fileId("file2")
            .fileName("file2")
            .fileState(FileState.PROCESSED)
            .fileType(FileType.YES)
            .key(StorageUtils.primaryKey())
            .gandalfUserId("test-user")
            .rowCount(10)
            .userType(UserType.SYSTEM)
            .build());

        fileRepository.saveFile("file3", File.builder()
            .disputeType(DisputeType.UPI_CHARGEBACK)
            .fileId("file3")
            .fileName("file3")
            .fileState(FileState.PROCESSED)
            .fileType(FileType.YES)
            .key(StorageUtils.primaryKey())
            .gandalfUserId("test-user")
            .rowCount(10)
            .userType(UserType.SYSTEM)
            .build());

        fileRepository.saveFile("file4", File.builder()
            .disputeType(DisputeType.UPI_CHARGEBACK)
            .fileId("file4")
            .fileName("file4")
            .fileState(FileState.PROCESSED)
            .fileType(FileType.YES)
            .key(StorageUtils.primaryKey())
            .gandalfUserId("test-user")
            .rowCount(10)
            .userType(UserType.SYSTEM)
            .build());

        fileRepository.saveFile("file5", File.builder()
            .disputeType(DisputeType.PG_CHARGEBACK)
            .fileId("file5")
            .fileName("file5")
            .fileState(FileState.PROCESSED)
            .fileType(FileType.PG_FIRST_LEVEL)
            .key(StorageUtils.primaryKey())
            .gandalfUserId("test-user")
            .rowCount(10)
            .userType(UserType.SYSTEM)
            .build());

        final var fileNameHistoryResponse = fileResource.history(
            FileNameFileFilter.builder()
                .fileName("file2")
                .build());

        assertEquals(Set.of("file2"),
            fileNameHistoryResponse.getRows().stream()
                .map(FileDto::getFileName)
                .collect(Collectors.toSet()));

        final var upiDisputeFileTypeDateRangeResponse = fileResource.history(
            DisputeFileTypeDateRange.builder()
                .dateRange(DateRange.builder()
                    .startDate(LocalDate.now().minusDays(2))
                    .endDate(LocalDate.now())
                    .build())
                .disputeTypes(Set.of(DisputeTypeDto.UPI_CHARGEBACK))
                .fileTypes(Set.of(FileTypeDto.YES))
                .build());

        final var rows = upiDisputeFileTypeDateRangeResponse.getRows();
        assertEquals(4, rows.size());
        assertEquals(Set.of("file1", "file2", "file3", "file4"),
            rows.stream()
                .map(FileDto::getFileName)
                .collect(Collectors.toSet()));

        final var pgDisputeFileTypeDateRangeResponse = fileResource.history(
            DisputeFileTypeDateRange.builder()
                .dateRange(DateRange.builder()
                    .startDate(LocalDate.now().minusDays(2))
                    .endDate(LocalDate.now())
                    .build())
                .disputeTypes(Set.of(DisputeTypeDto.PG_CHARGEBACK))
                .fileTypes(Set.of(FileTypeDto.PG_FIRST_LEVEL))
                .build());

        final var pgRows = pgDisputeFileTypeDateRangeResponse.getRows();
        assertEquals(1, pgRows.size());
        assertEquals(Set.of("file5"),
            pgRows.stream()
                .map(FileDto::getFileName)
                .collect(Collectors.toSet()));
    }

    @Test
    public void testFileRow() {
        rowRepository.saveRow(Row.builder()
            .key(StorageUtils.primaryKey())
            .rowId("row1234")
            .rowState(RowState.FAILED)
            .sourceId("file")
            .sourceType(SourceType.FILE)
            .rowType(RowType.UPI_FILE_ROW)
            .build());
        rowRepository.saveRow(Row.builder()
            .key(StorageUtils.primaryKey())
            .rowId("row1235")
            .rowState(RowState.FAILED)
            .sourceId("file")
            .sourceType(SourceType.FILE)
            .rowType(RowType.PG_MIS_ROW)
            .build());
        rowRepository.saveRow(Row.builder()
            .key(StorageUtils.primaryKey())
            .rowId("row1236")
            .rowState(RowState.FAILED)
            .sourceId("file")
            .sourceType(SourceType.FILE)
            .rowType(RowType.PG_FILE_ROW)
            .build());

        var response = fileResource.fileRow("file");

        var list = response.getRows();

        assertEquals(3, list.size());
        assertEquals(Set.of("row1234", "row1235", "row1236"), list.stream()
            .map(FileRow::getRowId)
            .collect(Collectors.toSet()));

    }

    @Test
    public void testDownloadProcessingRowForUpi() throws Exception {
        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForCreateChargebackApiMocking();

        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        final var upiFileResponse = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK,
            FileTypeDto.YES,
            TestDataUtils.UPIFirstLevelChargebackCSVFileStream(),
            FormDataContentDisposition.name("randomName123").fileName("file123.csv")
                .build(),"AuthToken", serviceUserPrincipal);
        AssertionUtils.assertRowStateEquals(RowState.FAILED,upiFileResponse.getId(), 15, TimeUnit.SECONDS);

        final var upiResponse = fileResource.downloadProcessingRow(FileFormat.CSV,
            upiFileResponse.getId());
        final var list = new CsvMapper().readerWithTypedSchemaFor(
                UpiFileHistoryProcessingSummaryRow.class)
            .readValues((byte[]) upiResponse.getEntity()).readAll();
        assertEquals(2, list.size());
        assertEquals("RRN1234", ((UpiFileHistoryProcessingSummaryRow) list.get(1)).getRrn());
        assertEquals("YBL123", ((UpiFileHistoryProcessingSummaryRow) list.get(1)).getUpiTransactionId());
        assertEquals("Chargeback Raise",((UpiFileHistoryProcessingSummaryRow) list.get(1)).getAdjType());
        assertEquals("YES",((UpiFileHistoryProcessingSummaryRow) list.get(1)).getBeneficiary());
        assertEquals("COMMUNICATION_ERROR",((UpiFileHistoryProcessingSummaryRow) list.get(1)).getCode());
        assertEquals("1.0",((UpiFileHistoryProcessingSummaryRow) list.get(1)).getAdjAmount());
        assertEquals("0.0",((UpiFileHistoryProcessingSummaryRow) list.get(1)).getCompensationAmount());
        assertEquals("1.0",((UpiFileHistoryProcessingSummaryRow) list.get(1)).getTxnAmount());
    }

    @Test
    public void testDownloadProcessingRowForPgFirstLevel() throws Exception {
        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForCreateChargebackApiMocking("HDFC Bank");

        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        final var pgFileResponse = fileResource.upload(DisputeTypeDto.PG_CHARGEBACK,
            FileTypeDto.PG_FIRST_LEVEL,
            TestDataUtils.pgFirstLevelChargebackCSVFileStreamFromPgName("HDFC Bank"),
            FormDataContentDisposition.name("randomName123").fileName("file123.csv")
                .build(),"AuthToken", serviceUserPrincipal);
        AssertionUtils.assertRowStateEquals(RowState.FAILED,pgFileResponse.getId(), 15, TimeUnit.SECONDS);
        final var pgResponse = fileResource.downloadProcessingRow(FileFormat.CSV,
            pgFileResponse.getId());
        final var list = new CsvMapper().readerWithTypedSchemaFor(
                PgFirstLevelFileHistoryProcessingSummaryRow.class)
            .readValues((byte[]) pgResponse.getEntity()).readAll();
        assertEquals(2, list.size());
        assertEquals("rrn1234", ((PgFirstLevelFileHistoryProcessingSummaryRow) list.get(1)).getPgId());
        assertEquals("PG1234",((PgFirstLevelFileHistoryProcessingSummaryRow) list.get(1)).getTransactionId());
        assertEquals("10.3",((PgFirstLevelFileHistoryProcessingSummaryRow) list.get(1)).getReasonCode());
        assertEquals("Service Chargeback",((PgFirstLevelFileHistoryProcessingSummaryRow) list.get(1)).getDisputeReason());
        assertEquals("PGT_TRANSACTION_DETAIL_ERROR",((PgFirstLevelFileHistoryProcessingSummaryRow) list.get(1)).getCode());
        assertEquals("ABCD2",((PgFirstLevelFileHistoryProcessingSummaryRow) list.get(1)).getBankRefNo());
        assertEquals("1.0",((PgFirstLevelFileHistoryProcessingSummaryRow) list.get(1)).getBankDisputedAmount());
    }
    @Test
    public void testDownloadProcessingRowForPgPreArb() throws Exception {

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForCreateChargebackPreArbApiMocking("HDFC Bank");

        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        final var pgFileResponse = fileResource.upload(DisputeTypeDto.PG_CHARGEBACK,
            FileTypeDto.PG_PRE_ARBITRATION,
            TestDataUtils.pgPreArbLevelChargebackCSVFileStream("HDFC Bank"),
            FormDataContentDisposition.name("randomName123").fileName("file123.csv")
                .build(),"AuthToken", serviceUserPrincipal);
        AssertionUtils.assertRowStateEquals(RowState.FAILED, pgFileResponse.getId(), 15, TimeUnit.SECONDS);
        final var pgResponse = fileResource.downloadProcessingRow(FileFormat.CSV,
            pgFileResponse.getId());
        final var list = new CsvMapper().readerWithTypedSchemaFor(
                PgPreArbFileHistoryProcessingSummaryRow.class)
            .readValues((byte[]) pgResponse.getEntity()).readAll();
        assertEquals(2, list.size());
        assertEquals("1.0",((PgPreArbFileHistoryProcessingSummaryRow) list.get(1)).getBankDisputedAmount());
        assertEquals("HDFC Bank",((PgPreArbFileHistoryProcessingSummaryRow) list.get(1)).getSource());
        assertEquals("2021-12-12",((PgPreArbFileHistoryProcessingSummaryRow) list.get(1)).getCbReceivedDate());
        assertEquals("PG1234",((PgPreArbFileHistoryProcessingSummaryRow) list.get(1)).getTransactionId());
    }

    @Test
    public void testDownloadProcessingRowForEdc() throws Exception {
        final var transactionReferenceId = IdGenerator.generate("T").getId();
        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForEdcCreateChargebackApiMocking();
        final var userDetailsOlympus = HumanUserDetails.builder()
            .userId("test-user")
            .build();
        final var userAuthDetails = UserAuthDetails.builder()
            .userDetails(userDetailsOlympus)
            .build();
        final var serviceUserPrincipal = ServiceUserPrincipal.builder()
            .userAuthDetails(userAuthDetails)
            .build();

        var response = fileResource.upload(DisputeTypeDto.EDC_CHARGEBACK,
            FileTypeDto.EDC_FIRST_LEVEL, TestDataUtils.edcChargebackCSVFileStream(),
            FormDataContentDisposition.name("randomName").fileName("file.csv")
                .build(),"AuthToken", serviceUserPrincipal);
        AssertionUtils.assertRowStateEquals(RowState.FAILED, response.getId(), 15, TimeUnit.SECONDS);
        final var edcResponse = fileResource.downloadProcessingRow(FileFormat.CSV,
            response.getId());
        final var list = new CsvMapper().readerWithTypedSchemaFor(
                EdcFileHistoryProcessingSummaryRow.class)
            .readValues((byte[]) edcResponse.getEntity()).readAll();
        assertEquals(2, list.size());
        assertEquals("1.0",((EdcFileHistoryProcessingSummaryRow) list.get(1)).getBankDisputedAmount());
        assertEquals("Axis Bank",((EdcFileHistoryProcessingSummaryRow) list.get(1)).getSource());
        assertEquals("2021-01-01",((EdcFileHistoryProcessingSummaryRow) list.get(1)).getCbReceivedDate());
    }
    @Test
    public void testFileMeta() {
        final var response = fileResource.meta();

        assertEquals(5, response.size());
    }


    @Test
    public void testFileDownload() throws Exception {
        MockingUtils.setupDocstoreGetFileForChargebackCreditApiMocking();
        fileRepository.saveFile("file1234", File.builder()
            .userType(UserType.USER)
            .rowCount(1)
            .gandalfUserId("user")
            .key(StorageUtils.primaryKey())
            .fileType(FileType.YES)
            .fileState(FileState.PROCESSED)
            .fileName("file1.csv")
            .fileId("file1234")
            .disputeType(DisputeType.UPI_CHARGEBACK)
            .build());
        final var response = fileResource.download("file1234");
        final var list = new CsvMapper().readerWithTypedSchemaFor(UPIChargebackBean.class)
            .readValues((byte[]) response.getEntity()).readAll();
        assertEquals(2, list.size());
        assertEquals("Representment Raise", ((UPIChargebackBean) list.get(1)).getAdjType());
    }

    @AfterEach
    void after() {
        MARIA_DB_CONTAINER.executeQuery("TRUNCATE stratos_shard_1.file;\n"
            + "TRUNCATE stratos_shard_2.file;\n"
            + "TRUNCATE stratos_shard_1.file_audit;\n"
            + "TRUNCATE stratos_shard_2.file_audit;\n"
            + "TRUNCATE stratos_shard_1.row;\n"
            + "TRUNCATE stratos_shard_2.row;\n"
            + "TRUNCATE stratos_shard_1.row_audit;\n"
            + "TRUNCATE stratos_shard_2.row_audit;", true);
    }

    @Test
    public void testDownloadProcessingRowForNbFirstLevel() throws Exception {

        var context = RowContext.builder()
            .content(TestDataUtils.getNBContent("NETPE","NP123", 10.0))
            .code("INTERNAL_SERVER_ERROR")
            .build();

        fileRepository.saveFile("file001", File.builder()
            .disputeType(DisputeType.NB_CHARGEBACK)
            .fileId("file001")
            .fileName("file1")
            .fileState(FileState.PROCESSING)
            .fileType(FileType.NB_FIRST_LEVEL)
            .key(StorageUtils.primaryKey())
            .gandalfUserId("test-user")
            .rowCount(1)
            .userType(UserType.SYSTEM)
            .build());

        rowRepository.saveRow(Row.builder()
            .key(StorageUtils.primaryKey())
            .rowId("row1235")
            .rowState(RowState.FAILED)
            .sourceId("file001")
            .sourceType(SourceType.FILE)
            .rowType(RowType.NB_FILE_ROW)
            .rowContext(context)
            .build());

        final var nbResponse = fileResource.downloadProcessingRow(FileFormat.CSV, "file001");
        final var list = new CsvMapper().readerWithTypedSchemaFor(
                NBFileHistoryProcessingSummaryRow.class)
            .readValues((byte[]) nbResponse.getEntity()).readAll();
        assertEquals(2, list.size());
        assertEquals("NETPE",((NBFileHistoryProcessingSummaryRow) list.get(1)).getSource());
        assertEquals("NP123",((NBFileHistoryProcessingSummaryRow) list.get(1)).getTransactionId());
        assertEquals("10.0",((NBFileHistoryProcessingSummaryRow) list.get(1)).getAmount());

    }
}