package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.visitors;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.phonepe.error.configurator.model.service.ResourceErrorService;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.BaseTest;
import com.phonepe.merchant.platform.stratos.server.ErrorConfiguratorBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.handlebars.HandleBarsHelperRegistry;
import com.phonepe.merchant.platform.stratos.server.core.handlebars.HandleBarsService;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.File;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.models.payments.banking.card.CardIssuer;
import com.phonepe.models.payments.common.party.impl.InternalUserParty;
import com.phonepe.models.payments.pay.ReceivedPayment;
import com.phonepe.models.payments.pay.context.impl.WalletAppTopupContext;
import com.phonepe.models.payments.pay.instrument.AccountPaymentInstrument;
import com.phonepe.models.payments.pay.instrument.BnplPaymentInstrument;
import com.phonepe.models.payments.pay.instrument.CreditCardPaymentInstrument;
import com.phonepe.models.payments.pay.instrument.CreditLinePaymentInstrument;
import com.phonepe.models.payments.pay.instrument.DebitCardPaymentInstrument;
import com.phonepe.models.payments.pay.instrument.ExternalVpaPaymentInstrument;
import com.phonepe.models.payments.pay.instrument.ExternalWalletPaymentInstrument;
import com.phonepe.models.payments.pay.instrument.GiftCardPaymentInstrument;
import com.phonepe.models.payments.pay.instrument.NetBankingPaymentInstrument;
import com.phonepe.models.payments.pay.instrument.PaymentInstrument;
import com.phonepe.models.payments.pay.instrument.WalletPaymentInstrument;
import edu.emory.mathcs.backport.java.util.Arrays;
import io.dropwizard.jackson.Jackson;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Optional;

@Slf4j
public class NetworkVisitorTest extends ErrorConfiguratorBaseTest {
    NetworkVisitor networkVisitor = new NetworkVisitor();
    /* AccountPaymentInstrument, CreditCardPaymentInstrument is tested as part of download flow
     * so here we test other functions as adding everything in download flow will lead to adding lot of mock files.
     */

    @BeforeEach
    void setUpGuiceInjection() throws JsonProcessingException, MalformedURLException, URISyntaxException {
        ResourceErrorService<StratosErrorCodeKey> resourceErrorService = getResourceErrorService();
        log.error("resourceErrorService  : {}", resourceErrorService);
        DisputeExceptionUtil.init(resourceErrorService);
        MapperUtils.init(Jackson.newObjectMapper());

    }
    @Test
    void testInstrumentsWoNetwork() {
        List<PaymentInstrument> instrumentWoNetwork = Arrays.asList(new PaymentInstrument[] {
            WalletPaymentInstrument.builder().build(),
            BnplPaymentInstrument.builder().build(),
            AccountPaymentInstrument.builder().build(),
            NetBankingPaymentInstrument.builder().build(),
            WalletPaymentInstrument.builder().build(),
            GiftCardPaymentInstrument.builder().build(),
            ExternalVpaPaymentInstrument.builder().build(),
            ExternalWalletPaymentInstrument.builder().build(),
            BnplPaymentInstrument.builder().build(),
            CreditLinePaymentInstrument.builder().build()
        });
        List<PaymentInstrument> instrumentWithNetwork = Arrays.asList(new PaymentInstrument[] {
            CreditCardPaymentInstrument.builder().cardIssuer(CardIssuer.MASTER_CARD).build(),
            DebitCardPaymentInstrument.builder().cardIssuer(CardIssuer.MASTER_CARD).build()
        });
        String network;
        for (PaymentInstrument payInstrument: instrumentWoNetwork) {
            network = payInstrument.accept(networkVisitor);
            Assertions.assertEquals("",network);
        }
        for (PaymentInstrument payInstrument: instrumentWithNetwork) {
            network = payInstrument.accept(networkVisitor);
            Assertions.assertEquals(CardIssuer.MASTER_CARD.getDisplayName(),network);
        }
    }

}
