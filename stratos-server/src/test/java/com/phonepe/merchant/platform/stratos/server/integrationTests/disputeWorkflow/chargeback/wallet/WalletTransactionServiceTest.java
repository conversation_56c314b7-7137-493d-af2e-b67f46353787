package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.wallet;

import com.phonepe.error.configurator.model.service.ResourceErrorService;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.ErrorConfiguratorBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.clients.PaymentsTxnlClient;
import com.phonepe.merchant.platform.stratos.server.core.clients.WalletTxnlClient;
import com.phonepe.merchant.platform.stratos.server.core.services.impls.WalletTransactionService;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.models.payments.pay.TransactionDetail;
import com.phonepe.models.response.GenericResponse;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

@Slf4j
@ExtendWith(MockitoExtension.class)
public class WalletTransactionServiceTest extends ErrorConfiguratorBaseTest {

    public PaymentsTxnlClient paymentsTxnlClient = Mockito.mock(PaymentsTxnlClient.class);
    public WalletTxnlClient walletTxnlClient = Mockito.mock(WalletTxnlClient.class);
    public WalletTransactionService walletTransactionService = new WalletTransactionService(paymentsTxnlClient, walletTxnlClient);

    static String UPI_ID ="UPI_ID";
    static String TRNX_ID ="TRNX_ID";
    static String RRN ="RRN";

    @Test
    public void wrongUpiIdFlowTest() throws MalformedURLException, URISyntaxException {

        MockitoAnnotations.initMocks(this);
        ResourceErrorService<StratosErrorCodeKey> resourceErrorService = getResourceErrorService();
        DisputeExceptionUtil.init(resourceErrorService);

        Date date = new Date();
        Mockito.when(walletTxnlClient.getPaymentsTransactionDetailFromUpiTxnId(UPI_ID, RRN, date)).thenReturn(
            GenericResponse.<TransactionDetail>builder()
                .success(false)
                .build()
        );

        final var exception = Assertions.assertThrows(DisputeException.class, ()->
            walletTransactionService.getTransactionDetailFromUpiId(UPI_ID, RRN, date));

        Assertions.assertEquals(StratosErrorCodeKey.INVALID_UPI_ID, exception.getErrorCode());
    }

    @Test
    public void wrongPaymentIdFlowTest() throws MalformedURLException, URISyntaxException {

        MockitoAnnotations.initMocks(this);
        ResourceErrorService<StratosErrorCodeKey> resourceErrorService = getResourceErrorService();
        log.error("resourceErrorService  : {}", resourceErrorService);
        DisputeExceptionUtil.init(resourceErrorService);

        Mockito.when(paymentsTxnlClient.getTransactionDetails(TRNX_ID)).thenReturn(
            GenericResponse.<TransactionDetail>builder()
                .success(false)
                .build()
        );

        final var exception = Assertions.assertThrows(DisputeException.class, ()->
            walletTransactionService.getTransactionDetails(TRNX_ID));

        Assertions.assertEquals(StratosErrorCodeKey.INVALID_PAYMENTS_ID, exception.getErrorCode());
    }
}
