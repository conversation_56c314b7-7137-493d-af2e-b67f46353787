package com.phonepe.merchant.platform.stratos.server.integrationTests.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.dataformat.csv.CsvMapper;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.List;
import lombok.experimental.UtilityClass;
@UtilityClass
public class FileTestUtils {


    public <T> InputStream getBeansAsInputStream(
        final List<T> beans)
        throws JsonProcessingException {
        T instance = (T) beans.get(0).getClass();
        final var csvMapper = new CsvMapper();
        final var csvSchema = csvMapper
            .schemaFor((Class<?>) instance)
            .withHeader();

        final var bytes = csvMapper.writer(csvSchema).writeValueAsBytes(beans);
        return new ByteArrayInputStream(bytes);
    }

}
