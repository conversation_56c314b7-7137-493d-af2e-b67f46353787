package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.wallet.firstLevel;

import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.requests.RefundInitiationReconRequest;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.WalletDisputeMetadataRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeMetadataHelper;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeWorkflowUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.services.refund.orchestrator.models.RefundStatus;
import java.time.LocalDateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class WalletReconcileRequestTest extends WalletFirstLevelBaseTest {

    public WalletDisputeMetadataRepository walletDisputeMetadataRepository;
    public DisputeMetadataHelper disputeMetadataHelper;

    @BeforeEach
    void initEach(){
        truncateDb();
        createEntrySetupForAPI("WALLET", String.valueOf(LocalDateTime.now()),
            dispute.getTransactionReferenceId());
        FinancialDisputeWorkflow financialDisputeWorkflow = DisputeWorkflowUtils.getFinancialDisputeWorkflow(disputeWorkflow);
        MockingUtils.setUpRefundResponseMocking(RefundStatus.ACCEPTED.name(),
            financialDisputeWorkflow.getAcceptedAmount());
        disputeWorkflow.setCurrentState(DisputeWorkflowState.FULLY_ACCEPTED_CHARGEBACK);
        disputeWorkflow.setCurrentEvent(DisputeWorkflowEvent.FULLY_ACCEPT_CHARGEBACK);
        disputeWorkflow.setDisputeWorkflowVersion(DisputeWorkflowVersion.V1);
        disputeService.persistDisputeAndDisputeWorkflow(dispute, disputeWorkflow);
        walletDisputeMetadataRepository = guiceInjector.getInstance(WalletDisputeMetadataRepository.class);
        disputeMetadataHelper = guiceInjector.getInstance(DisputeMetadataHelper.class);
        MockingUtils.setupEventIngestionApiMocking();
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource(API_SERVICE_CATEGORY)
    void testNpciAckToRefundInitiationChargebackWithReconcileAPI(
        com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeCategory disputeCategory) {
        walletDisputeMetadataRepository.save(disputeMetadataHelper.toWalletDisputeMetadata(
            disputeWorkflow, "123"));

        disputeService.reconcile(RefundInitiationReconRequest.builder()
            .build());
        assertFromCBRefundInitiationCompleteToAcceptedChargebackWithReconcileAPI(
            DisputeWorkflowState.CB_REFUND_INITIATED_COMPLETED);
    }
}
