package com.phonepe.merchant.platform.stratos.server.integrationTests.file.upi;

import com.phonepe.merchant.platform.stratos.models.disputes.DisputeTypeDto;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.models.files.FileTypeDto;
import com.phonepe.merchant.platform.stratos.server.BaseTest;
import com.phonepe.merchant.platform.stratos.server.LoadOnlyOnClassLevelBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.FileUploadLockCommand;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.keys.FileUploadLockKey;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.*;
import com.phonepe.merchant.platform.stratos.server.core.resources.FileResource;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.olympus.im.client.OlympusIMClient;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.concurrent.TimeUnit;

public class FileUploadIntegrationTest extends LoadOnlyOnClassLevelBaseTest {

    private FileResource fileResource;
    private DisputeService disputeService;
    private DisputeWorkflowRepository disputeWorkflowRepository;
    private DisputeRepository disputeRepository;
    private OlympusIMClient olympusIMClient;
    private FileUploadLockCommand fileUploadLockCommand;

    @BeforeEach
    void setUpGuiceInjection() {
        fileResource = guiceInjector.getInstance(FileResource.class);
        disputeService = guiceInjector.getInstance(DisputeService.class);
        disputeWorkflowRepository = guiceInjector.getInstance(DisputeWorkflowRepository.class);
        disputeRepository = guiceInjector.getInstance(DisputeRepository.class);
        olympusIMClient = guiceInjector.getInstance(OlympusIMClient.class);
        fileUploadLockCommand = guiceInjector.getInstance(FileUploadLockCommand.class);

        truncateDb();
    }

    public void uploadDataForUPI(final String transactionReferenceId) throws Exception {
        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();
        final var userAuthDetails = TestDataUtils.getOlympusAuthDetails();

        var response = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
            TestDataUtils.UPIFirstLevelChargebackCSVFileStream(),
            FormDataContentDisposition.name("randomName").fileName("file.csv")
                .build(), "AuthToken",serviceUserPrincipal);

        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);

        final var disputeWorkflow = disputeService.
            getDisputeWorkflow(transactionReferenceId, DisputeType.UPI_CHARGEBACK,
                DisputeStage.FIRST_LEVEL);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.REPRESENTMENT_REQUIRED,
            5L, TimeUnit.SECONDS);

        // Trigger Event for Completing Representment on NPCI
        disputeService.triggerEvent(
            userAuthDetails,
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowEvent.COMPLETE_NPCI_REPRESENTMENT,
            Constants.EMPTY_TRANSITION_CONTEXT);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.NPCI_REPRESENTMENT_COMPLETED,
            5L, TimeUnit.SECONDS);

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileFirstLevelForChargebackCreditApiMocking();

        response = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
            TestDataUtils.UPI_FIRST_LEVEL_CSV_StreamWithCreditEntry(),
            FormDataContentDisposition.name("randomName1").fileName("file1.csv")
                .build(),"AuthToken", serviceUserPrincipal);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.CREDIT_RECEIVED,
            5L, TimeUnit.SECONDS);

        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);
    }

    @Test
    public void testUploadUPIFileForFullRefundWithoutCompleteNpciCompletedEvent() throws Exception {
        final var transactionReferenceId = IdGenerator.generate("P").getId();

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForCreateChargebackApiMocking();
        MockingUtils.setupPaymentsApiFullRefundExistsMocking(transactionReferenceId, 100, "YBL123");
        MockingUtils.setupEventIngestionApiMocking();

        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        var response = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
            TestDataUtils.UPIFirstLevelChargebackCSVFileStream(),
            FormDataContentDisposition.name("randomName").fileName("file.csv")
                .build(), "AuthToken",serviceUserPrincipal);

        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);

        final var disputeWorkflow = disputeService.
            getDisputeWorkflow(transactionReferenceId, DisputeType.UPI_CHARGEBACK,
                DisputeStage.FIRST_LEVEL);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.REPRESENTMENT_REQUIRED,
            5L, TimeUnit.SECONDS);

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileFirstLevelForChargebackCreditApiMocking();

        response = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
            TestDataUtils.UPI_FIRST_LEVEL_CSV_StreamWithCreditEntry(),
            FormDataContentDisposition.name("randomName1").fileName("file1.csv")
                .build(), "AuthToken",serviceUserPrincipal);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.CREDIT_RECEIVED,
            5L, TimeUnit.SECONDS);

        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);

    }

    @Test
    void testUploadUPIFulfilmentDocumentReceivedToCreditReceivedFlow() throws Exception {
        final var transactionReferenceId = IdGenerator.generate("P").getId();

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForCreateChargebackApiMocking();
        MockingUtils.setupPaymentsApiNoRefundExistsMocking(transactionReferenceId, 100, "YBL123");

        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        var response = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
            TestDataUtils.UPIFirstLevelChargebackCSVFileStream(),
            FormDataContentDisposition.name("randomName").fileName("file.csv")
                .build(), "AuthToken",serviceUserPrincipal);

        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);

        var disputeWorkflow = disputeService.
            getDisputeWorkflow(transactionReferenceId, DisputeType.UPI_CHARGEBACK,
                DisputeStage.FIRST_LEVEL);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.REFUND_BLOCKED,
            5L, TimeUnit.SECONDS);

        // Trigger Event for Completing Representment on NPCI
        disputeService.triggerEvent(serviceUserPrincipal.getUserAuthDetails(),
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowEvent.RECEIVE_FULFILMENT_DOCUMENTS,
            Constants.EMPTY_TRANSITION_CONTEXT);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.FULFILMENT_DOCUMENTS_RECEIVED,
            5L, TimeUnit.SECONDS);

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileFirstLevelForChargebackCreditApiMocking();

        response = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
            TestDataUtils.UPI_FIRST_LEVEL_CSV_StreamWithCreditEntry(),
            FormDataContentDisposition.name("randomName1").fileName("file1.csv")
                .build(), "AuthToken",serviceUserPrincipal);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.CREDIT_RECEIVED,
            5L, TimeUnit.SECONDS);


        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForPreArbCreateChargebackApiMocking();

        response = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
            TestDataUtils.UPI_PRE_ARB_CSV_Stream_Raise(),
            FormDataContentDisposition.name("randomName1").fileName("file2.csv")
                .build(), "AuthToken",serviceUserPrincipal);

        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);

        disputeWorkflow = disputeService.
            getDisputeWorkflow(transactionReferenceId, DisputeType.UPI_CHARGEBACK,
                DisputeStage.PRE_ARBITRATION);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.REFUND_BLOCKED,
            5L, TimeUnit.SECONDS);

        // Trigger Event for Completing Representment on NPCI
        disputeService.triggerEvent(serviceUserPrincipal.getUserAuthDetails(),
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK,
            Constants.EMPTY_TRANSITION_CONTEXT);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK,
            5L, TimeUnit.SECONDS);

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForPreArbDebitChargebackApiMocking();

        response = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
            TestDataUtils.UPI_PRE_ARB_CSV_StreamWithDebitEntry(),
            FormDataContentDisposition.name("randomName1").fileName("file3.csv")
                .build(), "AuthToken",serviceUserPrincipal);

        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.DEBIT_RECEIVED,
            5L, TimeUnit.SECONDS);

    }




    @Test
    public void testUploadUPICBFileForFullRefundExists() throws Exception {

        final var transactionReferenceId = IdGenerator.generate("P").getId();

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForCreateChargebackApiMocking();
        MockingUtils.setupPaymentsApiFullRefundExistsMocking(transactionReferenceId, 100, "YBL123");
        MockingUtils.setupEventIngestionApiMocking();
        uploadDataForUPI(transactionReferenceId);

    }

    @Test
    public void testUploadUPICBFileForFullRefundExistsForRedeemMandate() throws Exception {

        final var transactionReferenceId = IdGenerator.generate("P").getId();

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForCreateChargebackApiMocking();
        MockingUtils.setupPaymentsApiFullRefundExistsMockingForRedeemMandate(transactionReferenceId, 100, "YBL123");
        MockingUtils.setupEventIngestionApiMocking();
        uploadDataForUPI(transactionReferenceId);

    }

    @Test
    public void testUploadUPICBFileForFullRefundExistsForRedeemMandateWithMMSFailure() throws Exception {

        final var transactionReferenceId = IdGenerator.generate("P").getId();

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForCreateChargebackApiMocking();
        MockingUtils.setupPaymentsApiFullRefundExistsMockingForRedeemMandateWithMmsFailure(transactionReferenceId, 100, "YBL123");
        MockingUtils.setupEventIngestionApiMocking();
        uploadDataForUPI(transactionReferenceId);
    }

    @Test
    public void testUploadUPICBFileForFullRefundExistsForRedeemMandateWithMMSGivingNull() throws Exception {

        final var transactionReferenceId = IdGenerator.generate("P").getId();
        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForCreateChargebackApiMocking();
        MockingUtils.setupPaymentsApiFullRefundExistsMockingForRedeemMandateWithMmsGivingNull(transactionReferenceId, 100, "YBL123");
        MockingUtils.setupEventIngestionApiMocking();
        var response = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
            TestDataUtils.UPIFirstLevelChargebackCSVFileStream(),
            FormDataContentDisposition.name("randomName").fileName("file.csv")
                .build(), "AuthToken",serviceUserPrincipal);
        AssertionUtils.assertRowStateEquals(RowState.FAILED, response.getId(), 15, TimeUnit.SECONDS);
    }


    @Test
    public void testUploadUPICBFileForFullRefundExistsForPayMandate() throws Exception {

        final var transactionReferenceId = IdGenerator.generate("P").getId();
        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForCreateChargebackApiMocking();
        MockingUtils.setupPaymentsApiFullRefundExistsMockingForPayMandate(transactionReferenceId, 100, "YBL123");
        MockingUtils.setupEventIngestionApiMocking();

        var response = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
            TestDataUtils.UPIFirstLevelChargebackCSVFileStream(),
            FormDataContentDisposition.name("randomName").fileName("file.csv")
                .build(), "AuthToken",serviceUserPrincipal);
        AssertionUtils.assertRowStateEquals(RowState.FAILED, response.getId(), 15, TimeUnit.SECONDS);

    }


    @Test
    public void testUploadUPICBFileForPreArbNoRefund() throws Exception {

        final var transactionReferenceId = IdGenerator.generate("P").getId();

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForCreateChargebackApiMocking();
        MockingUtils.setupPaymentsApiNoRefundExistsMocking(transactionReferenceId, 100, "YBL123");
        Mockito.doReturn(true).when(olympusIMClient).verifyPermission(Mockito.any(), Mockito.any());

        final var userAuthDetails = TestDataUtils.getOlympusAuthDetails();
        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        var response = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
            TestDataUtils.UPIFirstLevelChargebackCSVFileStream(),
            FormDataContentDisposition.name("randomName").fileName("file.csv")
                .build(), "AuthToken",serviceUserPrincipal);

        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);

        var disputeWorkflow = disputeService.
            getDisputeWorkflow(transactionReferenceId, DisputeType.UPI_CHARGEBACK,
                DisputeStage.FIRST_LEVEL);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.REFUND_BLOCKED,
            5L, TimeUnit.SECONDS);

        // Trigger Event for Completing Representment on NPCI
        disputeService.triggerEvent(
            userAuthDetails,
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowEvent.RECEIVE_FULFILMENT_DOCUMENTS,
            Constants.EMPTY_TRANSITION_CONTEXT);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.FULFILMENT_DOCUMENTS_RECEIVED,
            5L, TimeUnit.SECONDS);

        // Trigger Event for Completing Representment on NPCI
        disputeService.triggerEvent(
            userAuthDetails,
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowEvent.COMPLETE_NPCI_REPRESENTMENT,
            Constants.EMPTY_TRANSITION_CONTEXT);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.NPCI_REPRESENTMENT_COMPLETED,
            5L, TimeUnit.SECONDS);

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileFirstLevelForChargebackCreditApiMocking();

        response = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
            TestDataUtils.UPI_FIRST_LEVEL_CSV_StreamWithCreditEntry(),
            FormDataContentDisposition.name("randomName1").fileName("file1.csv")
                .build(), "AuthToken",serviceUserPrincipal);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.CREDIT_RECEIVED,
            5L, TimeUnit.SECONDS);

        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForPreArbCreateChargebackApiMocking();

        response = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
            TestDataUtils.UPI_PRE_ARB_CSV_Stream_Raise(),
            FormDataContentDisposition.name("randomName1").fileName("file2.csv")
                .build(), "AuthToken",serviceUserPrincipal);

        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);

        disputeWorkflow = disputeService.
            getDisputeWorkflow(transactionReferenceId, DisputeType.UPI_CHARGEBACK,
                DisputeStage.PRE_ARBITRATION);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.REFUND_BLOCKED,
            5L, TimeUnit.SECONDS);

        // Trigger Event for Completing Representment on NPCI
        disputeService.triggerEvent(userAuthDetails,
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK,
            Constants.EMPTY_TRANSITION_CONTEXT);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK,
            5L, TimeUnit.SECONDS);

        // Trigger Event for Completing Representment on NPCI
        disputeService.triggerEvent(
            userAuthDetails,
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowEvent.COMPLETE_NPCI_ACCEPTANCE,
            Constants.EMPTY_TRANSITION_CONTEXT);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.NPCI_ACCEPTANCE_COMPLETED,
            5L, TimeUnit.SECONDS);

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForPreArbDebitChargebackApiMocking();

        response = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
            TestDataUtils.UPI_PRE_ARB_CSV_StreamWithDebitEntry(),
            FormDataContentDisposition.name("randomName1").fileName("file3.csv")
                .build(),"AuthToken",serviceUserPrincipal);

        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.DEBIT_RECEIVED,
            5L, TimeUnit.SECONDS);


    }

    @Test
    public void testUploadUPICBFileWithPaymentFailures() throws Exception {
        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForCreateChargebackApiMocking();
        Mockito.doReturn(true).when(olympusIMClient).verifyPermission(Mockito.any(), Mockito.any());

        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        final var response = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
            TestDataUtils.UPIFirstLevelChargebackCSVFileStream(),
            FormDataContentDisposition.name("randomName123").fileName("file123.csv")
                .build(),"AuthToken", serviceUserPrincipal);

        AssertionUtils.assertErrorRowUpdated(response.getId());
    }


    @Test
    public void testUploadUPIPreArbWhenFirstLevelExist() throws Exception {

        final var dispute = TestDataUtils
            .getServiceChargeBackDispute(DisputeType.UPI_CHARGEBACK, DisputeStage.FIRST_LEVEL);
        disputeRepository.save(dispute);
        final var disputeWorkflow = TestDataUtils.getDisputeWorkflow(dispute);
        disputeWorkflowRepository.save(disputeWorkflow);
        final var transactionReferenceId = dispute.getTransactionReferenceId();
        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForPreArbCreateChargebackForGivenDisputeApiMocking(dispute);
        MockingUtils.setupPaymentsApiFullRefundExistsMocking(transactionReferenceId,
            dispute.getTransactionAmount(), "YBL123");
        Mockito.doReturn(true).when(olympusIMClient).verifyPermission(Mockito.any(), Mockito.any());

        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        final var response = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
            TestDataUtils.UPI_PRE_ARB_FOR_GIVEN_DISPUTE_CSV_Stream_Raise(dispute),
            FormDataContentDisposition.name("randomName1").fileName("file2.csv")
                .build(),"AuthToken", serviceUserPrincipal);
        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);
    }

    @Test
    public  void testUploadUPIPreArbWhenNoFirstLevelExist() throws Exception{
        final var transactionReferenceId = IdGenerator.generate("P").getId();
        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForPreArbCreateChargebackApiMocking();
        Mockito.doReturn(true).when(olympusIMClient).verifyPermission(Mockito.any(), Mockito.any());

        MockingUtils.setupPaymentsApiFullRefundExistsMocking(transactionReferenceId, 100, "YBL123");

        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        var response = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
            TestDataUtils.UPI_PRE_ARB_CSV_Stream_Raise(),
            FormDataContentDisposition.name("randomName1").fileName("file27.csv")
                .build(),"AuthToken", serviceUserPrincipal);
        AssertionUtils.assertFirstLevelDisputeNotFound(response.getId(), 20, TimeUnit.SECONDS);
    }

    @Test
    public void testFileUploadLockExists() throws Exception{

        final var transactionReferenceId = IdGenerator.generate("P").getId();
        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForPreArbCreateChargebackApiMocking();
        Mockito.doReturn(true).when(olympusIMClient).verifyPermission(Mockito.any(), Mockito.any());

        MockingUtils.setupPaymentsApiFullRefundExistsMocking(transactionReferenceId, 100, "YBL123");

        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        var lockKey = FileUploadLockKey.builder()
            .fileName("file_re_upload.csv")
            .build();

        fileUploadLockCommand.strictSave(lockKey, lockKey.getKey());

        var error = Assertions.assertThrows(DisputeException.class, () -> fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
            TestDataUtils.UPI_PRE_ARB_CSV_Stream_Raise(),
            FormDataContentDisposition.name("randomName1").fileName("file_re_upload.csv")
                .build(),"AuthToken", serviceUserPrincipal));

        Assertions.assertEquals(StratosErrorCodeKey.FILE_LOCK, error.getErrorCode());

    }

    @Test
    public void testFileUploadLockRemovedWithUnexpectedErrors() throws Exception{

        // Mock the fail doc upload
        MockingUtils.setupDocstoreUploadApiMocking(false, 500);
        MockingUtils.setupDocstoreGetFileForPreArbCreateChargebackApiMocking();

        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        var lockKey = FileUploadLockKey.builder()
            .fileName("file_re_upload.csv")
            .build();

        // Delete if any lock already set from any other test case
        fileUploadLockCommand.delete(lockKey);

        var error = Assertions.assertThrows(DisputeException.class, () -> fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
            TestDataUtils.UPI_PRE_ARB_CSV_Stream_Raise(),
            FormDataContentDisposition.name("randomName1").fileName("file_re_upload.csv")
                .build(),"AuthToken", serviceUserPrincipal));

        // Assert that this is a communication error
        Assertions.assertEquals(StratosErrorCodeKey.COMMUNICATION_ERROR, error.getErrorCode());

        // Assert that the lock is removed
        Assertions.assertNull(fileUploadLockCommand.get(lockKey));

    }

    @Test
    public void testUPIFileUploadDifferedChargebackRaise() throws Exception {

        final var transactionReferenceId = IdGenerator.generate("P").getId();
        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();
        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileFirstLevelDifferedChargeBackRaiseApiMocking();
        MockingUtils.setupEventIngestionApiMocking();
        MockingUtils.setupPaymentsApiNoRefundExistsMocking(transactionReferenceId, 100, "YBL123");

        var response = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
            TestDataUtils.UPIDifferedChargebackCSVFileStream("Differed Chargeback Raise"),
            FormDataContentDisposition.name("randomName").fileName("file.csv")
                .build(),"AuthToken", serviceUserPrincipal);

        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);

        String disputeWorkflowId = disputeService.getDisputeWorkflow(transactionReferenceId,
            DisputeType.UPI_CHARGEBACK, DisputeStage.FIRST_LEVEL).getDisputeWorkflowId();
        AssertionUtils.assertDisputeWorkflowStateAndCategoryEquals(
            transactionReferenceId, disputeWorkflowId, DisputeWorkflowState.REFUND_BLOCKED,
            DisputeCategory.DIFFERED_CHARGEBACK, 30, TimeUnit.SECONDS
        );

    }

    @Test
    public void testUPIDifferedChargebackRepresentmentRaise() throws Exception {

        final var transactionReferenceId = IdGenerator.generate("P").getId();

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileFirstLevelDifferedChargeBackRaiseApiMocking();
        MockingUtils.setupPaymentsApiNoRefundExistsMocking(transactionReferenceId, 100, "YBL123");

        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        var response = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
            TestDataUtils.UPIDifferedChargebackCSVFileStream("Differed Chargeback Raise"),
            FormDataContentDisposition.name("randomName").fileName("file.csv")
                .build(),"AuthToken", serviceUserPrincipal);

        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);

        var disputeWorkflow = disputeService.
            getDisputeWorkflow(transactionReferenceId, DisputeType.UPI_CHARGEBACK,
                DisputeStage.FIRST_LEVEL);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.REFUND_BLOCKED,
            5L, TimeUnit.SECONDS);

        // Trigger Event for Completing Representment on NPCI
        disputeService.triggerEvent(serviceUserPrincipal.getUserAuthDetails(),
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowEvent.RECEIVE_FULFILMENT_DOCUMENTS,
            Constants.EMPTY_TRANSITION_CONTEXT);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.FULFILMENT_DOCUMENTS_RECEIVED,
            5L, TimeUnit.SECONDS);

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileFirstLevelDifferedChargeBackRepresentmentRaiseApiMocking();

        response = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
            TestDataUtils.UPIDifferedChargebackCSVFileStream("Differed Re-presentment Raise"),
            FormDataContentDisposition.name("randomName1").fileName("file1.csv")
                .build(),"AuthToken", serviceUserPrincipal);

        AssertionUtils.assertDisputeWorkflowStateAndCategoryEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.CREDIT_RECEIVED, DisputeCategory.DIFFERED_CHARGEBACK,
            5L, TimeUnit.SECONDS);
    }


    @Test
    public void testUPIDifferedPreArbRaise() throws Exception {

        final var transactionReferenceId = IdGenerator.generate("P").getId();

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileFirstLevelDifferedChargeBackRaiseApiMocking();
        MockingUtils.setupPaymentsApiNoRefundExistsMocking(transactionReferenceId, 100, "YBL123");
        Mockito.doReturn(true).when(olympusIMClient).verifyPermission(Mockito.any(), Mockito.any());

        final var userAuthDetails = TestDataUtils.getOlympusAuthDetails();
        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        var response = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
            TestDataUtils.UPIFirstLevelChargebackCSVFileStream(),
            FormDataContentDisposition.name("randomName").fileName("file.csv")
                .build(),"AuthToken", serviceUserPrincipal);

        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);

        var disputeWorkflow = disputeService.
            getDisputeWorkflow(transactionReferenceId, DisputeType.UPI_CHARGEBACK,
                DisputeStage.FIRST_LEVEL);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.REFUND_BLOCKED,
            5L, TimeUnit.SECONDS);

        // Trigger Event for received fulfilment documents
        disputeService.triggerEvent(
            userAuthDetails,
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowEvent.RECEIVE_FULFILMENT_DOCUMENTS,
            Constants.EMPTY_TRANSITION_CONTEXT);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.FULFILMENT_DOCUMENTS_RECEIVED,
            5L, TimeUnit.SECONDS);

        // Trigger Event for Completing Representment on NPCI
        disputeService.triggerEvent(
            userAuthDetails,
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowEvent.COMPLETE_NPCI_REPRESENTMENT,
            Constants.EMPTY_TRANSITION_CONTEXT);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.NPCI_REPRESENTMENT_COMPLETED,
            5L, TimeUnit.SECONDS);

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileFirstLevelDifferedChargeBackRepresentmentRaiseApiMocking();

        response = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
            TestDataUtils.UPIDifferedChargebackCSVFileStream("Differed Pre-Arbitration Raise"),
            FormDataContentDisposition.name("randomName1").fileName("file1.csv")
                .build(),"AuthToken", serviceUserPrincipal);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.CREDIT_RECEIVED,
            5L, TimeUnit.SECONDS);

        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setUpDocstoreGetPreArbDifferedChargebackRaiseApiMocking();

        response = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
            TestDataUtils.UPIDifferedChargebackCSVFileStream("Differed Pre-Arbitration Raise"),
            FormDataContentDisposition.name("randomName1").fileName("file2.csv")
                .build(),"AuthToken", serviceUserPrincipal);

        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);

        disputeWorkflow = disputeService.
            getDisputeWorkflow(transactionReferenceId, DisputeType.UPI_CHARGEBACK,
                DisputeStage.PRE_ARBITRATION);

        AssertionUtils.assertDisputeWorkflowStateAndCategoryEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.REFUND_BLOCKED,DisputeCategory.DIFFERED_CHARGEBACK,
            5L, TimeUnit.SECONDS);

    }


    @Test
    public void testUPIDifferedChargebackDebitSignalReceived() throws Exception {

        final var transactionReferenceId = IdGenerator.generate("P").getId();

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileFirstLevelDifferedChargeBackRaiseApiMocking();
        MockingUtils.setupPaymentsApiNoRefundExistsMocking(transactionReferenceId, 100, "YBL123");
        Mockito.doReturn(true).when(olympusIMClient).verifyPermission(Mockito.any(), Mockito.any());

        final var userAuthDetails = TestDataUtils.getOlympusAuthDetails();
        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        var response = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
            TestDataUtils.UPIFirstLevelChargebackCSVFileStream(),
            FormDataContentDisposition.name("randomName").fileName("file.csv")
                .build(),"AuthToken", serviceUserPrincipal);

        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);

        var disputeWorkflow = disputeService.
            getDisputeWorkflow(transactionReferenceId, DisputeType.UPI_CHARGEBACK,
                DisputeStage.FIRST_LEVEL);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.REFUND_BLOCKED,
            5L, TimeUnit.SECONDS);

        // Trigger Event for received fulfilment documents
        disputeService.triggerEvent(
            userAuthDetails,
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowEvent.RECEIVE_FULFILMENT_DOCUMENTS,
            Constants.EMPTY_TRANSITION_CONTEXT);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.FULFILMENT_DOCUMENTS_RECEIVED,
            5L, TimeUnit.SECONDS);

        // Trigger Event for Completing Representment on NPCI
        disputeService.triggerEvent(
            userAuthDetails,
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowEvent.COMPLETE_NPCI_REPRESENTMENT,
            Constants.EMPTY_TRANSITION_CONTEXT);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.NPCI_REPRESENTMENT_COMPLETED,
            5L, TimeUnit.SECONDS);

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileFirstLevelDifferedChargeBackRepresentmentRaiseApiMocking();

        response = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
            TestDataUtils.UPIDifferedChargebackCSVFileStream("Differed Pre-Arbitration Raise"),
            FormDataContentDisposition.name("randomName1").fileName("file1.csv")
                .build(),"AuthToken", serviceUserPrincipal);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.CREDIT_RECEIVED,
            5L, TimeUnit.SECONDS);

        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setUpDocstoreGetPreArbDifferedChargebackRaiseApiMocking();

        response = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
            TestDataUtils.UPIDifferedChargebackCSVFileStream("Differed Pre-Arbitration Raise"),
            FormDataContentDisposition.name("randomName1").fileName("file2.csv")
                .build(),"AuthToken", serviceUserPrincipal);

        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);

        disputeWorkflow = disputeService.
            getDisputeWorkflow(transactionReferenceId, DisputeType.UPI_CHARGEBACK,
                DisputeStage.PRE_ARBITRATION);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.REFUND_BLOCKED,
            5L, TimeUnit.SECONDS);

        //Trigger event for Merchant Accepted Chargeback

        disputeService.triggerEvent(
            userAuthDetails,
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK,
            Constants.EMPTY_TRANSITION_CONTEXT);

        // Trigger event of NPCI acceptance completed

        disputeService.triggerEvent(
            userAuthDetails,
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowEvent.COMPLETE_NPCI_ACCEPTANCE,
            Constants.EMPTY_TRANSITION_CONTEXT);

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setUpDocstoreGetPreArbDifferedChargebackAcceptanceApiMocking();

        response = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
            TestDataUtils.UPIDifferedChargebackCSVFileStream("Differed Pre-Arbitration Acceptance"),
            FormDataContentDisposition.name("randomName1").fileName("file3.csv")
                .build(),"AuthToken", serviceUserPrincipal);

        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);

        disputeWorkflow = disputeService.
            getDisputeWorkflow(transactionReferenceId, DisputeType.UPI_CHARGEBACK,
                DisputeStage.PRE_ARBITRATION);

        AssertionUtils.assertDisputeWorkflowStateAndCategoryEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.DEBIT_RECEIVED,DisputeCategory.DIFFERED_CHARGEBACK,
            5L, TimeUnit.SECONDS);

    }

}
