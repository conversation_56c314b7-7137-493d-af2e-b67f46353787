package com.phonepe.merchant.platform.stratos.server.integrationTests.file.pg;

import com.phonepe.merchant.platform.stratos.models.disputes.DisputeTypeDto;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.models.files.FileTypeDto;
import com.phonepe.merchant.platform.stratos.server.BaseTest;
import com.phonepe.merchant.platform.stratos.server.core.resources.FileResource;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.olympus.im.client.OlympusIMClient;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class
PgFileUploadIntegrationTpslDateFormatTest extends BaseTest {

    private FileResource fileResource;
    private DisputeService disputeService;
    private OlympusIMClient olympusIMClient;

    public PgFileUploadIntegrationTpslDateFormatTest() {
        super("config/test-tpsl-date.yml");
    }

    @BeforeEach
    void setUpGuiceInjection() {
        fileResource = guiceInjector.getInstance(FileResource.class);
        disputeService = guiceInjector.getInstance(DisputeService.class);
        olympusIMClient = guiceInjector.getInstance(OlympusIMClient.class);
        MARIA_DB_CONTAINER.executeQuery("TRUNCATE stratos_shard_1.file;\n"
            + "TRUNCATE stratos_shard_2.file;\n"
            + "TRUNCATE stratos_shard_1.file_audit;\n"
            + "TRUNCATE stratos_shard_2.file_audit;\n"
            + "TRUNCATE stratos_shard_1.row;\n"
            + "TRUNCATE stratos_shard_2.row;\n"
            + "TRUNCATE stratos_shard_1.row_audit;\n"
            + "TRUNCATE stratos_shard_2.row_audit;", true);
        truncateDb();
    }

    @Test
    void testInvalidDate() throws Exception {
        String pgName = "HDFC Bank";
        final var transactionReferenceId = IdGenerator.generate("T").getId();

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForCreateChargebackApiMocking(pgName, "01-Jan-21");
        MockingUtils.setupPgTransportTransactionDetailMocking("PG1234",
            transactionReferenceId);
        MockingUtils.setupPaymentsApiFullRefundExistsMockingPaidFromDebitCard(
            transactionReferenceId, 100, "PG1234");
        MockingUtils.setupEventIngestionApiMocking();
        Mockito.doReturn(true).when(olympusIMClient).verifyPermission(Mockito.any(), Mockito.any());

        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        var error = Assertions.assertThrows(DisputeException.class, ()->{
            fileResource.upload(DisputeTypeDto.PG_CHARGEBACK,
                FileTypeDto.PG_FIRST_LEVEL, TestDataUtils.pgFirstLevelChargebackCSVFileStreamFromPgName(pgName, "01-Jan-21"),
                FormDataContentDisposition.name("randomName").fileName("file.csv")
                    .build(),"AuthToken", serviceUserPrincipal);
        });

        Assertions.assertEquals(StratosErrorCodeKey.INVALID_FILE, error.getErrorCode());
    }
}