package com.phonepe.merchant.platform.stratos.server.integrationTests.file.wallet.beans;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WalletChargebackBean {

    @JsonProperty("Adjdate")
    private String adjDate;
    @JsonProperty("Txndate")
    private String txnDate;
    @JsonProperty("Adjtype")
    private String adjType;
    @JsonProperty("Remitter")
    private String remitter;
    @JsonProperty("Beneficiery")
    private String beneficiery;
    @JsonProperty("RRN")
    private String rrn;
    @JsonProperty("Adjamount")
    private String adjAmount;
    @JsonProperty("Compensation amount")
    private String compensationAmount;
    @JsonProperty("UPI Transaction ID")
    private String upiTransactionId;
    @JsonProperty("Txnamount")
    private String txnAmount;
}
