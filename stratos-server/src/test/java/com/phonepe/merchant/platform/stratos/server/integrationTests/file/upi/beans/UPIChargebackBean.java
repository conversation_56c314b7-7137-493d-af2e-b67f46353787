package com.phonepe.merchant.platform.stratos.server.integrationTests.file.upi.beans;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UPIChargebackBean {

    @JsonProperty("Adjdate")
    private String adjDate;
    @JsonProperty("Txndate")
    private String txnDate;
    @JsonProperty("Adjtype")
    private String adjType;
    @JsonProperty("Beneficiery")
    private String beneficiary;
    @JsonProperty("RRN")
    private String rrn;
    @JsonProperty("Adjamount")
    private String adjAmount;
    @JsonProperty("Compensation amount")
    private String compensationAmount;
    @JsonProperty("UPI Transaction ID")
    private String upiTransactionId;
    @JsonProperty("Txnamount")
    private String txnAmount;
}
