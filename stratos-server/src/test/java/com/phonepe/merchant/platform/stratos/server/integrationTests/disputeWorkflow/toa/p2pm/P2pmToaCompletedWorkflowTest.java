package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.toa.p2pm;

import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.DisputeWorkflowKey;
import com.phonepe.merchant.platform.stratos.server.LoadOnlyOnClassLevelBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.ToaDisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.NeuronTestUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.resources.ToaResource;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.models.payments.merchant.MerchantTransactionState;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.IntStream;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class P2pmToaCompletedWorkflowTest extends LoadOnlyOnClassLevelBaseTest {

    private ToaResource toaResource;

    @BeforeEach
    void setUpGuiceInjection() {
        toaResource = guiceInjector.getInstance(ToaResource.class);
        MockingUtils.setupEventIngestionApiMocking();
        MockingUtils.disableKillSwitchMocking();
        truncateDb();
    }

    /*
   Test: All Success Completed flow
   WORKFLOW: PULSE ->
                   RECEIVED -> P2PM_TOA_INITIATED -> P2PM_TOA_COMPLETED
   */
    @Test
    void testCompletedToaWithAllSuccess() {

        final var transactionReferenceId = IdGenerator.generate("T").getId();

        MockingUtils.setDeemFailureUpiTransactionDetail(transactionReferenceId, 120);  // SUCCESS txn details api
        MockingUtils.setUpPaymentPayApi(true, 200); // SUCCESS pay api

        String merchantOrderId = "DEEM0-" + transactionReferenceId;

        // mock transaction details status api
        MockingUtils.setMerchantTransactionsStatus("IRCTCINAPP", merchantOrderId,
            MerchantTransactionState.COMPLETED, 120);

        // Create a pulse
        NeuronTestUtils.createP2pmToaPulse(transactionReferenceId);

        // wait till the entry for workflow is created
        DisputeWorkflow disputeWorkflow = AssertionUtils
            .assertAndGetDisputeWorkflowCreation(transactionReferenceId, DisputeType.P2PM_TOA,
                DisputeStage.FIRST_LEVEL);

        // wait till the metadata for workflow is created
        List<ToaDisputeMetadata> disputeMetadataList = AssertionUtils.getToaDisputeMetadata(
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId());
        Assertions.assertEquals(1, disputeMetadataList.size());
        ToaDisputeMetadata toaDisputeMetadata = disputeMetadataList.get(0);

        // Assert Metadata
        Assertions.assertEquals(merchantOrderId, toaDisputeMetadata.getMerchantOrderId());
        Assertions.assertEquals(transactionReferenceId, toaDisputeMetadata.getTransactionReferenceId());
        Assertions.assertEquals(disputeWorkflow.getDisputeWorkflowId(), toaDisputeMetadata.getDisputeWorkflowId());

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.P2PM_TOA_COMPLETED,
            30L, TimeUnit.SECONDS);
    }

    /*
    Test: Complete flow with initialisation fail then re-initiate
    WORKFLOW: PULSE ->
                  RECEIVED -> P2PM_TOA_INITIATION_FAILED ->
                        retryToa
                  P2PM_TOA_INITIATION_FAILED -> P2PM_TOA_INITIATED
                  P2PM_TOA_INITIATED -> P2PM_TOA_COMPLETED
    */
    @Test
    void testWithInitializationFailedReinitializeThenSuccess() {

        final var transactionReferenceId = IdGenerator.generate("T").getId();
        String merchantOrderId = "DEEM0-" + transactionReferenceId;
        final var manualRoleUser = TestDataUtils.getOlympusUser();

        MockingUtils.setDeemFailureUpiTransactionDetail(transactionReferenceId, 120); // SUCCESS txn details api

        // FAIL Payment pay api
        MockingUtils.setUpPaymentPayApi(false, 200);

        // Create a pulse
        NeuronTestUtils.createP2pmToaPulse(transactionReferenceId);

        // wait till the entry for workflow is created
        DisputeWorkflow disputeWorkflow = AssertionUtils
            .assertAndGetDisputeWorkflowCreation(transactionReferenceId, DisputeType.P2PM_TOA,
                DisputeStage.FIRST_LEVEL);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.P2PM_TOA_INITIATION_FAILED,
            30L, TimeUnit.SECONDS);

        // SUCCESS Payment pay api
        MockingUtils.setUpPaymentPayApi(true, 200);

        // mock transaction details status api
        MockingUtils.setMerchantTransactionsStatus("IRCTCINAPP", merchantOrderId,
            MerchantTransactionState.COMPLETED, 120);


        // Trigger RE_INITIATE_TOA event
        var response = toaResource.retryToa(manualRoleUser, DisputeWorkflowKey.builder()
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .transactionReferenceId(transactionReferenceId)
            .build(), DisputeType.P2PM_TOA);

        // Assert Retry success
        Assertions.assertTrue(response.isSuccess());
        AssertionUtils.assertToaSummary(response.getData(), disputeWorkflow, DisputeWorkflowState.P2PM_TOA_INITIATED);

        // Toa should be in initiated immediately because of in sync retry
        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.P2PM_TOA_INITIATED,
            0, TimeUnit.SECONDS);

        // wait till the metadata for workflow is created
        List<ToaDisputeMetadata> disputeMetadataList = AssertionUtils.getToaDisputeMetadata(
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId());
        Assertions.assertEquals(1, disputeMetadataList.size());
        ToaDisputeMetadata toaDisputeMetadata = disputeMetadataList.get(0);
        Assertions.assertEquals(merchantOrderId, toaDisputeMetadata.getMerchantOrderId());
        Assertions.assertEquals(transactionReferenceId, toaDisputeMetadata.getTransactionReferenceId());
        Assertions.assertEquals(disputeWorkflow.getDisputeWorkflowId(), toaDisputeMetadata.getDisputeWorkflowId());

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.P2PM_TOA_COMPLETED,
            30L, TimeUnit.SECONDS);
    }

    /*
    Test: Complete flow with pending then reconcile
    WORKFLOW: PULSE ->
                  RECEIVED -> P2PM_TOA_INITIATED -> P2PM_TOA_PENDING
                    reconcileToa
                  P2PM_TOA_PENDING -> P2PM_TOA_COMPLETED
    */
    @Test
    void testToaCompletedWithReconcile(){
        final var transactionReferenceId = IdGenerator.generate("T").getId();
        String merchantOrderId = "DEEM0-" + transactionReferenceId;
        final var manualRoleUser = TestDataUtils.getOlympusUser();

        MockingUtils.setDeemFailureUpiTransactionDetail(transactionReferenceId, 120);  // SUCCESS txn details api
        MockingUtils.setUpPaymentPayApi(true, 200); // SUCCESS pay api

        // mock transaction details status api o pending
        MockingUtils.setMerchantTransactionsStatus("IRCTCINAPP", merchantOrderId,
            MerchantTransactionState.CREATED, 120);

        // Create a pulse
        NeuronTestUtils.createP2pmToaPulse(transactionReferenceId);

        // wait till the entry for workflow is created
        DisputeWorkflow disputeWorkflow = AssertionUtils
            .assertAndGetDisputeWorkflowCreation(transactionReferenceId, DisputeType.P2PM_TOA,
                DisputeStage.FIRST_LEVEL);

        // Toa went to pending
        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.P2PM_TOA_PENDING,
            30L, TimeUnit.SECONDS);

        // mock transaction details status api o pending
        MockingUtils.setMerchantTransactionsStatus("IRCTCINAPP", merchantOrderId,
            MerchantTransactionState.ACCEPTED, 120);

        // reconcileToa
        var response = toaResource.reconcileToa(manualRoleUser, DisputeWorkflowKey.builder()
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .transactionReferenceId(transactionReferenceId)
            .build(), DisputeType.P2PM_TOA);

        Assertions.assertFalse(response.isSuccess());
        AssertionUtils.assertToaSummary(response.getData(), disputeWorkflow, DisputeWorkflowState.P2PM_TOA_PENDING);

        // mock transaction details status api o pending
        MockingUtils.setMerchantTransactionsStatus("IRCTCINAPP", merchantOrderId,
            MerchantTransactionState.COMPLETED, 120);

        response = toaResource.reconcileToa(manualRoleUser, DisputeWorkflowKey.builder()
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .transactionReferenceId(transactionReferenceId)
            .build(), DisputeType.P2PM_TOA);

        Assertions.assertTrue(response.isSuccess());
        AssertionUtils.assertToaSummary(response.getData(), disputeWorkflow, DisputeWorkflowState.P2PM_TOA_COMPLETED);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.P2PM_TOA_COMPLETED,
            0L, TimeUnit.SECONDS);

        // Reconciled ignored when reconciled in invalid state
        response = toaResource.reconcileToa(manualRoleUser, DisputeWorkflowKey.builder()
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .transactionReferenceId(transactionReferenceId)
            .build(), DisputeType.P2PM_TOA);

        Assertions.assertFalse(response.isSuccess());
    }

    /*
    Test: Complete flow with retry
    WORKFLOW: PULSE ->
                  RECEIVED -> P2PM_TOA_INITIATED -> P2PM_TOA_FAILED -> P2PM_TOA_FAILED_AFTER_MAX_AUTO_RETRY
                  retryToa
                  P2PM_TOA_FAILED_AFTER_MAX_AUTO_RETRY -> P2PM_TOA_INITIATED -> P2PM_TOA_COMPLETED
    */
    @Test
    void testToaCompletedWithRetryAfterMaxFailedAndThenRetry(){

        final var transactionReferenceId = IdGenerator.generate("T").getId();
        final var manualRoleUser = TestDataUtils.getOlympusUser();
        ArrayList<String> merchantOrderIds = new ArrayList<>();
        IntStream.range(0,3)
            .forEach((i) ->
                merchantOrderIds.add(String.format("DEEM%d-%s",i,transactionReferenceId)));

        MockingUtils.setDeemFailureUpiTransactionDetail(transactionReferenceId, 120);  // SUCCESS txn details api
        MockingUtils.setUpPaymentPayApi(true, 200); // SUCCESS pay api

        // mock transaction details status api o pending for all three retry
        merchantOrderIds
            .forEach((id) ->
                MockingUtils.setMerchantTransactionsStatus("IRCTCINAPP", id, MerchantTransactionState.FAILED,
                        120, 1000));

        // Create a pulse
        NeuronTestUtils.createP2pmToaPulse(transactionReferenceId);

        // wait till the entry for workflow is created
        DisputeWorkflow disputeWorkflow = AssertionUtils
            .assertAndGetDisputeWorkflowCreation(transactionReferenceId, DisputeType.P2PM_TOA,
                DisputeStage.FIRST_LEVEL);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.P2PM_TOA_FAILED_AFTER_MAX_AUTO_RETRY,
            30L, TimeUnit.SECONDS);

        final List<ToaDisputeMetadata> disputeMetadataList = AssertionUtils.getToaDisputeMetadata(
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId());
        assertToaMetadataList(merchantOrderIds,disputeMetadataList);

        merchantOrderIds.add("DEEM3-" + transactionReferenceId);
        MockingUtils.setMerchantTransactionsStatus("IRCTCINAPP", merchantOrderIds.get(3),
                MerchantTransactionState.COMPLETED, 120, 1000);

        var response = toaResource.retryToa(manualRoleUser, DisputeWorkflowKey.builder()
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .transactionReferenceId(transactionReferenceId)
            .build(), DisputeType.P2PM_TOA);

        Assertions.assertTrue(response.isSuccess(), "Retry toa api failed");
        AssertionUtils.assertToaSummary(response.getData(), disputeWorkflow, DisputeWorkflowState.P2PM_TOA_INITIATED);
        final var newDisputeMetadataList = AssertionUtils.getToaDisputeMetadata(
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId());
        assertToaMetadataList(merchantOrderIds,newDisputeMetadataList);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.P2PM_TOA_COMPLETED,
            10L, TimeUnit.SECONDS);

    }

    /*
    Test: Complete flow with externally failed after max failed retry
    WORKFLOW: PULSE ->
                  RECEIVED -> P2PM_TOA_INITIATED -> P2PM_TOA_FAILED -> P2PM_TOA_FAILED_AFTER_MAX_AUTO_RETRY
                    processToaExternally
                  P2PM_TOA_FAILED_AFTER_MAX_AUTO_RETRY -> TOA_COMPLETED_EXTERNALLY
    */
    @Test
    void testToaCompletedExternallyWithRetryAfterMaxFailed(){
        final var transactionReferenceId = IdGenerator.generate("T").getId();
        final var manualRoleUser = TestDataUtils.getOlympusUser();
        ArrayList<String> merchantOrderIds = new ArrayList<>();
        IntStream.range(0,3)
            .forEach((i) ->
                merchantOrderIds.add(String.format("DEEM%d-%s",i,transactionReferenceId)));

        MockingUtils.setDeemFailureUpiTransactionDetail(transactionReferenceId, 120);  // SUCCESS txn details api
        MockingUtils.setUpPaymentPayApi(true, 200); // SUCCESS pay api

        // mock transaction details status api o pending for all three retry
        merchantOrderIds
            .forEach((id) ->
                MockingUtils.setMerchantTransactionsStatus("IRCTCINAPP", id, MerchantTransactionState.FAILED, 120));

        // Create a pulse
        NeuronTestUtils.createP2pmToaPulse(transactionReferenceId);

        // wait till the entry for workflow is created
        DisputeWorkflow disputeWorkflow = AssertionUtils
            .assertAndGetDisputeWorkflowCreation(transactionReferenceId, DisputeType.P2PM_TOA,
                DisputeStage.FIRST_LEVEL);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.P2PM_TOA_FAILED_AFTER_MAX_AUTO_RETRY,
            30L, TimeUnit.SECONDS);

        var response = toaResource.processToaExternally(manualRoleUser, transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(), TestDataUtils.getCommentContext(), DisputeType.P2PM_TOA);


        Assertions.assertTrue(response.isSuccess(), "processToaExternally failed");
        AssertionUtils.assertToaSummary(response.getData(), disputeWorkflow, DisputeWorkflowState.TOA_COMPLETED_EXTERNALLY);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.TOA_COMPLETED_EXTERNALLY,
            0, TimeUnit.SECONDS);
    }

    private void assertToaMetadataList(final ArrayList<String> merchantOrderIds, final List<ToaDisputeMetadata> disputeMetadataList){
        int retryCount = merchantOrderIds.size();
        Assertions.assertEquals(retryCount, disputeMetadataList.size());
        // Assert all toa metadata in reverse order
        IntStream.range(0,retryCount)
            .forEach((i) ->  Assertions.assertEquals(merchantOrderIds.get(i), disputeMetadataList.get(retryCount-1-i).getMerchantOrderId() ));
    }
}
