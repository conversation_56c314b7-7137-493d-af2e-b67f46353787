package com.phonepe.merchant.platform.stratos.server.integrationTests.housekeeping;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeTypeDto;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.models.files.FileTypeDto;
import com.phonepe.merchant.platform.stratos.server.BaseTest;
import com.phonepe.merchant.platform.stratos.server.LoadOnlyOnClassLevelBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.PrimaryKey;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Row;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Row.Fields;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.FileRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.RowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.*;
import com.phonepe.merchant.platform.stratos.server.core.resources.housekeeping.HousekeepingResource;
import com.phonepe.merchant.platform.stratos.server.core.resources.housekeeping.requests.CorrectDisputeStateRequest;
import com.phonepe.merchant.platform.stratos.server.core.resources.housekeeping.requests.CorrectRowStateRequest;
import com.phonepe.merchant.platform.stratos.server.core.resources.housekeeping.requests.DisputeWorkflowUpdateRequest;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.models.common.enums.ResponseCode;
import com.phonepe.models.response.GenericResponse;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Stream;

import static com.phonepe.models.common.enums.ResponseCode.SUCCESS;

@Slf4j
class HouseKeepingIntegrationTest extends LoadOnlyOnClassLevelBaseTest {

    private HousekeepingResource housekeepingResource;
    private DisputeWorkflowRepository disputeWorkflowRepository;
    private RowRepository rowRepository;
    private FileRepository fileRepository;
    private DisputeRepository disputeRepository;

    @BeforeEach
    void setUpGuiceInjection() {
        housekeepingResource = guiceInjector.getInstance(HousekeepingResource.class);
        disputeWorkflowRepository = guiceInjector.getInstance(DisputeWorkflowRepository.class);
        rowRepository = guiceInjector.getInstance(RowRepository.class);
        fileRepository = guiceInjector.getInstance(FileRepository.class);
        disputeRepository = guiceInjector.getInstance(DisputeRepository.class);
    }

    @Test
    void testUnblockRefundForTxnsApi() {
        final var list = List.of("TXN123456", "TXN12345678");

        list.forEach(txnId ->
            WireMock.stubFor(
                WireMock.post(WireMock.urlPathMatching(
                        String.format("/v1/chargeback/update/reversal/flag/%s/unblock", txnId)))
                    .willReturn(WireMock.aResponse()
                        .withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON)
                        .withStatus(200)
                        .withBody(MapperUtils.serializeToBytes(
                            GenericResponse.<ResponseCode>builder()
                                .success(true)
                                .data(SUCCESS)
                                .build()
                        ))))
        );

        try {
            housekeepingResource.unblockReversalsForTransactionIds(list);
        } catch (final Exception e) {
            Assertions.fail();
        }
    }


    @Test
    void testUpdateDisputeStateWorkflowApi() {

        final var currentDateTime = LocalDateTime.now();
        // Merchant not responded scenario for UPI first level chargeback
        DisputeWorkflow disputeWorkflow = TestDataUtils.getDisputeWorkflow(
            DisputeType.UPI_CHARGEBACK, DisputeStage.FIRST_LEVEL, IdGenerator.generate("D").getId(),
            IdGenerator.generate("T").getId(),
            DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK,
            DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK,
            currentDateTime, currentDateTime);
        disputeWorkflowRepository.save(disputeWorkflow);

        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        housekeepingResource.correctDisputeState(serviceUserPrincipal,
            CorrectDisputeStateRequest.builder()
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .transactionId(disputeWorkflow.getTransactionReferenceId())
            .toState(DisputeWorkflowState.NPCI_REPRESENTMENT_COMPLETED)
            .toEvent(DisputeWorkflowEvent.COMPLETE_NPCI_REPRESENTMENT)
            .build());

        housekeepingResource.correctDisputeState(serviceUserPrincipal,
                CorrectDisputeStateRequest.builder()
                        .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
                        .transactionId(disputeWorkflow.getTransactionReferenceId())
                        .toState(DisputeWorkflowState.NPCI_REPRESENTMENT_COMPLETED)
                        .toEvent(DisputeWorkflowEvent.COMPLETE_NPCI_REPRESENTMENT)
                        .build());

        disputeWorkflow = disputeWorkflowRepository.select(
            disputeWorkflow.getTransactionReferenceId(),
            disputeWorkflow.getDisputeWorkflowId()).get();

        Assertions.assertEquals(DisputeWorkflowState.NPCI_REPRESENTMENT_COMPLETED,
            disputeWorkflow.getCurrentState());
        Assertions.assertEquals(DisputeWorkflowEvent.COMPLETE_NPCI_REPRESENTMENT,
            disputeWorkflow.getCurrentEvent());
        Assertions.assertEquals("UID", disputeWorkflow.getGandalfUserId());
    }


    @Test
    void testUpdateRowStateApi() {
        final Row row = rowRepository.saveRow(Row.builder()
            .rowType(RowType.PG_MIS_ROW)
            .rowState(RowState.FAILED)
            .key(PrimaryKey.builder()
                .partitionId(202101)
                .build())
            .rowId(IdGenerator.generate("Row").getId())
            .sourceId("SourceId")
            .sourceType(SourceType.FILE)
            .build());

        Assertions.assertEquals(RowState.FAILED, row.getRowState());

        housekeepingResource.correctRowState(CorrectRowStateRequest.builder()
            .sourceId(row.getSourceId())
            .rowId(row.getRowId())
            .toRowState(RowState.PROCESSED)
            .build());
        final var updatedRow = rowRepository.select(row.getSourceId(),
            DetachedCriteria.forClass(Row.class)
                .add(Restrictions.eq(Fields.rowId, row.getRowId()))).stream().findFirst().get();

        Assertions.assertEquals(RowState.PROCESSED, updatedRow.getRowState());
    }

    @Test
    void testMarkFileAsProcessed() {
        var fileId = IdGenerator.generate("File").getId();
        final var file = TestDataUtils.getFile(fileId, FileState.PROCESSING);

        fileRepository.saveFile(fileId, file);
        Assertions.assertEquals(FileState.PROCESSING,
            fileRepository.getFile(fileId).getFileState());

        housekeepingResource.markFileAsProcessed(fileId);

        Assertions.assertEquals(FileState.PROCESSED, fileRepository.getFile(fileId).getFileState());
    }

    @Test
    void testBlockReversalForTxns(){

        WireMock.stubFor(
        WireMock.post(WireMock.urlPathMatching(
                        String.format("/v1/chargeback/update/reversal/flag/%s/block", "TXN1234")))
                .willReturn(WireMock.aResponse()
                        .withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON)
                        .withStatus(200)
                        .withBody(MapperUtils.serializeToBytes(
                                GenericResponse.<ResponseCode>builder()
                                        .success(true)
                                        .data(SUCCESS)
                                        .build()
                        ))));

        Stream.of(DisputeType.PG_CHARGEBACK, DisputeType.NB_CHARGEBACK, DisputeType.UPI_CHARGEBACK)
                .forEach(disputeType -> {
                    Response res = housekeepingResource.blockReversals(List.of("TXN1234"),disputeType, DisputeStage.FIRST_LEVEL);
                    Assertions.assertEquals(200,res.getStatus());
                });

        Stream.of(DisputeType.UDIR_INCOMING_COMPLAINT,DisputeType.UDIR_OUTGOING_COMPLAINT, DisputeType.P2PM_TOA)
                .forEach(disputeType -> {
                    Response res = housekeepingResource.blockReversals(List.of("TXN1234"),disputeType, DisputeStage.FIRST_LEVEL);
                    Assertions.assertEquals(400, res.getStatus());
                });

        var dispute = TestDataUtils.getDispute(DisputeType.EDC_CHARGEBACK,DisputeStage.FIRST_LEVEL, DisputeCategory.SERVICE_CHARGEBACK);
        disputeRepository.save(dispute);
        var disputeWorkflow = TestDataUtils.getDisputeWorkflow(dispute);
        disputeWorkflowRepository.save(disputeWorkflow);
        MockingUtils.setEdcBlockReversals(dispute.getMerchantTransactionId());

        Response edcResponse = housekeepingResource.blockReversals(List.of(dispute.getTransactionReferenceId()), DisputeType.EDC_CHARGEBACK, DisputeStage.FIRST_LEVEL);
        Assertions.assertEquals(200, edcResponse.getStatus());

    }

    @Test
    void testUpdateDisputeWF() {
        final var currentDateTime = LocalDateTime.now();
        final var disputeId1 = IdGenerator.generate("D").getId();
        final var transactionId1 = IdGenerator.generate("T").getId();

        DisputeWorkflow disputeWorkflow1 = TestDataUtils.getDisputeWorkflow(
            DisputeType.UPI_CHARGEBACK,
            DisputeStage.FIRST_LEVEL,
            disputeId1,
            transactionId1,
            DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK,
            DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK,
            currentDateTime, currentDateTime);
        disputeWorkflowRepository.save(disputeWorkflow1);
        LocalDate date1 = LocalDate.of(2024, 1, 1);

        try {
            housekeepingResource.updateDisputeRaisedAt(
                new DisputeWorkflowUpdateRequest(
                    transactionId1,
                    date1,
                    DisputeTypeDto.UPI_CHARGEBACK,
                    FileTypeDto.YES));
        } catch (final Exception e) {
            Assertions.fail();
        }
        DisputeWorkflow dw1 = disputeWorkflowRepository.select(transactionId1).get(0);
        Assertions.assertTrue(dw1.getRaisedAt().toLocalDate().equals(date1));
        Assertions.assertTrue(dw1.getRespondBy().toLocalDate().minusDays(15).equals(date1));

        //Failure case
        try {
            housekeepingResource.updateDisputeRaisedAt(
                    new DisputeWorkflowUpdateRequest(
                            IdGenerator.generate("T").getId(),
                            date1,
                            DisputeTypeDto.UPI_CHARGEBACK,
                            FileTypeDto.YES));
        } catch (final Exception e) {
            if(e instanceof DisputeException) {
                DisputeException thrown = (DisputeException) e;
                Assertions.assertEquals(StratosErrorCodeKey.INVALID_TRANSACTION, thrown.getErrorCode());
            }
            else
                Assertions.fail();
        }

    }
}
