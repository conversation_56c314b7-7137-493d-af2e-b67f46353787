package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.toa.p2pm;


import com.phonepe.merchant.platform.stratos.server.BaseTest;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.RowState;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.NeuronTestUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.models.payments.merchant.MerchantTransactionState;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import java.util.List;
import java.util.concurrent.TimeUnit;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class P2pmToaDuplicateToaFailedTest extends BaseTest {

    private DisputeService disputeService;

    @BeforeEach
    void setUpGuiceInjection() {
        disputeService = guiceInjector.getInstance(DisputeService.class);
        MockingUtils.setupEventIngestionApiMocking();
    }

    /*
    Test: Duplicate Toa Validation
    WORKFLOW: PULSE ->
                    DUPLICATE_TOA stored in ROW table
    */
    @Test
    void test() {

        final var transactionReferenceId = IdGenerator.generate("T").getId();

        MockingUtils.enableKillSwitchMocking(DisputeType.P2PM_TOA);  // enable kill switch
        MockingUtils.setDeemFailureUpiTransactionDetail(transactionReferenceId, 120); // SUCCESS txn details api

        // Create a pulse
        NeuronTestUtils.createP2pmToaPulse(transactionReferenceId);

        // wait till the entry for workflow is created
        DisputeWorkflow disputeWorkflow = AssertionUtils
            .assertAndGetDisputeWorkflowCreation(transactionReferenceId, DisputeType.P2PM_TOA,
                DisputeStage.FIRST_LEVEL);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.TOA_BLOCKED_DUE_TO_KS,
            10L, TimeUnit.SECONDS);

        // Trigger another event with same txn value
        String eventId = NeuronTestUtils.createP2pmToaPulse(transactionReferenceId);

        // Assert this event as recorded as failed
        AssertionUtils.assertRowStateEquals(RowState.FAILED, eventId, 20, TimeUnit.SECONDS);

        // check for dispute to get only one workflow
        List<DisputeWorkflow> allDisputeWorkflows = disputeService.getAllDisputeWorkflows(
            transactionReferenceId, DisputeType.P2PM_TOA, DisputeStage.FIRST_LEVEL);

        Assertions.assertNotNull(allDisputeWorkflows);
        Assertions.assertEquals(1, allDisputeWorkflows.size());

    }


    @Test
    void testChargeBackAcceptanceCheckForNonFraudImpl() {

        final var transactionReferenceId = IdGenerator.generate("T").getId();

        MockingUtils.setDeemFailureUpiTransactionDetail(transactionReferenceId, 120);  // SUCCESS txn details api
        MockingUtils.setUpPaymentPayApi(true, 200); // SUCCESS pay api

        String merchantOrderId = "DEEM0-" + transactionReferenceId;

        // mock transaction details status api
        MockingUtils.setMerchantTransactionsStatus("IRCTCINAPP", merchantOrderId,
            MerchantTransactionState.COMPLETED, 120);

        // Create a pulse
        NeuronTestUtils.createP2pmToaPulse(transactionReferenceId);

        // wait till the entry for workflow is created
        DisputeWorkflow disputeWorkflow = AssertionUtils
            .assertAndGetDisputeWorkflowCreation(transactionReferenceId, DisputeType.P2PM_TOA,
                DisputeStage.FIRST_LEVEL);


        MockingUtils.setupGetMerchantProfileApiMocking(disputeWorkflow.getDispute().getMerchantId(),
            "P2P_MERCHANT");

        UnsupportedOperationException exception = Assertions.assertThrows(
            UnsupportedOperationException.class, () -> {
                disputeService.buildChargebackPayload(disputeWorkflow);
            });

        Assertions.assertEquals(
            "Invalid build Chargeback kratos Payload for dispute type P2PM_TOA",
            exception.getMessage());
    }
}
