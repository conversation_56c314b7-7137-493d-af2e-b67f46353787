package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.toa.notional_wallet;

import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.RowState;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.NeuronTestUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.models.payments.merchant.MerchantTransactionState;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.concurrent.TimeUnit;

@Slf4j
public class ToaFailedAndRetryFlowTest extends NotionalToaBaseTestCases {


    @Test
    void testToaPayInitiationFailedThenretrySuccess(){
        testTillToaInitiationFailed();
        MockingUtils.setUpPaymentPayApiV2(true, 200);
        String merchantOrderId = "TOA-0-"+ disputeWorkflow.getTransactionReferenceId();
        MockingUtils.setMerchantTransactionsStatus(PHONEPETOA, merchantOrderId, MerchantTransactionState.COMPLETED, disputeWorkflow.getDisputedAmount());
        toaResource.retryToa(serviceUserPrincipal, getDisputeWorkflowKey(), DisputeType.NOTIONAL_CREDIT_TOA).getData();
        AssertionUtils.assertDisputeWorkflowStateEquals(disputeWorkflow.getTransactionReferenceId(),
                disputeWorkflow.getDisputeWorkflowId(), DisputeWorkflowState.TOA_COMPLETED, 20L, TimeUnit.SECONDS);
    }

    @Test
    void testToaInitFailedThenMarkedCompletedExternally() {
        testTillToaInitiationFailed();

        var response = toaResource.processToaExternally(serviceUserPrincipal, disputeWorkflow.getTransactionReferenceId(),
                disputeWorkflow.getDisputeWorkflowId(), TestDataUtils.getCommentContext(), DisputeType.NOTIONAL_CREDIT_TOA);


        Assertions.assertTrue(response.isSuccess());
        AssertionUtils.assertToaSummary(response.getData(), disputeWorkflow, DisputeWorkflowState.TOA_COMPLETED_EXTERNALLY);

        AssertionUtils.assertDisputeWorkflowStateEquals(
                disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
                DisputeWorkflowState.TOA_COMPLETED_EXTERNALLY,
                0L, TimeUnit.SECONDS);
    }

    @Test
    void testToaFailedThenMarkedExternallyProcessed(){
        testTillToaFailedAfterMaxRetry();

        var response = toaResource.processToaExternally(serviceUserPrincipal, disputeWorkflow.getTransactionReferenceId(),
                disputeWorkflow.getDisputeWorkflowId(), TestDataUtils.getCommentContext(), DisputeType.NOTIONAL_CREDIT_TOA);

        Assertions.assertTrue(response.isSuccess());
        AssertionUtils.assertToaSummary(response.getData(), disputeWorkflow, DisputeWorkflowState.TOA_COMPLETED_EXTERNALLY);

        AssertionUtils.assertDisputeWorkflowStateEquals(
                disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
                DisputeWorkflowState.TOA_COMPLETED_EXTERNALLY,
                0L, TimeUnit.SECONDS);
    }

    @Test
    void duplicateToaTest() {
        testTillToaOpened();
        NeuronTestUtils.createNotionalToaPulse(pulse);
        AssertionUtils.assertRowStateEquals(RowState.FAILED, pulse.getSignals().get(1).getValue().toString(), 20, TimeUnit.SECONDS);
    }
}
