package com.phonepe.merchant.platform.stratos.server.integrationTests.file.pg;

import com.phonepe.merchant.platform.stratos.models.disputes.DisputeTypeDto;
import com.phonepe.merchant.platform.stratos.models.files.FileTypeDto;
import com.phonepe.merchant.platform.stratos.server.BaseTest;
import com.phonepe.merchant.platform.stratos.server.LoadOnlyOnClassLevelBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.RowState;
import com.phonepe.merchant.platform.stratos.server.core.resources.FileResource;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.olympus.im.models.security.ServiceUserPrincipal;
import com.phonepe.olympus.im.models.user.HumanUserDetails;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

public class PgFileUploadIntegrationTest extends LoadOnlyOnClassLevelBaseTest {

    private FileResource fileResource;
    private DisputeService disputeService;

    @BeforeEach
    void setUpGuiceInjection() {
        fileResource = guiceInjector.getInstance(FileResource.class);
        disputeService = guiceInjector.getInstance(DisputeService.class);
        MARIA_DB_CONTAINER.executeQuery("TRUNCATE stratos_shard_1.file;\n"
            + "TRUNCATE stratos_shard_2.file;\n"
            + "TRUNCATE stratos_shard_1.file_audit;\n"
            + "TRUNCATE stratos_shard_2.file_audit;\n"
            + "TRUNCATE stratos_shard_1.row;\n"
            + "TRUNCATE stratos_shard_2.row;\n"
            + "TRUNCATE stratos_shard_1.row_audit;\n"
            + "TRUNCATE stratos_shard_2.row_audit;", true);
    }

    static Stream<Arguments> provideArgumentParameters() {
        return Stream.of(
            Arguments.of(
                "HDFC First Level CB FIle Upload Full Refund",
                "HDFC Bank"),
            Arguments.of(
                "Razorpay First Level CB FIle Upload Full Refund",
                "Razor Pay"),
            Arguments.of(
                "Payu First Level CB FIle Upload Full Refund",
                "Pay U"
            ),
            Arguments.of(
                "BILLDESK First Level CB FIle Upload Full Refund",
                "Axis Bank"
            ),
            Arguments.of(
                "AxisBank First Level CB FIle Upload Full Refund",
                "Bill Desk"
            ),
            Arguments.of(
                "FirstData First Level CB FIle Upload Full Refund",
                "First Data"
            ),
            Arguments.of(
                "TPSL First Level CB FIle Upload Full Refund",
                "TPSL"
            )
        );
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource("provideArgumentParameters")
    public void test(String name, String pgName)
        throws Exception {
        final var transactionReferenceId = IdGenerator.generate("T").getId();

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForCreateChargebackApiMocking(pgName);
        MockingUtils.setupPgTransportTransactionDetailMocking("PG1234",
            transactionReferenceId);
        MockingUtils.setupPaymentsApiFullRefundExistsMockingPaidFromDebitCard(
            transactionReferenceId, 100, "PG1234");
        MockingUtils.setupEventIngestionApiMocking();

        final var userDetailsOlympus = HumanUserDetails.builder()
                .userId("test-user")
                .build();
        final var userAuthDetails = UserAuthDetails.builder()
                .userDetails(userDetailsOlympus)
                .build();
        final var serviceUserPrincipal = ServiceUserPrincipal.builder()
                .userAuthDetails(userAuthDetails)
                .build();

        var response = fileResource.upload(DisputeTypeDto.PG_CHARGEBACK,
            FileTypeDto.PG_FIRST_LEVEL, TestDataUtils.pgFirstLevelChargebackCSVFileStreamFromPgName(pgName),
            FormDataContentDisposition.name("randomName").fileName("file.csv")
                .build(),"AuthToken", serviceUserPrincipal);

        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);

        final var disputeWorkflow = disputeService.
            getDisputeWorkflow(transactionReferenceId, DisputeType.PG_CHARGEBACK,
                DisputeStage.FIRST_LEVEL);

        Assertions.assertTrue(disputeWorkflow instanceof FinancialDisputeWorkflow);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.REPRESENTMENT_REQUIRED,
            5L, TimeUnit.SECONDS);

        disputeService.triggerEvent(
                userAuthDetails,
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowEvent.COMPLETE_PG_REPRESENTMENT,
            Constants.EMPTY_TRANSITION_CONTEXT);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.PG_REPRESENTMENT_COMPLETED,
            5L, TimeUnit.SECONDS);

        var dispute = disputeService.getDispute(transactionReferenceId, DisputeType.PG_CHARGEBACK,
            DisputeStage.FIRST_LEVEL);

        Assertions.assertNotNull(dispute.getRrn());
        Assertions.assertNotNull(dispute.getDisputeIssuer());
        Assertions.assertNotNull(dispute.getDisputeCategory());

        //PreArb File Upload
        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForCreateChargebackPreArbApiMocking(pgName);

        var preArbResponse = fileResource.upload(DisputeTypeDto.PG_CHARGEBACK,
            FileTypeDto.PG_PRE_ARBITRATION,
            TestDataUtils.pgPreArbLevelChargebackCSVFileStream(pgName),
            FormDataContentDisposition.name("randomName1").fileName("file2.csv")
                .build(),"AuthToken", serviceUserPrincipal);

        AssertionUtils.assertFileProcessed(preArbResponse.getId(), 20, TimeUnit.SECONDS);

        final var preArbDisputeWorkflow = disputeService.
            getDisputeWorkflow(transactionReferenceId, DisputeType.PG_CHARGEBACK,
                DisputeStage.PRE_ARBITRATION);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, preArbDisputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.REPRESENTMENT_REQUIRED,
            5L, TimeUnit.SECONDS);

        disputeService.triggerEvent(
                userAuthDetails,
            transactionReferenceId,
            preArbDisputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowEvent.COMPLETE_PG_REPRESENTMENT,
            Constants.EMPTY_TRANSITION_CONTEXT);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, preArbDisputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.PG_REPRESENTMENT_COMPLETED,
            5L, TimeUnit.SECONDS);
    }

    @Test
    public void testDuplicateChargebackFileUpload() throws Exception {
        final var transactionReferenceId = IdGenerator.generate("T").getId();

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForCreateChargebackApiMocking("HDFC Bank");
        MockingUtils.setupPgTransportTransactionDetailMocking("PG1234",
            transactionReferenceId);
        MockingUtils.setupPaymentsApiFullRefundExistsMockingPaidFromDebitCard(
            transactionReferenceId, 100, "PG1234");
        MockingUtils.setupEventIngestionApiMocking();

        final var userDetailsOlympus = HumanUserDetails.builder()
                .userId("test-user")
                .build();
        final var userAuthDetails = UserAuthDetails.builder()
                .userDetails(userDetailsOlympus)
                .build();
        final var serviceUserPrincipal = ServiceUserPrincipal.builder()
                .userAuthDetails(userAuthDetails)
                .build();

        var response = fileResource.upload(DisputeTypeDto.PG_CHARGEBACK,
            FileTypeDto.PG_FIRST_LEVEL, TestDataUtils.pgFirstLevelChargebackCSVFileStreamFromPgName("HDFC Bank"),
            FormDataContentDisposition.name("randomName").fileName("file.csv")
                .build(),"AuthToken", serviceUserPrincipal);

        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForCreateChargebackApiMocking("HDFC Bank");

        var secondFileResponse = fileResource.upload(DisputeTypeDto.PG_CHARGEBACK,
            FileTypeDto.PG_FIRST_LEVEL, TestDataUtils.pgFirstLevelChargebackCSVFileStreamFromPgName("HDFC Bank"),
            FormDataContentDisposition.name("randomName").fileName("file2.csv")
                .build(),"AuthToken", serviceUserPrincipal);

        AssertionUtils.assertRowStateEquals(RowState.FAILED, secondFileResponse.getId(), 15,
            TimeUnit.SECONDS);
    }
}