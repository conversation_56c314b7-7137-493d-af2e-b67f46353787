package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.udir;

import com.phonepe.merchant.platform.stratos.models.disputes.DisputeStageDto;
import com.phonepe.merchant.platform.stratos.models.disputes.UdirComplaintStateDto;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.models.udir.requests.UdirDisputeTypeDto;
import com.phonepe.merchant.platform.stratos.models.udir.requests.UdirOutgoingComplaintStatusRequest;
import com.phonepe.merchant.platform.stratos.models.udir.requests.UdirOutgoingRaiseComplaintRequest;
import com.phonepe.merchant.platform.stratos.models.udir.response.UdirOutgoingComplaintStatusResponse;
import com.phonepe.merchant.platform.stratos.models.udir.response.UdirOutgoingRaiseComplaintResponse;
import com.phonepe.merchant.platform.stratos.server.BaseTest;
import com.phonepe.merchant.platform.stratos.server.LoadOnlyOnClassLevelBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.NonFinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeCategory;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.resources.DisputeResource;
import com.phonepe.merchant.platform.stratos.server.core.resources.UdirResource;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.models.payments.pay.destination.DestinationType;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.payments.upiclientmodel.complaint.UPIClientOutgoingComplaintResponse;
import com.phonepe.payments.upiclientmodel.enums.ComplaintRequestType;
import com.phonepe.payments.upiclientmodel.enums.ComplaintState;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

public class UdirComplaintWorkflowTest extends LoadOnlyOnClassLevelBaseTest {

    private UdirResource udirResource;
    private DisputeService disputeService;
    private DisputeResource disputeResource;
    private OlympusIMClient olympusIMClient;

    @BeforeEach
    void setUpGuiceInjection() {
        udirResource = guiceInjector.getInstance(UdirResource.class);
        disputeService = guiceInjector.getInstance(DisputeService.class);
        disputeResource = guiceInjector.getInstance(DisputeResource.class);
        olympusIMClient = guiceInjector.getInstance(OlympusIMClient.class);
        truncateDb();
    }

    @Test
    void testServiceComplaintSuccessWorkflow() {
        var transactionId = IdGenerator.generate("T").getId();
        var complaintId = IdGenerator.generate("CS").getId();
        MockingUtils.setupP2MExternalTransactionDetail(transactionId, 100,
            DestinationType.VPA.name(), "1234");
        MockingUtils.setupPaymentsRaiseComplaintMocking();
        Mockito.doReturn(true).when(olympusIMClient).verifyPermission(Mockito.any(), Mockito.any());

        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        var raiseComplaintResponse = udirResource.raiseComplaint(
            UdirOutgoingRaiseComplaintRequest.builder()
                .paymentTransactionId(transactionId)
                .requestCode(ComplaintRequestType.COMPLAINT_REQ_FULFILLMENT_ISSUE_PAID_AGAIN)
                .adjAmount(100)
                .disputeStage(DisputeStageDto.FIRST_LEVEL)
                .complaintId(complaintId)
                .build(),serviceUserPrincipal);

        final var disputeWorkflow = disputeService.
            getDisputeWorkflow(transactionId, DisputeType.UDIR_OUTGOING_COMPLAINT,
                DisputeStage.FIRST_LEVEL);

        Assertions.assertTrue(disputeWorkflow instanceof NonFinancialDisputeWorkflow);

        Assertions.assertEquals(
            UdirDisputeTypeDto.UDIR_OUTGOING_COMPLAINT, raiseComplaintResponse.getDisputeType());

        Assertions.assertEquals(transactionId, raiseComplaintResponse.getTransactionId());

        Assertions.assertEquals(complaintId, disputeWorkflow.getDispute().getDisputeReferenceId());

        Assertions.assertEquals(disputeWorkflow.getDisputeWorkflowId(),
            ((UdirOutgoingRaiseComplaintResponse) raiseComplaintResponse).getStratosComplaintId());

        Assertions.assertEquals(complaintId,
            ((UdirOutgoingRaiseComplaintResponse) raiseComplaintResponse).getComplaintId());

        var response1 = (UdirOutgoingComplaintStatusResponse) disputeResource.status(
            UdirOutgoingComplaintStatusRequest
                .builder()
                .complaintId(complaintId)
                .transactionId(transactionId)
                .build(),
                serviceUserPrincipal);

        Assertions.assertEquals(UdirComplaintStateDto.ACCEPTED, response1.getComplaintState());
        Assertions.assertEquals(transactionId, response1.getTransactionId());
        Assertions.assertEquals(complaintId, response1.getComplaintId());
        Assertions.assertEquals(disputeWorkflow.getDisputeWorkflowId(),
            response1.getStratosComplaintId());

        AssertionUtils.assertDisputeWorkflowStateEquals(transactionId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.RECEIVED);

        Assertions.assertEquals(DisputeCategory.UDIR_SERVICE,
            disputeWorkflow.getDispute().getDisputeCategory());

        udirResource.complaintCallback(UPIClientOutgoingComplaintResponse.builder()
            .complaintId(disputeWorkflow.getDisputeWorkflowId())
            .state(ComplaintState.INITIATED)
            .crn("1234")
            .build());

        final var dispute = disputeService.
            getDisputeWorkflow(transactionId, DisputeType.UDIR_OUTGOING_COMPLAINT,
                DisputeStage.FIRST_LEVEL).getDispute();

        Assertions.assertEquals(complaintId, dispute.getDisputeReferenceId());

        var response2 = (UdirOutgoingComplaintStatusResponse) disputeResource.status(
            UdirOutgoingComplaintStatusRequest
                .builder()
                .complaintId(complaintId)
                .transactionId(transactionId)
                .build(),
                serviceUserPrincipal);

        Assertions.assertEquals(UdirComplaintStateDto.PENDING, response2.getComplaintState());
        Assertions.assertEquals("1234", response2.getCrn());

        AssertionUtils.assertDisputeWorkflowStateEquals(transactionId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.UDIR_COMPLAINT_ACCEPTED);

        udirResource.complaintCallback(UPIClientOutgoingComplaintResponse.builder()
            .complaintId(disputeWorkflow.getDisputeWorkflowId())
            .state(ComplaintState.COMPLETED)
            .adjCode("Adjcode")
            .adjFlag("Ajdflag")
            .adjReason("AdjReason")
            .adjRemark("AdjRemark")
            .crn("1234")
            .build());

        AssertionUtils.assertDisputeWorkflowStateEquals(transactionId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.UDIR_RESPONSE_RECEIVED);

        var response3 = (UdirOutgoingComplaintStatusResponse) disputeResource.status(
            UdirOutgoingComplaintStatusRequest
                .builder()
                .complaintId(complaintId)
                .transactionId(transactionId)
                .build(),
                serviceUserPrincipal);

        Assertions.assertEquals(UdirComplaintStateDto.COMPLETED, response3.getComplaintState());
        Assertions.assertEquals("Adjcode", response3.getAdjCode());
        Assertions.assertEquals("Ajdflag", response3.getAdjFlag());
        Assertions.assertEquals("AdjReason", response3.getAdjReason());
        Assertions.assertEquals("AdjRemark", response3.getAdjRemark());
        Assertions.assertEquals("1234", response3.getCrn());
    }

    @Test
    void testInvalidReceivedMcc() {
        var transactionId = IdGenerator.generate("T").getId();
        var complaintId = IdGenerator.generate("CS").getId();

        MockingUtils.setupP2MExternalTransactionDetail(transactionId, 100,
            DestinationType.VPA.name(), "0000");
        MockingUtils.setupPaymentsRaiseComplaintTimeoutMocking();
        Mockito.doReturn(true).when(olympusIMClient).verifyPermission(Mockito.any(), Mockito.any());

        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        final var stratosError = Assertions.assertThrows(DisputeException.class, () ->
            udirResource.raiseComplaint(UdirOutgoingRaiseComplaintRequest.builder()
                .paymentTransactionId(transactionId)
                .requestCode(ComplaintRequestType.COMPLAINT_REQ_FULFILLMENT_ISSUE_PAID_AGAIN)
                .adjAmount(100)
                .disputeStage(DisputeStageDto.FIRST_LEVEL)
                .complaintId(complaintId)
                .build(), serviceUserPrincipal));

        Assertions.assertEquals(StratosErrorCodeKey.INVALID_UDIR_COMPLAINT,
            stratosError.getErrorCode());
    }

    @Test
    void testComplaintRetry() {
        var transactionId = IdGenerator.generate("T").getId();
        var complaintId = IdGenerator.generate("CS").getId();
        MockingUtils.setupP2MExternalTransactionDetail(transactionId, 100,
            DestinationType.VPA.name(), "1234");
        MockingUtils.setupPaymentsRaiseComplaintMocking();
        Mockito.doReturn(true).when(olympusIMClient).verifyPermission(Mockito.any(), Mockito.any());

        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        var raiseComplaintResponse = udirResource.raiseComplaint(
            UdirOutgoingRaiseComplaintRequest.builder()
                .paymentTransactionId(transactionId)
                .requestCode(ComplaintRequestType.COMPLAINT_REQ_FULFILLMENT_ISSUE_PAID_AGAIN)
                .adjAmount(100)
                .disputeStage(DisputeStageDto.FIRST_LEVEL)
                .complaintId(complaintId)
                .build(), serviceUserPrincipal);

        final var disputeWorkflow = disputeService.
            getDisputeWorkflow(transactionId, DisputeType.UDIR_OUTGOING_COMPLAINT,
                DisputeStage.FIRST_LEVEL);

        AssertionUtils.assertDisputeWorkflowStateEquals(transactionId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.RECEIVED);

        //Exception when complaint is in received and it is retried
        var stratosError = Assertions.assertThrows(DisputeException.class,
            () -> udirResource.raiseComplaint(UdirOutgoingRaiseComplaintRequest.builder()
                .paymentTransactionId(transactionId)
                .requestCode(ComplaintRequestType.COMPLAINT_REQ_FULFILLMENT_ISSUE_PAID_AGAIN)
                .adjAmount(100)
                .disputeStage(DisputeStageDto.FIRST_LEVEL)
                .complaintId(IdGenerator.generate("CS").getId())
                .build(), serviceUserPrincipal));

        Assertions.assertEquals(StratosErrorCodeKey.PREVIOUS_DISPUTE_WORKFLOW_NOT_ENDED,
            stratosError.getErrorCode());

        var statusResponse1 = (UdirOutgoingComplaintStatusResponse) disputeResource.status(
            UdirOutgoingComplaintStatusRequest
                .builder()
                .complaintId(complaintId)
                .transactionId(transactionId)
                .build(), serviceUserPrincipal);

        Assertions.assertEquals(UdirComplaintStateDto.ACCEPTED,
            statusResponse1.getComplaintState());
        Assertions.assertEquals(complaintId, statusResponse1.getComplaintId());

        //Failed Callback Received
        udirResource.complaintCallback(UPIClientOutgoingComplaintResponse.builder()
            .complaintId(disputeWorkflow.getDisputeWorkflowId())
            .state(ComplaintState.FAILED)
            .errorCode("ABCD")
            .build());

        AssertionUtils.assertDisputeWorkflowStateEquals(transactionId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.UDIR_COMPLAINT_REJECTED);

        var statusResponse2 = (UdirOutgoingComplaintStatusResponse) disputeResource.status(
            UdirOutgoingComplaintStatusRequest
                .builder()
                .complaintId(complaintId)
                .transactionId(transactionId)
                .build(), serviceUserPrincipal);

        Assertions.assertEquals(UdirComplaintStateDto.FAILURE, statusResponse2.getComplaintState());
        Assertions.assertEquals("ABCD", statusResponse2.getErrorCode());
        Assertions.assertEquals(complaintId, statusResponse2.getComplaintId());

        //Complaint is retried
        var complaintId2 = IdGenerator.generate("CS").getId();
        var raiseComplaintResponse2 = udirResource.raiseComplaint(
            UdirOutgoingRaiseComplaintRequest.builder()
                .paymentTransactionId(transactionId)
                .requestCode(ComplaintRequestType.COMPLAINT_REQ_FULFILLMENT_ISSUE_PAID_AGAIN)
                .adjAmount(100)
                .disputeStage(DisputeStageDto.FIRST_LEVEL)
                .complaintId(complaintId2)
                .build(), serviceUserPrincipal);

        var disputeWorkflowId1 = ((UdirOutgoingRaiseComplaintResponse) raiseComplaintResponse).getStratosComplaintId();
        var disputeWorkflowId2 = ((UdirOutgoingRaiseComplaintResponse) raiseComplaintResponse2).getStratosComplaintId();

        Assertions.assertNotEquals(disputeWorkflowId2, disputeWorkflowId1);

        AssertionUtils.assertDisputeWorkflowStateEquals(transactionId,
            disputeWorkflowId2,
            DisputeWorkflowState.RECEIVED);

        AssertionUtils.assertDisputeWorkflowStateEquals(transactionId,
            disputeWorkflowId1,
            DisputeWorkflowState.UDIR_COMPLAINT_REJECTED);

        var disputeWorkflowList = disputeService.getAllDisputeWorkflows(transactionId,
            DisputeType.UDIR_OUTGOING_COMPLAINT, DisputeStage.FIRST_LEVEL);

        Assertions.assertEquals(2, disputeWorkflowList.size());

        var statusResponse3 = (UdirOutgoingComplaintStatusResponse) disputeResource.status(
            UdirOutgoingComplaintStatusRequest
                .builder()
                .complaintId(complaintId)
                .transactionId(transactionId)
                .complaintId(complaintId2)
                .build(), serviceUserPrincipal);

        Assertions.assertEquals(UdirComplaintStateDto.ACCEPTED,
            statusResponse3.getComplaintState());

        udirResource.complaintCallback(UPIClientOutgoingComplaintResponse.builder()
            .complaintId(disputeWorkflowId2)
            .state(ComplaintState.INITIATED)
            .crn("1234")
            .build());

        udirResource.complaintCallback(UPIClientOutgoingComplaintResponse.builder()
            .complaintId(disputeWorkflowId2)
            .state(ComplaintState.COMPLETED)
            .adjCode("Adjcode")
            .adjFlag("Ajdflag")
            .adjReason("AdjReason")
            .adjRemark("AdjRemark")
            .crn("1234")
            .build());

        AssertionUtils.assertDisputeWorkflowStateEquals(transactionId,
            disputeWorkflowId2,
            DisputeWorkflowState.UDIR_RESPONSE_RECEIVED);

        AssertionUtils.assertDisputeWorkflowStateEquals(transactionId,
            disputeWorkflowId1,
            DisputeWorkflowState.UDIR_COMPLAINT_REJECTED);

        var statusResponse4 = (UdirOutgoingComplaintStatusResponse) disputeResource.status(
            UdirOutgoingComplaintStatusRequest
                .builder()
                .complaintId(complaintId)
                .transactionId(transactionId)
                .complaintId(complaintId2)
                .build(), serviceUserPrincipal);

        Assertions.assertEquals(UdirComplaintStateDto.COMPLETED,
            statusResponse4.getComplaintState());

        var statusResponse5 = (UdirOutgoingComplaintStatusResponse) disputeResource.status(
            UdirOutgoingComplaintStatusRequest
                .builder()
                .complaintId(complaintId)
                .transactionId(transactionId)
                .complaintId(complaintId)
                .build(), serviceUserPrincipal);

        Assertions.assertEquals(UdirComplaintStateDto.FAILURE, statusResponse5.getComplaintState());
    }
}
