package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.udir;

import com.phonepe.merchant.platform.stratos.models.disputes.DisputeStageDto;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.models.udir.requests.UdirOutgoingRaiseComplaintRequest;
import com.phonepe.merchant.platform.stratos.server.BaseTest;
import com.phonepe.merchant.platform.stratos.server.LoadOnlyOnClassLevelBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.resources.UdirResource;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.payments.upiclientmodel.enums.ComplaintRequestType;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

public class UdirDeemedRaiseComplaintInvalidDeemedWorkflowTest extends LoadOnlyOnClassLevelBaseTest {

    private UdirResource udirResource;

    private OlympusIMClient olympusIMClient;

    @BeforeEach
    void setUpGuiceInjection() {
        udirResource = guiceInjector.getInstance(UdirResource.class);
        olympusIMClient = guiceInjector.getInstance(OlympusIMClient.class);
        truncateDb();
    }
    @Test
    void testInvalidDeemedTransaction() {
        var transactionId = IdGenerator.generate("T").getId();
        var complaintId = IdGenerator.generate("CS").getId();
        wireMockServer.resetRequests();
        MockingUtils.setupPaymentsApiFullRefundExistsMocking(transactionId, 100, "COMPLETED");
        MockingUtils.setupPaymentsRaiseComplaintMocking();
        Mockito.doReturn(true).when(olympusIMClient).verifyPermission(Mockito.any(), Mockito.any());

        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        final var stratosError = Assertions.assertThrows(DisputeException.class, () ->
            udirResource.raiseComplaint(UdirOutgoingRaiseComplaintRequest.builder()
                .paymentTransactionId(transactionId)
                .requestCode(ComplaintRequestType.COMPLAINT_REQ_P2P_DEEMED)
                .adjAmount(100)
                .disputeStage(DisputeStageDto.FIRST_LEVEL)
                .complaintId(complaintId)
                .build(), serviceUserPrincipal));

        Assertions.assertEquals(StratosErrorCodeKey.INVALID_UDIR_COMPLAINT,
            stratosError.getErrorCode());
    }
}
