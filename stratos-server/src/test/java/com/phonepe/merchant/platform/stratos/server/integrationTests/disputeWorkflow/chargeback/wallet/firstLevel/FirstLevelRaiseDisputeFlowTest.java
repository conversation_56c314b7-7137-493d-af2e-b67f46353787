package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.wallet.firstLevel;


import com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeCategory;
import com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeStage;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.responses.CreateDisputeResponse;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.wallet.WalletBaseTest;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import java.time.LocalDateTime;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mockito;

class FirstLevelRaiseDisputeFlowTest extends WalletBaseTest {

    String USER_ID = "USER_ID";

    @ParameterizedTest(name = "{0}")
    @MethodSource(API_SERVICE_CATEGORY)
    void createDisputeFlowTest(
        DisputeCategory disputeCategory){

        var request = TestDataUtils.getWalletDisputeRequest(DisputeStage.FIRST_LEVEL, disputeCategory, dispute.getTransactionReferenceId(),
            (int) dispute.getTransactionAmount());

        createEntrySetupForAPI("WALLET", String.valueOf(LocalDateTime.now()), dispute.getTransactionReferenceId());
        CreateDisputeResponse response = disputeService.createDispute(request, USER_ID);
        Assertions.assertNotNull(response.getDisputeId());
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource(API_SERVICE_CATEGORY)
    void createDisputeFlowTestForExternalUser(DisputeCategory disputeCategory){

        var request = TestDataUtils.getWalletDisputeRequest(DisputeStage.FIRST_LEVEL, disputeCategory, dispute.getTransactionReferenceId(),
            (int) dispute.getTransactionAmount());

        createEntrySetupForAPIForExternalUser("WALLET", String.valueOf(LocalDateTime.now()), dispute.getTransactionReferenceId());
        final var exception = Assertions.assertThrows(
            DisputeException.class, ()->disputeService.createDispute(request, USER_ID));
        Assertions.assertEquals(StratosErrorCodeKey.NON_EXTERNAL_MERCHANT_TRANSACTION,
            exception.getErrorCode());
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource(API_SERVICE_CATEGORY)
    void createDisputeWithWrongTransactionIDFlowTest(
        DisputeCategory disputeCategory){

        var request = TestDataUtils.getWalletDisputeRequest(
            com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeStage.FIRST_LEVEL, disputeCategory, String.format("12345_" + disputeCategory),
            (int) dispute.getTransactionAmount());

        createEntrySetupForAPI("WALLET", String.valueOf(LocalDateTime.now()),
            dispute.getTransactionReferenceId());

        final var exception = Assertions.assertThrows(
            DisputeException.class, ()->disputeService.createDispute(request, USER_ID));
        Assertions.assertEquals(StratosErrorCodeKey.COMMUNICATION_ERROR,
            exception.getErrorCode());
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource(API_SERVICE_CATEGORY)
    void createDisputeWithDuplicateChargebackFlowTest(DisputeCategory disputeCategory){
        disputeService.persistDisputeAndDisputeWorkflow(dispute, disputeWorkflow);
        var request = TestDataUtils.getWalletDisputeRequest(
            com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeStage.FIRST_LEVEL, disputeCategory, dispute.getTransactionReferenceId(),
            (int) dispute.getTransactionAmount());

        createEntrySetupForAPI("WALLET", String.valueOf(LocalDateTime.now()),
            dispute.getTransactionReferenceId());

        final var exception = Assertions.assertThrows(
            DisputeException.class, ()->disputeService.createDispute(request, USER_ID));
        Assertions.assertEquals(StratosErrorCodeKey.DUPLICATE_CHARGEBACK
            , exception.getErrorCode());
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource(API_SERVICE_CATEGORY)
    void createDisputeWithMoreThanTransactionAmountFlowTest(
        com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeCategory disputeCategory){

        var request = TestDataUtils.getWalletDisputeRequest(com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeStage.FIRST_LEVEL, disputeCategory, dispute.getTransactionReferenceId(),
            (int) dispute.getTransactionAmount() + 1);

        createEntrySetupForAPI("WALLET", String.valueOf(LocalDateTime.now()),
            request.getTransactionId());
        Assertions.assertThrows(
            DisputeException.class, ()->disputeService.createDispute(request, USER_ID));
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource(API_SERVICE_CATEGORY)
    void createDisputeWithTllBreachedFlowTest(
        com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeCategory disputeCategory){

        var request = TestDataUtils.getWalletDisputeRequest(
            com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeStage.FIRST_LEVEL, disputeCategory, dispute.getTransactionReferenceId(),
            (int) dispute.getTransactionAmount());

        createEntrySetupForAPI("WALLET", "1626281167000", request.getTransactionId());
        final var exception = Assertions.assertThrows(
            DisputeException.class, ()->disputeService.createDispute(request, USER_ID));
        Assertions.assertEquals(StratosErrorCodeKey.TTL_BREACHED,
            exception.getErrorCode());
    }

    @Override
    protected com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage getDisputeStage() {
        return com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage.FIRST_LEVEL;
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource(API_SERVICE_CATEGORY)
    void createDisputeForInternalRailFlowTest(
        com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeCategory disputeCategory){

        var request = TestDataUtils.getWalletDisputeRequest(
            com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeStage.FIRST_LEVEL, disputeCategory, dispute.getTransactionReferenceId(),
            (int) dispute.getTransactionAmount());

        createEntrySetupForInternalRail("WALLET", String.valueOf(LocalDateTime.now()));
        final var exception = Assertions.assertThrows(
            DisputeException.class, ()->disputeService.createDispute(request, USER_ID));
        Assertions.assertEquals(StratosErrorCodeKey.NON_EXTERNAL_MERCHANT_TRANSACTION,
            exception.getErrorCode());
    }

    @Test
    void createDisputeFlowForFraudTest(){

        var request = TestDataUtils.getWalletDisputeRequest(DisputeStage.FIRST_LEVEL, DisputeCategory.FRAUD, dispute.getTransactionReferenceId(),
            (int) dispute.getTransactionAmount());

        createEntrySetupForAPI("WALLET", String.valueOf(LocalDateTime.now()), dispute.getTransactionReferenceId());

        final var exception = Assertions.assertThrows(
            DisputeException.class, ()->disputeService.createDispute(request, USER_ID));
        Assertions.assertEquals(StratosErrorCodeKey.FRAUD_CHARGEBACK_NOT_ALLOWED,
            exception.getErrorCode());
    }
}
