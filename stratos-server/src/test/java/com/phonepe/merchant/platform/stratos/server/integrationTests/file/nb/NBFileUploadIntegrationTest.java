package com.phonepe.merchant.platform.stratos.server.integrationTests.file.nb;

import static com.phonepe.platform.schema.utils.SerDeUtils.getObjectMapper;

import com.fasterxml.jackson.databind.JsonNode;
import com.phonepe.merchant.platform.stratos.models.files.responses.PrimusSOFResponse;
import com.phonepe.merchant.platform.stratos.server.BaseTest;
import com.phonepe.merchant.platform.stratos.server.LoadOnlyOnClassLevelBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.models.primus.DestinationRequest;
import com.phonepe.merchant.platform.stratos.server.core.models.primus.EligibilityRequest;
import com.phonepe.merchant.platform.stratos.server.core.models.primus.FileRowStatus;
import com.phonepe.merchant.platform.stratos.server.core.resources.PrimusResource;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.integrationTests.file.nb.beans.NetBankingChargebackBean;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.testcontainers.shaded.com.fasterxml.jackson.core.JsonProcessingException;
import org.testcontainers.shaded.com.fasterxml.jackson.databind.ObjectMapper;

public class NBFileUploadIntegrationTest extends LoadOnlyOnClassLevelBaseTest {

    private PrimusResource primusResource;
    private DisputeService disputeService;

    @BeforeEach
    void setUpGuiceInjection() {
        primusResource = guiceInjector.getInstance(PrimusResource.class);
        disputeService = guiceInjector.getInstance(DisputeService.class);
        truncateDb();
    }

    static Stream<Arguments> provideArgumentParameters() {

        return Stream.of(
            Arguments.of("NetPe Transaction Test",
                "NP", "NETPE", 1.0, "test101", "testFile101"
            ),
            Arguments.of("PG transaction Test",
                "PG", "TPSL", 1.0, "test102", "testFile102")
        );

    }

    @ParameterizedTest(name = "{0}")
    @MethodSource("provideArgumentParameters")
    public void test(String name, String prefix, String source, Double amount,
        String fileId, String fileName) throws Exception {

        final var txnId = IdGenerator.generate(prefix).getId();
        final var transactionReferenceId = IdGenerator.generate("T").getId();

        MockingUtils.setupPgTransportTransactionDetailMocking(txnId, transactionReferenceId);
        MockingUtils.setUpNetPeTransactionDetailsMocking(txnId, transactionReferenceId);
        MockingUtils.setUpPaymentsApiFullRefundExistsMockingPaidFromNB(transactionReferenceId,
            100, txnId);
        MockingUtils.setupEventIngestionApiMocking();

        EligibilityRequest eligibilityRequest = EligibilityRequest.builder()
            .fileId(fileId)
            .fileName(fileName)
            .rowCount(1)
            .configId("NB_CHARGEBACK-NB_FIRST_LEVEL")
            .build();
        var fileResponse = primusResource.processPrimusFileSignal(eligibilityRequest);
        Assertions.assertEquals(200, fileResponse.getStatus());
        Assertions.assertTrue(((PrimusSOFResponse)fileResponse.getEntity()).isCanIngest());

        DestinationRequest<JsonNode,JsonNode> destinationRequest =
            DestinationRequest.<JsonNode, JsonNode>builder()
                .fileId(fileId)
                .key("key101")
                .status(FileRowStatus.SUCCESS)
                .request(getObjectMapper().readValue(TestDataUtils.getNBContent(source, txnId, amount), JsonNode.class))
                .build();


        var response = primusResource.processPrimusSignal(destinationRequest);

        Assertions.assertEquals(202, response.getStatus());
        AssertionUtils.assertFileProcessed(fileId,20, TimeUnit.SECONDS);

    }

}
