package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.commons;

import com.phonepe.merchant.platform.stratos.models.commons.DateRange;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.ChargebackBaseTestCase;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;

public class ChargebackTTLBreachIT extends ChargebackBaseTestCase {

    @Test
    void testInvalidDateRangeEndDateAfterCurrentDateScenario() {

        final var currentDate = LocalDate.now();
        final var dateRange = DateRange.builder()
            .startDate(currentDate)
            .endDate(currentDate.plusDays(1))
            .build();

        final var stratosError = Assertions.assertThrows(DisputeException.class,
            () -> chargebackResource.updateChargebackStateBreachingTtl(dateRange));

        Assertions.assertEquals(StratosErrorCodeKey.INVALID_END_DATE, stratosError.getErrorCode());
    }

    @Override
    protected DisputeStage getDisputeStage() {
        return DisputeStage.FIRST_LEVEL;
    }

    @Override
    protected DisputeType getDisputeType() {
        return DisputeType.UPI_CHARGEBACK;
    }
}
