package com.phonepe.merchant.platform.stratos.server.integrationTests.row;

import com.fasterxml.jackson.databind.MappingIterator;
import com.fasterxml.jackson.dataformat.csv.CsvMapper;
import com.fasterxml.jackson.dataformat.csv.CsvSchema;
import com.phonepe.merchant.platform.stratos.models.commons.DateRange;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeTypeDto;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.filters.DateRangeFilter;
import com.phonepe.merchant.platform.stratos.models.files.FileFormat;
import com.phonepe.merchant.platform.stratos.models.files.FileTypeDto;
import com.phonepe.merchant.platform.stratos.models.row.EdcRowDto;
import com.phonepe.merchant.platform.stratos.models.row.PgMisRowDto;
import com.phonepe.merchant.platform.stratos.models.row.RowTransactionType;
import com.phonepe.merchant.platform.stratos.models.row.RowTypeDto;
import com.phonepe.merchant.platform.stratos.models.row.requests.RowDownloadData;
import com.phonepe.merchant.platform.stratos.models.row.requests.RowDownloadRequest;
import com.phonepe.merchant.platform.stratos.models.row.requests.RowHistoryRequest;
import com.phonepe.merchant.platform.stratos.server.LoadOnlyOnClassLevelBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.models.RowState;
import com.phonepe.merchant.platform.stratos.server.core.resources.FileResource;
import com.phonepe.merchant.platform.stratos.server.core.resources.RowResource;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.RowService;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.olympus.im.models.user.HumanUserDetails;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import java.io.IOException;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

public class RowSummaryDownloadTest extends LoadOnlyOnClassLevelBaseTest {

    private RowService rowService;
    private RowResource rowResource;
    private OlympusIMClient olympusIMClient;
    private DisputeService disputeService;
    private FileResource fileResource;

    @BeforeEach
    void setup() {
        rowService = guiceInjector.getInstance(RowService.class);
        rowResource = guiceInjector.getInstance(RowResource.class);
        olympusIMClient = guiceInjector.getInstance(OlympusIMClient.class);
        disputeService = guiceInjector.getInstance(DisputeService.class);
        fileResource = guiceInjector.getInstance(FileResource.class);
        MARIA_DB_CONTAINER.executeQuery("TRUNCATE stratos_shard_1.file;\n"
            + "TRUNCATE stratos_shard_2.file;\n"
            + "TRUNCATE stratos_shard_1.file_audit;\n"
            + "TRUNCATE stratos_shard_2.file_audit;\n"
            + "TRUNCATE stratos_shard_1.row;\n"
            + "TRUNCATE stratos_shard_2.row;\n"
            + "TRUNCATE stratos_shard_1.row_audit;\n"
            + "TRUNCATE stratos_shard_2.row_audit;", true);
    }

    @Test
    void testDownload() throws IOException {
        final var transactionReferenceId = IdGenerator.generate("T").getId();
        final var pgTransactionId = IdGenerator.generate("PG").getId();
        MockingUtils.setupPgTransportTransactionDetailMocking(pgTransactionId,
            transactionReferenceId);

        MockingUtils.setupPaymentsApiNoRefundExistsMockingPaidFromDebitCard(
            transactionReferenceId, 100);
        rowService.processSignal(TestDataUtils.getPgMisReportRequest(pgTransactionId,
            RowTransactionType.CHARGEBACK, 1.20, "mis_reports_file1"));
        Mockito.doReturn(true).when(olympusIMClient).verifyPermission(Mockito.any(), Mockito.any());

        AssertionUtils.assertRowStateEquals(RowState.FAILED, "mis_reports_file1", 15,
            TimeUnit.SECONDS);

        var pgMisRowResponse = rowService.history(RowHistoryRequest.builder()
            .rowTypes(Set.of(RowTypeDto.PG_MIS_ROW))
            .dateRangeFilter(DateRangeFilter.builder()
                .dateRange(DateRange.builder().startDate(LocalDate.now().minusDays(2))
                    .endDate(LocalDate.now())
                    .build())
                .build())
            .build());

        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        final var response = rowResource.download(RowDownloadRequest.builder()
            .rowType(RowTypeDto.PG_MIS_ROW)
            .fileFormat(FileFormat.CSV)
            .rowList(Collections.singletonList(RowDownloadData.builder()
                .rowId(((PgMisRowDto) pgMisRowResponse.getRows().get(0)).getRowId())
                .sourceId("mis_reports_file1").build())).build(), serviceUserPrincipal);

        final CsvMapper mapper = new CsvMapper();
        final CsvSchema schema = CsvSchema.emptySchema().withHeader();
        final List<Map<String, String>> list = new LinkedList<>();
        final MappingIterator<Map<String, String>> iterator = mapper.readerWithTypedSchemaFor(
                Map.class)
            .with(schema)
            .readValues((byte[]) response.getEntity());
        while (iterator.hasNext()) {
            list.add(iterator.next());
        }
        Assertions.assertEquals(1, list.size());

        Assertions.assertEquals(pgTransactionId, list.get(0).get("Transaction Id"));
        Assertions.assertEquals("mis_reports_file1", list.get(0).get("Source Id"));
        Assertions.assertEquals("FAILED", list.get(0).get("Row State"));
        Assertions.assertEquals("CHARGEBACK", list.get(0).get("Transaction Type"));
        Assertions.assertEquals("HDFC", list.get(0).get("Interchange"));
    }

    @Test
    void testDownloadForEdc() throws Exception {
        final var transactionReferenceId = IdGenerator.generate("T").getId();
        final var merchantTransactionId = IdGenerator.generate("MT").getId();
        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForEdcCreateChargebackApiMocking();
        MockingUtils.setEdcTransactionDetails("BijliPay", "MERCHANTID", "TERMINALID", "RRN",
            transactionReferenceId, 100, 0, merchantTransactionId);
        MockingUtils.setupEdcTransactionsFromMerchantTransactionId(transactionReferenceId, 100, 0, merchantTransactionId);
        MockingUtils.setupEventIngestionApiMocking();
        MockingUtils.setEdcUnBlockReversals(merchantTransactionId);
        MockingUtils.setEdcBlockReversals(merchantTransactionId);
        final var userDetailsOlympus = HumanUserDetails.builder()
            .userId("test-user")
            .build();

        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        var edcUploadResponse = fileResource.upload(DisputeTypeDto.EDC_CHARGEBACK,
            FileTypeDto.EDC_FIRST_LEVEL, TestDataUtils.edcChargebackCSVFileStream(),
            FormDataContentDisposition.name("randomName").fileName("file.csv")
                .build(),"AuthToken", serviceUserPrincipal);
        AssertionUtils.assertFileProcessed(edcUploadResponse.getId(), 20, TimeUnit.SECONDS);

        rowService.processSignal(TestDataUtils.getEdcReportRequest(transactionReferenceId,
            RowTransactionType.CHARGEBACK, 100.0, "mis_reports_file1"));
        Mockito.doReturn(true).when(olympusIMClient).verifyPermission(Mockito.any(), Mockito.any());
        AssertionUtils.assertRowStateEquals(RowState.FAILED, "mis_reports_file1", 15,
            TimeUnit.SECONDS);

        var edcMisRowResponse = rowService.history(RowHistoryRequest.builder()
            .rowTypes(Set.of(RowTypeDto.EDC_MIS_ROW))
            .dateRangeFilter(DateRangeFilter.builder()
                .dateRange(DateRange.builder().startDate(LocalDate.now().minusDays(2))
                    .endDate(LocalDate.now())
                    .build())
                .build())
            .build());

        final var response = rowResource.download(RowDownloadRequest.builder()
            .rowType(RowTypeDto.EDC_MIS_ROW)
            .fileFormat(FileFormat.CSV)
            .rowList(Collections.singletonList(RowDownloadData.builder()
                .rowId(((EdcRowDto) edcMisRowResponse.getRows().get(0)).getRowId())
                .sourceId("mis_reports_file1").build())).build(), serviceUserPrincipal);

        final CsvMapper mapper = new CsvMapper();
        final CsvSchema schema = CsvSchema.emptySchema().withHeader();
        final List<Map<String, String>> list = new LinkedList<>();
        final MappingIterator<Map<String, String>> iterator = mapper.readerWithTypedSchemaFor(
                Map.class)
            .with(schema)
            .readValues((byte[]) response.getEntity());
        while (iterator.hasNext()) {
            list.add(iterator.next());
        }
        Assertions.assertEquals(1, list.size());
        Assertions.assertEquals(transactionReferenceId, list.get(0).get("Transaction Id"));
        Assertions.assertEquals("mis_reports_file1", list.get(0).get("Source Id"));
        Assertions.assertEquals("FAILED", list.get(0).get("Row State"));
        Assertions.assertEquals("abcd", list.get(0).get("Transaction Type"));
    }
}
