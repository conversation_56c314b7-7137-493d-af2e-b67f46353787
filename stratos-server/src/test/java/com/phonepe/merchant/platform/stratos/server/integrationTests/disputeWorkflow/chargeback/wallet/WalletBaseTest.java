package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.wallet;

import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.ChargebackBaseTestCase;

public abstract class WalletBaseTest extends ChargebackBaseTestCase {

    @Override
    protected DisputeType getDisputeType() {
        return DisputeType.WALLET_CHARGEBACK;
    }
}
