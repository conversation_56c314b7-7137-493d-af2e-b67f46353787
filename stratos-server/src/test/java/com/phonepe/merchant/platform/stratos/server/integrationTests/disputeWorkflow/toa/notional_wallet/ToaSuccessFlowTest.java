package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.toa.notional_wallet;

import com.phonepe.growth.neuron.pulse.Pulse;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.DisputeWorkflowKey;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.ToaDisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.ToaState;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.NeuronTestUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.models.payments.merchant.MerchantTransactionState;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
public class ToaSuccessFlowTest extends NotionalToaBaseTestCases {

    @Test
    void testTOAOpenThenClosedFlow() {
        testTillToaOpened();
        var signalList = pulse.getSignals();
        Pulse closeSignal = NeuronTestUtils.getNeuronPulse(signalList.get(0).getValue().toString()
                , signalList.get(1).getValue().toString(),
                ToaState.CLOSE, signalList.get(4).getValue().toString());

        NeuronTestUtils.createNotionalToaPulse(closeSignal);

        AssertionUtils.assertDisputeWorkflowStateEquals(disputeWorkflow.getTransactionReferenceId(),
                disputeWorkflow.getDisputeWorkflowId(), DisputeWorkflowState.TOA_CLOSED, 5L, TimeUnit.SECONDS);

    }

    @Test
    void testTOAInitiateSuccessFlow() {
        MockingUtils.setUpPaymentPayApiV2(true, 200);
        testTillToaOpened();
        testTillInitiated(MerchantTransactionState.COMPLETED);
        AssertionUtils.assertDisputeWorkflowStateEquals(
                disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
                DisputeWorkflowState.TOA_COMPLETED,
                30L, TimeUnit.SECONDS);
    }

    @Test
    void testTOAPendingToCompletedFlow() {

        MockingUtils.setUpPaymentPayApiV2(true, 200);
        testTillToaOpened();
        testTillInitiated(MerchantTransactionState.CREATED);
        AssertionUtils.assertDisputeWorkflowStateEquals(
                disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
                DisputeWorkflowState.TOA_PENDING,
                30L, TimeUnit.SECONDS);
        List<ToaDisputeMetadata> disputeMetadataList = AssertionUtils.getToaDisputeMetadata(
                disputeWorkflow.getTransactionReferenceId(),
                disputeWorkflow.getDisputeWorkflowId());
        Assertions.assertEquals(1, disputeMetadataList.size());
        ToaDisputeMetadata toaDisputeMetadata = disputeMetadataList.get(0);

        MockingUtils.setMerchantTransactionsStatus("PHONEPETOA",
                toaDisputeMetadata.getMerchantOrderId(), MerchantTransactionState.COMPLETED,
                disputeWorkflow.getDisputedAmount());

        var disputeWorkflowKey = DisputeWorkflowKey.builder()
                .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
                .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
                .build();
        var toaSummary = toaResource.reconcileToa(serviceUserPrincipal, disputeWorkflowKey, DisputeType.NOTIONAL_CREDIT_TOA);

        Assertions.assertEquals(toaSummary.getData().getCurrentState(),
                DisputeWorkflowState.TOA_COMPLETED.name());
    }

    @Test
    void testKSEnabledAndMovedToExternallyCompleted() {
        testTillToaOpened();
        MockingUtils.enableKillSwitchMocking(DisputeType.NOTIONAL_CREDIT_TOA);
        initiatePulseSignal();
        AssertionUtils.assertDisputeWorkflowStateEquals(
                disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
                DisputeWorkflowState.TOA_BLOCKED_DUE_TO_KS,
                5L, TimeUnit.SECONDS);
        var response = toaResource.processToaExternally(serviceUserPrincipal, disputeWorkflow.getTransactionReferenceId(),
                disputeWorkflow.getDisputeWorkflowId(), TestDataUtils.getCommentContext(), DisputeType.NOTIONAL_CREDIT_TOA);

        Assertions.assertTrue(response.isSuccess());
        AssertionUtils.assertToaSummary(response.getData(), disputeWorkflow, DisputeWorkflowState.TOA_COMPLETED_EXTERNALLY);
    }

    @Test
    void testChargeBackAcceptanceCheckForNonFraudImpl() {

        MockingUtils.setUpPaymentPayApiV2(true, 200);
        testTillToaOpened();
        testTillInitiated(MerchantTransactionState.COMPLETED);

        MockingUtils.setupGetMerchantProfileApiMocking(disputeWorkflow.getDispute().getMerchantId(),
            "P2P_MERCHANT");

        UnsupportedOperationException exception = Assertions.assertThrows(
            UnsupportedOperationException.class, () -> {
                disputeService.buildChargebackPayload(disputeWorkflow);
            });

        Assertions.assertEquals(
            "Invalid build Chargeback kratos Payload for dispute type NOTIONAL_CREDIT_TOA",
            exception.getMessage());
    }

}
