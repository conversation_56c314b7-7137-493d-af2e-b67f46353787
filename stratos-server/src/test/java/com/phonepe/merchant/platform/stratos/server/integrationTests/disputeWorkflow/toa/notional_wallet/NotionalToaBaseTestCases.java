package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.toa.notional_wallet;

import com.phonepe.growth.neuron.pulse.Pulse;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.DisputeWorkflowKey;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.ToaDisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.ToaState;
import com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.toa.BaseToaTestCase;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.NeuronTestUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.resources.ToaResource;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.models.payments.merchant.MerchantTransactionState;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.olympus.im.models.security.ServiceUserPrincipal;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;

import java.time.LocalDate;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class NotionalToaBaseTestCases extends BaseToaTestCase {

    protected ToaResource toaResource;
    protected ServiceUserPrincipal serviceUserPrincipal;
    public static String PHONEPETOA = "PHONEPETOA";

    @BeforeEach
    void setUpGuiceInjection() {
        toaResource = guiceInjector.getInstance(ToaResource.class);
        olympusIMClient = guiceInjector.getInstance(OlympusIMClient.class);
        MockingUtils.setupEventIngestionApiMocking();
        serviceUserPrincipal = MockingUtils.getAndMockMerchantOpsUser(olympusIMClient);
        truncateDb();
    }

    @Override
    protected DisputeStage getDisputeStage() {
        return DisputeStage.FIRST_LEVEL;
    }

    @Override
    protected DisputeType getDisputeType() {
        return DisputeType.NOTIONAL_CREDIT_TOA;
    }
    protected Pulse pulse;
    protected DisputeWorkflow disputeWorkflow;


    void testTillToaOpened() {
        String transactionRefId = IdGenerator.generate("T").getId();
        String catalystId = IdGenerator.generate("CT").getId();
        String date = LocalDate.now().minusDays(90).toString();

        MockingUtils.setUpPaymentDetailMockingBasedOnInstrument("WALLET", 100L, transactionRefId);

        //create neuron pulse
        pulse = NeuronTestUtils.getNeuronPulse(transactionRefId, catalystId, ToaState.OPEN, date);
        NeuronTestUtils.createNotionalToaPulse(pulse);
        //Wait till DisputeWorkflow is created
        disputeWorkflow = AssertionUtils
                .assertAndGetDisputeWorkflowCreation(transactionRefId, DisputeType.NOTIONAL_CREDIT_TOA, DisputeStage.FIRST_LEVEL);

        AssertionUtils.assertDisputeWorkflowStateEquals(
                transactionRefId, disputeWorkflow.getDisputeWorkflowId(),
                DisputeWorkflowState.TOA_OPENED, 15L, TimeUnit.SECONDS
        );
    }

    void testTillInitiated(MerchantTransactionState merchantTransactionState) {

        String transactionReferenceId = disputeWorkflow.getTransactionReferenceId();
        String merchantOrderId = "TOA-0-" + transactionReferenceId;

        MockingUtils.disableKillSwitchMocking();
        initiatePulseSignal();
        List<ToaDisputeMetadata> disputeMetadataList = AssertionUtils.getToaDisputeMetadata(
                transactionReferenceId,
                disputeWorkflow.getDisputeWorkflowId());
        Assertions.assertEquals(1, disputeMetadataList.size());
        ToaDisputeMetadata toaDisputeMetadata = disputeMetadataList.get(0);

        // Assert Metadata
        Assertions.assertEquals(merchantOrderId, toaDisputeMetadata.getMerchantOrderId());
        Assertions.assertEquals(transactionReferenceId, toaDisputeMetadata.getTransactionReferenceId());
        Assertions.assertEquals(disputeWorkflow.getDisputeWorkflowId(), toaDisputeMetadata.getDisputeWorkflowId());

        MockingUtils.setMerchantTransactionsStatus(PHONEPETOA,
                merchantOrderId, merchantTransactionState, disputeWorkflow.getDisputedAmount());
    }

    void testTillToaInitiationFailed() {
        testTillToaOpened();
        MockingUtils.setUpPaymentPayApiV2(false, 500);
        MockingUtils.disableKillSwitchMocking();
        initiatePulseSignal();
        AssertionUtils.assertDisputeWorkflowStateEquals(disputeWorkflow.getTransactionReferenceId(),
                disputeWorkflow.getDisputeWorkflowId(), DisputeWorkflowState.TOA_INITIATION_FAILED, 5L,
                TimeUnit.SECONDS);
    }

    void testTillToaFailedAfterMaxRetry() {

        testTillToaOpened();
        String merchantOrderId2 = "TOA-1-"+disputeWorkflow.getTransactionReferenceId();
        MockingUtils.setMerchantTransactionsStatus(PHONEPETOA, merchantOrderId2,
                MerchantTransactionState.FAILED, disputeWorkflow.getDisputedAmount());
        String merchantOrderId3 = "TOA-2-"+ disputeWorkflow.getTransactionReferenceId();
        MockingUtils.setMerchantTransactionsStatus(PHONEPETOA, merchantOrderId3,
                MerchantTransactionState.FAILED, disputeWorkflow.getDisputedAmount());
        MockingUtils.setUpPaymentPayApiV2(true, 200);
        testTillInitiated(MerchantTransactionState.FAILED);
        AssertionUtils.assertDisputeWorkflowStateEquals(disputeWorkflow.getTransactionReferenceId(),
                disputeWorkflow.getDisputeWorkflowId(), DisputeWorkflowState.TOA_FAILED_AFTER_MAX_AUTO_RETRY,
                30L, TimeUnit.SECONDS);

    }
    
    protected void initiatePulseSignal(){
        var signalList = pulse.getSignals();
        var initiatePulse = NeuronTestUtils.getNeuronPulse(signalList.get(0).getValue().toString()
                ,signalList.get(1).getValue().toString(),
                ToaState.INITIATE, signalList.get(4).getValue().toString());
        NeuronTestUtils.createNotionalToaPulse(initiatePulse);
    }

    protected DisputeWorkflowKey getDisputeWorkflowKey() {
        return DisputeWorkflowKey.builder()
                .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
                .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
                .build();
    }
}
