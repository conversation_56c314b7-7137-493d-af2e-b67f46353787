package com.phonepe.merchant.platform.stratos.server.integrationTests.disputemanagement;

import com.phonepe.merchant.platform.stratos.models.disputemanagement.DisputeStatus;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.PaginationDetails;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.requests.*;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.responses.DisputeDetails;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.responses.DisputeSummaries;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.responses.DisputeSummary;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.LoadOnlyOnClassLevelBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.*;
import com.phonepe.merchant.platform.stratos.server.core.resources.DisputeManagementResource;
import com.phonepe.merchant.platform.stratos.server.core.utils.StorageUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.ResourceUtils;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Stream;

@Slf4j
public class DisputeManagementResourceTest extends LoadOnlyOnClassLevelBaseTest {
    private DisputeManagementResource disputeManagementResource;
    private DisputeWorkflowRepository disputeWorkflowRepository;
    private DisputeRepository disputeRepository;
    private final String merchantId = "TestMerchant";
    private final String transactionId = "TXN1234";
    private final long disputedAmount = 100;

    @BeforeEach
    void setUpGuiceInjection() {
        disputeManagementResource = guiceInjector.getInstance(DisputeManagementResource.class);
        disputeWorkflowRepository = guiceInjector.getInstance(DisputeWorkflowRepository.class);
        disputeRepository = guiceInjector.getInstance(DisputeRepository.class);
        truncateDb();
    }

    @Test
    void testListAll() {
        int limit  = 5;
        int offset = 1;
        int total = 10;
        DisputeStatus status = DisputeStatus.NEEDS_ACTION;
        //NEEDS_ACTION state
        for(int i = 0; i < total; i++)
            createDispute(DisputeWorkflowState.REFUND_BLOCKED);

        //UNDER_REVIEW state
        for(int i = 0; i < total; i++)
            createDispute(DisputeWorkflowState.FULFILMENT_DOCUMENTS_RECEIVED);

        try {
            DisputeSummaries summaries = disputeManagementResource.
                listDisputes(TestDataUtils.getOlympusUser(),
                    ResourceUtils.getMd5Hash(merchantId),
                    DisputeFilterParams
                        .builder()
                            .merchantId(merchantId)
                            .status(status)
                            .limit(limit)
                            .offset(offset)
                        .build());
            List<DisputeSummary> disputes = summaries.getDisputes();
            PaginationDetails paginationDetails = summaries.getPaginationDetails();
            Assertions.assertEquals(paginationDetails.getCount(), disputes.size());
            Assertions.assertEquals(paginationDetails.getCount(), limit);
            for (int i = 0; i < disputes.size(); i++) {
                Assertions.assertEquals(disputes.get(i).getDisputeStatus(),status);
            }
            Assertions.assertEquals(paginationDetails.getTotal(),total);
        } catch (final Exception e) {
            log.error("Error Occurred", e);
            Assertions.fail("Error shouldn't occur for this test case");
        }
    }

    @Test
    void testListAllInvalidMerchantId() {
        try {
            final var thrown = Assertions.assertThrows(DisputeException.class,
                () -> disputeManagementResource.
                    listDisputes(TestDataUtils.getOlympusUser(),
                        merchantId,
                        DisputeFilterParams.builder()
                            .merchantId(merchantId)
                            .limit(10)
                            .offset(0)
                        .build()));
            Assertions.assertEquals(StratosErrorCodeKey.INVALID_MERCHANT_ID,
                    thrown.getErrorCode());
        } catch (final Exception e) {
            log.error("Error Occurred", e);
            Assertions.fail("Error shouldn't occur for this test case");
        }
    }

    @Test
    void testListAllInvalidDateRange() {
        try {
            final var thrown = Assertions.assertThrows(DisputeException.class,
                () -> disputeManagementResource.
                    listDisputes(TestDataUtils.getOlympusUser(),
                        ResourceUtils.getMd5Hash(merchantId),
                        DisputeFilterParams.builder()
                            .merchantId(merchantId)
                            .dateRange(DateRangeParam.builder()
                                    .startDate(LocalDate.MAX)
                                    .endDate(LocalDate.MIN)
                                    .build())
                            .limit(10)
                            .offset(0)
                        .build()));

            Assertions.assertEquals(StratosErrorCodeKey.INVALID_END_DATE,
                    thrown.getErrorCode());
        } catch (final Exception e) {
            log.error("Error Occurred", e);
            Assertions.fail("Error shouldn't occur for this test case");
        }
    }

    @ParameterizedTest(name = "{0},{1}")
    @MethodSource("provideArgumentWithInvalidPaginationParams")
    void testListAllInvalidPaginationParams(int limit, int offset) {
        try {
            final var thrown = Assertions.assertThrows(DisputeException.class,
                () -> disputeManagementResource.
                    listDisputes(TestDataUtils.getOlympusUser(),
                        ResourceUtils.getMd5Hash(merchantId),
                        DisputeFilterParams.builder()
                            .merchantId(merchantId)
                            .limit(limit)
                            .offset(offset)
                            .build()));
            Assertions.assertEquals(StratosErrorCodeKey.INVALID_PAGINATION_PARAMETERS,
                    thrown.getErrorCode());
        } catch (final Exception e) {
            log.error("Error Occurred", e);
            Assertions.fail("Error shouldn't occur for this test case");
        }
    }

    @Test
    void testListAllInvaildOffsetWithSize() {
        int limit  = 5;
        int offset = 11;
        int total = 10;
        DisputeStatus status = DisputeStatus.NEEDS_ACTION;
        //NEEDS_ACTION state
        for(int i = 0; i < total; i++)
            createDispute(DisputeWorkflowState.REFUND_BLOCKED);

        try {
            final var thrown = Assertions.assertThrows(DisputeException.class,
                () -> disputeManagementResource.
                listDisputes(TestDataUtils.getOlympusUser(),
                    ResourceUtils.getMd5Hash(merchantId),
                    DisputeFilterParams
                        .builder()
                        .merchantId(merchantId)
                        .status(status)
                        .limit(limit)
                        .offset(offset)
                    .build()));
            Assertions.assertEquals(StratosErrorCodeKey.INVALID_PAGINATION_PARAMETERS,
                    thrown.getErrorCode());

        } catch (final Exception e) {
            log.error("Error Occurred", e);
            Assertions.fail("Error shouldn't occur for this test case");
        }
    }

    @Test
    void testGetDetails() {
        String disputeId = createDispute(DisputeWorkflowState.REFUND_BLOCKED);

        try {
            DisputeDetails details = disputeManagementResource.
                getDisputeDetails(TestDataUtils.getOlympusUser(),
                    ResourceUtils.getMd5Hash(merchantId),
                    DisputeIdentifierParams.builder()
                        .merchantId(merchantId)
                        .disputeId(disputeId)
                    .build());
            Assertions.assertEquals(disputeId,details.getDisputeId());
        } catch (final Exception e) {
            log.error("Error Occurred", e);
            Assertions.fail("Error shouldn't occur for this test case");
        }
    }

    @Test
    void testGetDetailsInvalidDisputeId() {

        try {
            final var thrown = Assertions.assertThrows(DisputeException.class,
                () -> disputeManagementResource.
                getDisputeDetails(TestDataUtils.getOlympusUser(),
                    ResourceUtils.getMd5Hash(merchantId),
                    DisputeIdentifierParams.builder()
                        .merchantId(merchantId)
                        .disputeId("disputeId")
                    .build()));
            Assertions.assertEquals(StratosErrorCodeKey.DISPUTE_NOT_FOUND,
                    thrown.getErrorCode());
        } catch (final Exception e) {
            log.error("Error Occurred", e);
            Assertions.fail("Error shouldn't occur for this test case");
        }
    }

    @Test
    void testAccept() {
        String disputeId = createDispute(DisputeWorkflowState.REFUND_BLOCKED);
        try {
            DisputeDetails details = disputeManagementResource.
                acceptDispute(TestDataUtils.getOlympusUser(),
                    ResourceUtils.getMd5Hash(merchantId),
                    DisputeIdentifierParams.builder()
                        .merchantId(merchantId)
                        .disputeId(disputeId)
                    .build());
            Assertions.assertEquals(DisputeStatus.UNDER_REVIEW,details.getDisputeStatus());
            List<DisputeWorkflow> wfList = disputeWorkflowRepository.select(transactionId);
            Assertions.assertEquals(1,wfList.size());
            Assertions.assertEquals(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK,
                    wfList.get(0).getCurrentState());
            Assertions.assertEquals(disputedAmount,
                    ((FinancialDisputeWorkflow)wfList.get(0)).getAcceptedAmount());

        } catch (final Exception e) {
            log.error("Error Occurred", e);
            Assertions.fail("Error shouldn't occur for this test case");
        }
    }

    @Test
    void testAcceptWithInvalidState() {
        String disputeId = createDispute(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK);
        try {
            final var thrown = Assertions.assertThrows(DisputeException.class,
                () ->  disputeManagementResource.
                acceptDispute(TestDataUtils.getOlympusUser(),
                    ResourceUtils.getMd5Hash(merchantId),
                    DisputeIdentifierParams.builder()
                        .merchantId(merchantId)
                        .disputeId(disputeId)
                    .build()));
            Assertions.assertEquals(StratosErrorCodeKey.TRANSITION_NOT_ALLOWED,
                    thrown.getErrorCode());
        } catch (final Exception e) {
            log.error("Error Occurred", e);
            Assertions.fail("Error shouldn't occur for this test case");
        }
    }

    @Test
    void testFullContest() {
        String disputeId = createDispute(DisputeWorkflowState.REFUND_BLOCKED);
        try {
            DisputeDetails details = disputeManagementResource.
                    contestDispute(TestDataUtils.getOlympusUser(),
                            ResourceUtils.getMd5Hash(merchantId),
                            FullContestPayload.builder()
                                    .merchantId(merchantId)
                                    .disputeId(disputeId)
                                    .build());
            Assertions.assertEquals(DisputeStatus.UNDER_REVIEW,details.getDisputeStatus());
            List<DisputeWorkflow> wfList = disputeWorkflowRepository.select(transactionId);
            Assertions.assertEquals(1,wfList.size());
            Assertions.assertEquals(DisputeWorkflowState.FULFILMENT_DOCUMENTS_RECEIVED,
                    wfList.get(0).getCurrentState());
            Assertions.assertEquals(0,
                    ((FinancialDisputeWorkflow)wfList.get(0)).getAcceptedAmount());
        } catch (final Exception e) {
            log.error("Error Occurred", e);
            Assertions.fail("Error shouldn't occur for this test case");
        }
    }

    @Test
    void testPartialContest() {
        String disputeId = createDispute(DisputeWorkflowState.REFUND_BLOCKED);
        long contestedAmount = 30;
        try {
            DisputeDetails details = disputeManagementResource.
                    contestDispute(TestDataUtils.getOlympusUser(),
                            ResourceUtils.getMd5Hash(merchantId),
                            PartialContestPayload.builder()
                                    .merchantId(merchantId)
                                    .disputeId(disputeId)
                                    .contestedAmount(contestedAmount)
                                    .build());
            Assertions.assertEquals(DisputeStatus.UNDER_REVIEW,details.getDisputeStatus());
            List<DisputeWorkflow> wfList = disputeWorkflowRepository.select(transactionId);
            Assertions.assertEquals(1,wfList.size());
            Assertions.assertEquals(DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED,
                    wfList.get(0).getCurrentState());
            Assertions.assertEquals(disputedAmount-contestedAmount,
                    ((FinancialDisputeWorkflow)wfList.get(0)).getAcceptedAmount());
        } catch (final Exception e) {
            log.error("Error Occurred", e);
            Assertions.fail("Error shouldn't occur for this test case");
        }
    }


    static Stream<Arguments> provideArgumentWithInvalidPaginationParams() {
        return Stream.of(
                Arguments.of(-1,0),
                Arguments.of(0,-1)
        );
    }

    private String createDispute(DisputeWorkflowState state){
        String disputeId = IdGenerator.generate("D").getId();
        String disputeWorkflowId = IdGenerator.generate("DW").getId();
        disputeWorkflowRepository.save(FinancialDisputeWorkflow.builder()
            .transactionReferenceId(transactionId)
            .raisedAt(LocalDateTime.now())
            .respondBy(LocalDateTime.now().plusDays(15))
            .key(StorageUtils.primaryKey())
            .gandalfUserId("user-1234")
            .disputeWorkflowVersion(DisputeWorkflowVersion.V1)
            .disputeWorkflowId(disputeWorkflowId)
            .userType(UserType.USER)
            .disputeType(DisputeType.UPI_CHARGEBACK)
            .disputeStage(DisputeStage.FIRST_LEVEL)
            .disputeSourceType(SourceType.FILE)
            .disputeSourceId("file-1234")
            .disputedAmount(disputedAmount)
            .currentState(state)
            .currentEvent(DisputeWorkflowEvent.BLOCK_REFUND)
            .disputeId(disputeId)
        .build());
        String disputeRefId = "reference-1234";
        disputeRepository.save(Dispute.builder()
            .currentDisputeStage(DisputeStage.FIRST_LEVEL)
            .disputeId(disputeId)
            .disputeReferenceId(disputeRefId)
            .disputeType(DisputeType.UPI_CHARGEBACK)
            .disputeIssuer(DisputeIssuer.YES_NPCI)
            .instrumentTransactionId("inst1234")
            .key(StorageUtils.primaryKey())
            .merchantId(merchantId)
            .merchantTransactionId("MTXN1234")
            .rrn("rrn1234")
            .disputeCategory(DisputeCategory.SERVICE_CHARGEBACK)
            .transactionAmount(100)
            .transactionReferenceId(transactionId)
        .build());
        return disputeId;
    }
}
