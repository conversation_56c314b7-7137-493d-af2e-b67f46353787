package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.udir;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeStageDto;
import com.phonepe.merchant.platform.stratos.models.disputes.UdirComplaintStateDto;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.models.udir.requests.UdirOutgoingComplaintStatusRequest;
import com.phonepe.merchant.platform.stratos.models.udir.requests.UdirOutgoingRaiseComplaintRequest;
import com.phonepe.merchant.platform.stratos.models.udir.response.UdirOutgoingComplaintStatusResponse;
import com.phonepe.merchant.platform.stratos.server.BaseTest;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeCategory;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.resources.DisputeResource;
import com.phonepe.merchant.platform.stratos.server.core.resources.UdirResource;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.merchant.platform.stratos.server.utils.ResourceUtils;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.payments.upiclientmodel.complaint.UPIClientOutgoingComplaintResponse;
import com.phonepe.payments.upiclientmodel.enums.ComplaintRequestType;
import com.phonepe.payments.upiclientmodel.enums.ComplaintState;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;

public class
UdirDrcComplaintWorkflowTest extends BaseTest {

    private UdirResource udirResource;
    private DisputeService disputeService;
    private DisputeResource disputeResource;
    private OlympusIMClient olympusIMClient;

    @BeforeEach
    void setUpGuiceInjection() {
        udirResource = guiceInjector.getInstance(UdirResource.class);
        disputeService = guiceInjector.getInstance(DisputeService.class);
        disputeResource = guiceInjector.getInstance(DisputeResource.class);
        olympusIMClient = guiceInjector.getInstance(OlympusIMClient.class);
        truncateDb();
    }

    @Test
    void testValidDrcTransaction() {
        var transactionId = IdGenerator.generate("T").getId();
        var complaintId = IdGenerator.generate("CS").getId();

        MockingUtils.setupPaymentDrcTransactionDetail(transactionId, 100);
        MockingUtils.setupPaymentsRaiseComplaintMocking();
        Mockito.doReturn(true).when(olympusIMClient).verifyPermission(Mockito.any(), Mockito.any());

        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        udirResource.raiseComplaint(UdirOutgoingRaiseComplaintRequest.builder()
            .paymentTransactionId(transactionId)
            .requestCode(ComplaintRequestType.COMPLAINT_REQ_P2P_DRC)
            .adjAmount(100)
            .disputeStage(DisputeStageDto.FIRST_LEVEL)
            .complaintId(complaintId)
            .build(), serviceUserPrincipal);

        var response1 = (UdirOutgoingComplaintStatusResponse) disputeResource.status(
            UdirOutgoingComplaintStatusRequest
                .builder()
                .complaintId(complaintId)
                .transactionId(transactionId)
                .build(), serviceUserPrincipal);

        Assertions.assertEquals(UdirComplaintStateDto.ACCEPTED, response1.getComplaintState());

        final var disputeWorkflow = disputeService.
            getDisputeWorkflow(transactionId, DisputeType.UDIR_OUTGOING_COMPLAINT,
                DisputeStage.FIRST_LEVEL);

        AssertionUtils.assertDisputeWorkflowStateEquals(transactionId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.RECEIVED);

        Assertions.assertEquals(DisputeCategory.UDIR_DRC,
            disputeWorkflow.getDispute().getDisputeCategory());

        udirResource.complaintCallback(UPIClientOutgoingComplaintResponse.builder()
            .complaintId(disputeWorkflow.getDisputeWorkflowId())
            .state(ComplaintState.INITIATED)
            .crn("1234")
            .build());

        var response2 = (UdirOutgoingComplaintStatusResponse) disputeResource.status(
            UdirOutgoingComplaintStatusRequest
                .builder()
                .complaintId(complaintId)
                .transactionId(transactionId)
                .build(),
                serviceUserPrincipal);

        Assertions.assertEquals(UdirComplaintStateDto.PENDING, response2.getComplaintState());
        Assertions.assertEquals("1234", response2.getCrn());

        AssertionUtils.assertDisputeWorkflowStateEquals(transactionId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.UDIR_COMPLAINT_ACCEPTED);

        udirResource.complaintCallback(UPIClientOutgoingComplaintResponse.builder()
            .complaintId(disputeWorkflow.getDisputeWorkflowId())
            .state(ComplaintState.COMPLETED)
            .crn("1234")
            .build());

        AssertionUtils.assertDisputeWorkflowStateEquals(transactionId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.UDIR_RESPONSE_RECEIVED);

        var response3 = (UdirOutgoingComplaintStatusResponse) disputeResource.status(
            UdirOutgoingComplaintStatusRequest
                .builder()
                .complaintId(complaintId)
                .transactionId(transactionId)
                .build(),
                serviceUserPrincipal);

        Assertions.assertEquals(UdirComplaintStateDto.COMPLETED, response3.getComplaintState());
        Assertions.assertEquals("1234", response3.getCrn());
    }

    @Test
    void testInvalidDrcTransaction() {
        var transactionId = IdGenerator.generate("T").getId();
        var complaintId = IdGenerator.generate("CS").getId();
        WireMock.stubFor(
            WireMock.get(WireMock.urlPathMatching(
                    String.format("/v2/transactions/transaction/%s/detail", transactionId)))
                .willReturn(WireMock.aResponse()
                    .withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON)
                    .withStatus(200)
                    .withBody(ResourceUtils.getResourceString(
                        "fixtures/disputeWorkflow/udir/invalidDrcTransactionDetailResponse.json"))));

        MockingUtils.setupPaymentsRaiseComplaintMocking();
        Mockito.doReturn(true).when(olympusIMClient).verifyPermission(Mockito.any(), Mockito.any());

        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        final var stratosError = Assertions.assertThrows(DisputeException.class,
            () -> udirResource.raiseComplaint(UdirOutgoingRaiseComplaintRequest.builder()
                .paymentTransactionId(transactionId)
                .requestCode(ComplaintRequestType.COMPLAINT_REQ_P2P_DRC)
                .adjAmount(100)
                .disputeStage(DisputeStageDto.FIRST_LEVEL)
                .complaintId(complaintId)
                .build(), serviceUserPrincipal));

        Assertions.assertEquals(StratosErrorCodeKey.INVALID_UDIR_COMPLAINT,
            stratosError.getErrorCode());
    }


}
