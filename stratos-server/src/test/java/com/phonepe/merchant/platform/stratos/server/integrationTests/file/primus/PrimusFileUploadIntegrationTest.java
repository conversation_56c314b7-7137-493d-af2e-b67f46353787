package com.phonepe.merchant.platform.stratos.server.integrationTests.file.primus;

import static com.phonepe.platform.schema.utils.SerDeUtils.getObjectMapper;

import com.fasterxml.jackson.databind.JsonNode;
import com.phonepe.merchant.platform.stratos.server.BaseTest;
import com.phonepe.merchant.platform.stratos.server.LoadOnlyOnClassLevelBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.models.primus.ApiClientCallbackRequest;
import com.phonepe.merchant.platform.stratos.server.core.models.primus.DestinationRequest;
import com.phonepe.merchant.platform.stratos.server.core.models.primus.EligibilityRequest;
import com.phonepe.merchant.platform.stratos.server.core.models.primus.FileRowStatus;
import com.phonepe.merchant.platform.stratos.server.core.models.primus.WorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.resources.PrimusResource;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import java.util.concurrent.TimeUnit;
import javax.ws.rs.core.Response;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class PrimusFileUploadIntegrationTest extends LoadOnlyOnClassLevelBaseTest {

    private PrimusResource primusResource;

    @BeforeEach
    void setUpGuiceInjection() {
        primusResource = guiceInjector.getInstance(PrimusResource.class);
        truncateDb();
    }

    @Test
    @SneakyThrows
    void primusFileUploadTest() {

        final var transactionReferenceId = IdGenerator.generate("P").getId();
        MockingUtils.setupPaymentsApiNoRefundExistsMocking(transactionReferenceId, 100, "YBL1234");

        EligibilityRequest eligibilityRequest = EligibilityRequest.builder()
            .fileId("test001")
            .fileName("testFile01")
            .rowCount(1)
            .configId("UPI_CHARGEBACK-YES")
            .build();
        Response actualResponse = primusResource.processPrimusFileSignal(eligibilityRequest);
        Assertions.assertEquals(200,actualResponse.getStatus());

        var content = getJsonStringForUPI("YBL123");

        DestinationRequest<JsonNode,JsonNode> destinationRequest =
            DestinationRequest.<JsonNode,JsonNode>builder()
                .fileId("test001")
                .key("key001")
                .status(FileRowStatus.SUCCESS)
                .request(getObjectMapper().readValue(content, JsonNode.class))
                .build();

        Response rowActualResponse = primusResource.processPrimusSignal(destinationRequest);

        ApiClientCallbackRequest apiClientCallbackRequest = ApiClientCallbackRequest.builder()
            .fileId("test001")
            .workflowState(WorkflowState.SUCCESS)
            .inputSourceRefId("WF001")
            .build();

        var eofResponse = primusResource.processPrimusEOFSignal(apiClientCallbackRequest);

        Assertions.assertEquals(202,rowActualResponse.getStatus());
        AssertionUtils.assertFileProcessed("test001", 20, TimeUnit.SECONDS);
        Assertions.assertEquals(200, eofResponse.getStatus());

    }

    @Test
    @SneakyThrows
    void primusFileUploadFailedTest() {

        final var transactionReferenceId = IdGenerator.generate("P").getId();
        MockingUtils.setupPaymentsApiNoRefundExistsMocking(transactionReferenceId, 100, "YBL1234");

        EligibilityRequest eligibilityRequest = EligibilityRequest.builder()
            .fileId("test002")
            .fileName("testFile02")
            .rowCount(2)
            .configId("UPI_CHARGEBACK-YES")
            .build();
        Response actualResponse = primusResource.processPrimusFileSignal(eligibilityRequest);
        Assertions.assertEquals(200,actualResponse.getStatus());

        var content = getJsonStringForUPI("YBL123");

        DestinationRequest<JsonNode,JsonNode> destinationRequest =
            DestinationRequest.<JsonNode,JsonNode>builder()
                .fileId("test002")
                .key("key002")
                .status(FileRowStatus.SUCCESS)
                .request(getObjectMapper().readValue(content, JsonNode.class))
                .build();

        Response rowActualResponse = primusResource.processPrimusSignal(destinationRequest);

        ApiClientCallbackRequest apiClientCallbackRequest = ApiClientCallbackRequest.builder()
            .fileId("test002")
            .workflowState(WorkflowState.FAILURE)
            .inputSourceRefId("WF002")
            .build();

        var eofResponse = primusResource.processPrimusEOFSignal(apiClientCallbackRequest);

        Assertions.assertEquals(202,rowActualResponse.getStatus());
        Assertions.assertTrue(AssertionUtils.assertFileProcessing("test002"));
        Assertions.assertEquals(200, eofResponse.getStatus());

    }

    @Test
    @SneakyThrows
    void primusRetryFileUploadTest() {

        final var transactionReferenceId1 = IdGenerator.generate("P").getId();
        MockingUtils.setupPaymentsApiNoRefundExistsMocking(transactionReferenceId1, 100, "YBL123");

        final var transactionReferenceId2 = IdGenerator.generate("P").getId();
        MockingUtils.setupPaymentsApiNoRefundExistsMocking(transactionReferenceId2, 100, "YBL345");

        EligibilityRequest eligibilityRequest = EligibilityRequest.builder()
            .fileId("test003")
            .fileName("testFile03")
            .rowCount(2)
            .configId("UPI_CHARGEBACK-YES")
            .build();

        Response response1 = primusResource.processPrimusFileSignal(eligibilityRequest);

        Assertions.assertEquals(200, response1.getStatus());

        var content = getJsonStringForUPI("YBL123");

        DestinationRequest<JsonNode,JsonNode> destinationRequest1 =
            DestinationRequest.<JsonNode,JsonNode>builder()
                .fileId("test003")
                .key("key003")
                .status(FileRowStatus.SUCCESS)
                .request(getObjectMapper().readValue(content, JsonNode.class))
                .build();

        Response rowActualResponse1 = primusResource.processPrimusSignal(destinationRequest1);
        Assertions.assertEquals(202,rowActualResponse1.getStatus());

        ApiClientCallbackRequest apiClientCallbackRequest1 = ApiClientCallbackRequest.builder()
            .fileId("test003")
            .workflowState(WorkflowState.FAILURE)
            .inputSourceRefId("WF003")
            .build();

        var eofResponse1 = primusResource.processPrimusEOFSignal(apiClientCallbackRequest1);

        Assertions.assertTrue(AssertionUtils.assertFileProcessing("test003"));
        Assertions.assertEquals(200, eofResponse1.getStatus());

        Response response2= primusResource.processPrimusFileSignal(eligibilityRequest);
        Assertions.assertEquals(200, response2.getStatus());

        content = getJsonStringForUPI("YBL345");

        DestinationRequest<JsonNode,JsonNode> destinationRequest2 =
            DestinationRequest.<JsonNode,JsonNode>builder()
                .fileId("test003")
                .key("key003")
                .status(FileRowStatus.SUCCESS)
                .request(getObjectMapper().readValue(content, JsonNode.class))
                .build();

        Response rowActualResponse2 = primusResource.processPrimusSignal(destinationRequest2);
        Assertions.assertEquals(202,rowActualResponse2.getStatus());

        ApiClientCallbackRequest apiClientCallbackRequest2 = ApiClientCallbackRequest.builder()
            .fileId("test003")
            .workflowState(WorkflowState.SUCCESS)
            .inputSourceRefId("WF003")
            .build();

        var eofResponse2 = primusResource.processPrimusEOFSignal(apiClientCallbackRequest2);

        Assertions.assertEquals(200, eofResponse2.getStatus());

    }

    @Test
    @SneakyThrows
    void primusInvalidFileIdTest() {

        final var transactionReferenceId = IdGenerator.generate("P").getId();
        MockingUtils.setupPaymentsApiNoRefundExistsMocking(transactionReferenceId, 100, "YBL1234");

        EligibilityRequest eligibilityRequest = EligibilityRequest.builder()
            .fileId("test004")
            .fileName("testFile04")
            .rowCount(1)
            .configId("UPI_CHARGEBACK-YES")
            .build();
        Response actualResponse = primusResource.processPrimusFileSignal(eligibilityRequest);
        Assertions.assertEquals(200,actualResponse.getStatus());

        var content = getJsonStringForUPI("YBL123");

        DestinationRequest<JsonNode,JsonNode> destinationRequest =
            DestinationRequest.<JsonNode,JsonNode>builder()
                .fileId("test000")
                .key("key004")
                .status(FileRowStatus.SUCCESS)
                .request(getObjectMapper().readValue(content, JsonNode.class))
                .build();

        Response rowResponse = primusResource.processPrimusSignal(destinationRequest);
        Assertions.assertEquals(400, rowResponse.getStatus());

    }

    @Test
    void primusWorkflowStatesTest() {
        final var transactionReferenceId = IdGenerator.generate("P").getId();
        MockingUtils.setupPaymentsApiNoRefundExistsMocking(transactionReferenceId, 100, "YBL1234");

        EligibilityRequest eligibilityRequest = EligibilityRequest.builder()
            .fileId("test005")
            .fileName("testFile05")
            .rowCount(1)
            .configId("UPI_CHARGEBACK-YES")
            .build();
        Response actualResponse = primusResource.processPrimusFileSignal(eligibilityRequest);
        Assertions.assertEquals(200,actualResponse.getStatus());

        ApiClientCallbackRequest apiClientCallbackRequest = ApiClientCallbackRequest.builder()
            .fileId("test005")
            .workflowState(WorkflowState.SUCCESS)
            .inputSourceRefId("WF005")
            .build();

        primusResource.processPrimusEOFSignal(apiClientCallbackRequest);

        apiClientCallbackRequest.setWorkflowState(WorkflowState.FAILURE);
        primusResource.processPrimusEOFSignal(apiClientCallbackRequest);

        apiClientCallbackRequest.setWorkflowState(WorkflowState.ERROR);
        primusResource.processPrimusEOFSignal(apiClientCallbackRequest);

        apiClientCallbackRequest.setWorkflowState(WorkflowState.PENDING);
        primusResource.processPrimusEOFSignal(apiClientCallbackRequest);

        apiClientCallbackRequest.setWorkflowState(WorkflowState.PENDING_RETRY);
        primusResource.processPrimusEOFSignal(apiClientCallbackRequest);

        apiClientCallbackRequest.setWorkflowState(WorkflowState.BLOCKED);
        primusResource.processPrimusEOFSignal(apiClientCallbackRequest);

        apiClientCallbackRequest.setWorkflowState(WorkflowState.COMPUTE_END_STATE);
        primusResource.processPrimusEOFSignal(apiClientCallbackRequest);

        apiClientCallbackRequest.setWorkflowState(WorkflowState.ELIGIBILITY_STATE);
        primusResource.processPrimusEOFSignal(apiClientCallbackRequest);

    }

    @Test
    void primusBadRequestTest() {

        EligibilityRequest eligibilityRequest = EligibilityRequest.builder()
            .fileId("test006")
            .fileName("testFile06")
            .rowCount(1)
            .configId("UPI_CHARGEBACK_YES")
            .build();
        Response actualResponse = primusResource.processPrimusFileSignal(eligibilityRequest);
        Assertions.assertEquals(400,actualResponse.getStatus());
    }

    private String getJsonStringForUPI(String upiTxnId)  {

        String upiContent = "{\"Adjdate\": \"2023-09-24\","
            + "\"Txndate\": \"2023-09-24\","
            + "\"Adjtype\": \"Chargeback Raise\","
            + "\"Remitter\": \"YES\","
            + "\"Beneficiery\":\"YES\","
            + "\"RRN\": \"RRN2345\","
            + "\"Txnamount\": 1.0,"
            + "\"Adjamount\": 0.5,"
            + "\"Compensation amount\": 0.0,"
            + "\"UPI Transaction ID\" : " + "\"" + upiTxnId + "\"}";

        return upiContent;
    }
}