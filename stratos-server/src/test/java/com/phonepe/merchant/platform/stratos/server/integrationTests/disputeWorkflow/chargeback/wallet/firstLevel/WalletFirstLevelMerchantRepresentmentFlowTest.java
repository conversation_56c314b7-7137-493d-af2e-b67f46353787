package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.wallet.firstLevel;

import com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeCategory;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.clients.PaymentsTxnlClient;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.impls.WalletTransactionService;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import java.time.LocalDateTime;
import java.util.Set;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class WalletFirstLevelMerchantRepresentmentFlowTest extends WalletFirstLevelBaseTest {

    @BeforeEach
    void initEach(){
        truncateDb();
        DisputeWorkflowRepository disputeWorkflowRepository = guiceInjector.getInstance(
            DisputeWorkflowRepository.class);
        DisputeRepository disputeRepository = guiceInjector.getInstance(DisputeRepository.class);
        disputeRepository.save(dispute);
        disputeWorkflow.setDisputeWorkflowVersion(DisputeWorkflowVersion.V1);
        disputeWorkflowRepository.save(disputeWorkflow);
        createEntrySetupForAPI("WALLET", String.valueOf(LocalDateTime.now()),
            dispute.getTransactionReferenceId());
        MockingUtils.setupEventIngestionApiMocking();
    }

    @Test
    void testNpciAckToMerchantFullRepresentment() {
        assertFromCreateToNpciAck(Set.of(DisputeWorkflowEvent.COMPLETE_NPCI_REPRESENTMENT,
            DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK));

        assertFromCreateToRejected();
    }
}
