package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.netbanking.firstLevel;

import com.phonepe.merchant.platform.stratos.server.core.models.DisputeCategory;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;


public class NBFirstLevelChargebackFullRefundExistsFlowTest extends NBFirstLevelBaseTest {

    @ParameterizedTest(name = "{0}")
    @MethodSource("provideArgumentWithChargebackCategories")
    void testFullRefundExistsFlow(DisputeCategory disputeCategory) {

        testTillRepresentmentRequirement();

        assertTriggerEvent(DisputeWorkflowEvent.COMPLETE_REPRESENTMENT,
            DisputeWorkflowState.REPRESENTMENT_COMPLETED);
    }
}
