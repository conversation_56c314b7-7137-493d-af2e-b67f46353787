package com.phonepe.merchant.platform.stratos.server.integrationTests.evidencemanagement;

import com.phonepe.merchant.platform.stratos.models.disputemanagement.EvidenceStatusDto;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.requests.EvidenceFilterParams;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.requests.EvidenceIdentifierParams;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.requests.EvidenceUploadPayload;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.responses.EvidenceDetail;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.responses.EvidenceResponse;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.BaseTest;
import com.phonepe.merchant.platform.stratos.server.LoadOnlyOnClassLevelBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeCategory;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeIssuer;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.models.SourceType;
import com.phonepe.merchant.platform.stratos.server.core.models.UserType;
import com.phonepe.merchant.platform.stratos.server.core.resources.EvidenceManagementResource;
import com.phonepe.merchant.platform.stratos.server.core.utils.StorageUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.merchant.platform.stratos.server.utils.ResourceUtils;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.glassfish.jersey.media.multipart.FormDataBodyPart;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mockito;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

@Slf4j
public class EvidenceManagementResourceTest extends LoadOnlyOnClassLevelBaseTest {
    private EvidenceManagementResource evidenceManagementResource;
    private final String merchantId = "TestMerchant";
    private DisputeWorkflowRepository disputeWorkflowRepository;
    private DisputeRepository disputeRepository;


    @BeforeEach
    void setUpGuiceInjection() {
        evidenceManagementResource = guiceInjector.getInstance(EvidenceManagementResource.class);
        disputeWorkflowRepository = guiceInjector.getInstance(DisputeWorkflowRepository.class);
        disputeRepository = guiceInjector.getInstance(DisputeRepository.class);
        truncateDb();
    }

    @Test
    void testUpload() {
        String disputeId = createDispute(DisputeWorkflowState.REFUND_BLOCKED);
        FormDataContentDisposition contentDisposition = FormDataContentDisposition
                .name("randomName123").fileName("file123.csv")
                .build();
        EvidenceUploadPayload payload = EvidenceUploadPayload.builder()
                .disputeId(disputeId)
                .merchantId(merchantId)
                .metadata(Map.of("key1","value1","key2","value2"))
                .build();
        FormDataBodyPart formDataBodyPart = Mockito.mock(FormDataBodyPart.class);
        Mockito.when(formDataBodyPart.getValueAs(Mockito.any()))
                .thenReturn(payload);
        MockingUtils.setupDocstoreUploadPublicApiMocking();
        try {
            EvidenceDetail response = evidenceManagementResource.
                    uploadEvidence(TestDataUtils.getOlympusUser(),
                            ResourceUtils.getMd5Hash(merchantId),
                            formDataBodyPart, contentDisposition,
                            InputStream.nullInputStream());
            Assertions.assertNotNull(response.getEvidenceId());
            Assertions.assertEquals(2,response.getMetadata().size());
            Assertions.assertEquals("value1",response.getMetadata().get("key1"));
            Assertions.assertEquals("value2",response.getMetadata().get("key2"));

        } catch (final Exception e) {
            log.error("Error Occurred", e);
            Assertions.fail("Error shouldn't occur for this test case");
        }
    }

    @Test
    void testListAll() {
        String disputeId = createDispute(DisputeWorkflowState.REFUND_BLOCKED);
        FormDataContentDisposition contentDisposition = FormDataContentDisposition
                .name("randomName123").fileName("file123.csv")
                .build();
        EvidenceUploadPayload payload = EvidenceUploadPayload.builder()
                .disputeId(disputeId)
                .merchantId(merchantId)
                .build();
        FormDataBodyPart formDataBodyPart = Mockito.mock(FormDataBodyPart.class);
        Mockito.when(formDataBodyPart.getValueAs(Mockito.any()))
                .thenReturn(payload);
        MockingUtils.setupDocstoreUploadPublicApiMocking();
        try {
            evidenceManagementResource.
                    uploadEvidence(TestDataUtils.getOlympusUser(),
                            ResourceUtils.getMd5Hash(merchantId),
                            formDataBodyPart, contentDisposition,
                            InputStream.nullInputStream());
            evidenceManagementResource.
                    uploadEvidence(TestDataUtils.getOlympusUser(),
                            ResourceUtils.getMd5Hash(merchantId),
                            formDataBodyPart, contentDisposition,
                            InputStream.nullInputStream());
            EvidenceResponse response = evidenceManagementResource.listEvidence(TestDataUtils.getOlympusUser(),
                    ResourceUtils.getMd5Hash(merchantId),
                    EvidenceFilterParams.builder()
                            .merchantId(merchantId)
                            .disputeId(disputeId)
                            .build());
            Assertions.assertEquals(2, response.getEvidenceList().size());

        } catch (final Exception e) {
            log.error("Error Occurred", e);
            Assertions.fail("Error shouldn't occur for this test case");
        }
    }

    @Test
    void testListAllWithUnderReviewState() {
        String disputeId = createDispute(DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED);
        FormDataContentDisposition contentDisposition = FormDataContentDisposition
                .name("randomName123").fileName("file123.csv")
                .build();
        EvidenceUploadPayload payload = EvidenceUploadPayload.builder()
                .disputeId(disputeId)
                .merchantId(merchantId)
                .build();
        FormDataBodyPart formDataBodyPart = Mockito.mock(FormDataBodyPart.class);
        Mockito.when(formDataBodyPart.getValueAs(Mockito.any()))
                .thenReturn(payload);
        MockingUtils.setupDocstoreUploadPublicApiMocking();
        try {
            EvidenceResponse response = evidenceManagementResource.listEvidence(TestDataUtils.getOlympusUser(),
                    ResourceUtils.getMd5Hash(merchantId),
                    EvidenceFilterParams.builder()
                            .merchantId(merchantId)
                            .disputeId(disputeId)
                            .build());
            Assertions.assertEquals(0, response.getEvidenceList().size());

        } catch (final Exception e) {
            log.error("Error Occurred", e);
            Assertions.fail("Error shouldn't occur for this test case");
        }
    }

    @Test
    void testDeleteWhenDisputeNotFound() {
        try {
            final var thrown = Assertions.assertThrows(DisputeException.class,
                    () -> evidenceManagementResource.
                            deleteEvidence(TestDataUtils.getOlympusUser(),
                                    ResourceUtils.getMd5Hash(merchantId),
                                    EvidenceIdentifierParams.builder()
                                            .merchantId(merchantId)
                                            .disputeId("disputeId")
                                            .evidenceId("evidenceId")
                                            .build()));
            Assertions.assertEquals(StratosErrorCodeKey.DISPUTE_NOT_FOUND,
                    thrown.getErrorCode());
        } catch (final Exception e) {
            log.error("Error Occurred", e);
            Assertions.fail("Error shouldn't occur for this test case");
        }
    }

    @Test
    void testDelete() {
        String disputeId = createDispute(DisputeWorkflowState.REFUND_BLOCKED);
        FormDataContentDisposition contentDisposition = FormDataContentDisposition
                .name("randomName123").fileName("file123.csv")
                .build();
        EvidenceUploadPayload payload = EvidenceUploadPayload.builder()
                .disputeId(disputeId)
                .merchantId(merchantId)
                .build();
        FormDataBodyPart formDataBodyPart = Mockito.mock(FormDataBodyPart.class);
        Mockito.when(formDataBodyPart.getValueAs(Mockito.any()))
                .thenReturn(payload);
        MockingUtils.setupDocstoreUploadPublicApiMocking();
        try {
            EvidenceDetail evidenceDetailCreated = evidenceManagementResource.
                    uploadEvidence(TestDataUtils.getOlympusUser(),
                            ResourceUtils.getMd5Hash(merchantId),
                            formDataBodyPart, contentDisposition,
                            InputStream.nullInputStream());
            Assertions.assertEquals(EvidenceStatusDto.ACTIVE, evidenceDetailCreated.getStatus());
            EvidenceDetail evidenceDetail = evidenceManagementResource.
                    deleteEvidence(TestDataUtils.getOlympusUser(),
                            ResourceUtils.getMd5Hash(merchantId),
                            EvidenceIdentifierParams.builder()
                                    .merchantId(merchantId)
                                    .disputeId(disputeId)
                                    .evidenceId(evidenceDetailCreated.getEvidenceId())
                                    .build());
            Assertions.assertEquals(EvidenceStatusDto.INACTIVE, evidenceDetail.getStatus());
        } catch (final Exception e) {
            log.error("Error Occurred", e);
            Assertions.fail("Error shouldn't occur for this test case");
        }
    }

    @Test
    void testListAllForDeletedEvidences() {
        String disputeId = createDispute(DisputeWorkflowState.REFUND_BLOCKED);
        FormDataContentDisposition contentDisposition = FormDataContentDisposition
                .name("randomName123").fileName("file123.csv")
                .build();
        EvidenceUploadPayload payload = EvidenceUploadPayload.builder()
                .disputeId(disputeId)
                .merchantId(merchantId)
                .build();
        FormDataBodyPart formDataBodyPart = Mockito.mock(FormDataBodyPart.class);
        Mockito.when(formDataBodyPart.getValueAs(Mockito.any()))
                .thenReturn(payload);
        MockingUtils.setupDocstoreUploadPublicApiMocking();
        try {
            EvidenceDetail evidenceDetailCreated1 = evidenceManagementResource.
                    uploadEvidence(TestDataUtils.getOlympusUser(),
                            ResourceUtils.getMd5Hash(merchantId),
                            formDataBodyPart, contentDisposition,
                            InputStream.nullInputStream());
            evidenceManagementResource.
                    uploadEvidence(TestDataUtils.getOlympusUser(),
                            ResourceUtils.getMd5Hash(merchantId),
                            formDataBodyPart, contentDisposition,
                            InputStream.nullInputStream());
            evidenceManagementResource.
                deleteEvidence(TestDataUtils.getOlympusUser(),
                    ResourceUtils.getMd5Hash(merchantId),
                    EvidenceIdentifierParams.builder()
                        .merchantId(merchantId)
                        .disputeId(disputeId)
                        .evidenceId(evidenceDetailCreated1.getEvidenceId())
                        .build());
            EvidenceResponse response = evidenceManagementResource.listEvidence(TestDataUtils.getOlympusUser(),
                ResourceUtils.getMd5Hash(merchantId),
                EvidenceFilterParams.builder()
                    .merchantId(merchantId)
                    .disputeId(disputeId)
                    .state(EvidenceStatusDto.INACTIVE)
                    .build());
            Assertions.assertEquals(1, response.getEvidenceList().size());
            Assertions.assertEquals(evidenceDetailCreated1.getEvidenceId(), response.getEvidenceList().get(0).getEvidenceId());
        } catch (final Exception e) {
            log.error("Error Occurred", e);
            Assertions.fail("Error shouldn't occur for this test case");
        }
    }

    @Test
    void testListAllForInvalidWorkFlowId() {
        String disputeId = createDispute(DisputeWorkflowState.REFUND_BLOCKED);
        FormDataContentDisposition contentDisposition = FormDataContentDisposition
                .name("randomName123").fileName("file123.csv")
                .build();
        EvidenceUploadPayload payload = EvidenceUploadPayload.builder()
                .disputeId(disputeId)
                .merchantId(merchantId)
                .build();
        FormDataBodyPart formDataBodyPart = Mockito.mock(FormDataBodyPart.class);
        Mockito.when(formDataBodyPart.getValueAs(Mockito.any()))
                .thenReturn(payload);
        MockingUtils.setupDocstoreUploadPublicApiMocking();
        try {
            final var thrown = Assertions.assertThrows(DisputeException.class,
                    () -> evidenceManagementResource.listEvidence(TestDataUtils.getOlympusUser(),
                    ResourceUtils.getMd5Hash(merchantId),
                    EvidenceFilterParams.builder()
                            .merchantId(merchantId)
                            .disputeId(disputeId)
                            .disputeWorkflowIds(Arrays.asList("dw1","dw2"))
                            .state(EvidenceStatusDto.INACTIVE)
                            .build()));
            Assertions.assertEquals(StratosErrorCodeKey.DISPUTE_WORKFLOW_NOT_FOUND, thrown.getErrorCode());
        } catch (final Exception e) {
            log.error("Error Occurred", e);
            Assertions.fail("Error shouldn't occur for this test case");
        }
    }

    @Test
    void testListAllWFfilter() {
        String disputeId = createDispute(DisputeWorkflowState.REFUND_BLOCKED);
        FormDataContentDisposition contentDisposition = FormDataContentDisposition
                .name("randomName123").fileName("file123.csv")
                .build();
        EvidenceUploadPayload payload = EvidenceUploadPayload.builder()
                .disputeId(disputeId)
                .merchantId(merchantId)
                .build();
        FormDataBodyPart formDataBodyPart = Mockito.mock(FormDataBodyPart.class);
        Mockito.when(formDataBodyPart.getValueAs(Mockito.any()))
                .thenReturn(payload);
        MockingUtils.setupDocstoreUploadPublicApiMocking();
        try {
            EvidenceDetail evidenceDetail1 = evidenceManagementResource.
                    uploadEvidence(TestDataUtils.getOlympusUser(),
                            ResourceUtils.getMd5Hash(merchantId),
                            formDataBodyPart, contentDisposition,
                            InputStream.nullInputStream());
            String dwId = addDisputeWorkflow(disputeId);
            EvidenceDetail evidenceDetail2 = evidenceManagementResource.
                    uploadEvidence(TestDataUtils.getOlympusUser(),
                            ResourceUtils.getMd5Hash(merchantId),
                            formDataBodyPart, contentDisposition,
                            InputStream.nullInputStream());

            EvidenceResponse response = evidenceManagementResource.listEvidence(TestDataUtils.getOlympusUser(),
                    ResourceUtils.getMd5Hash(merchantId),
                    EvidenceFilterParams.builder()
                            .merchantId(merchantId)
                            .disputeId(disputeId)
                            .build());
            Assertions.assertEquals(2, response.getEvidenceList().size());
            List<String> eId = response.getEvidenceList().stream().map(EvidenceDetail::getEvidenceId).toList();
            Assertions.assertTrue(eId.containsAll(Arrays.asList(evidenceDetail1.getEvidenceId(),evidenceDetail2.getEvidenceId())));

            EvidenceResponse responseDwFilter = evidenceManagementResource.listEvidence(TestDataUtils.getOlympusUser(),
                    ResourceUtils.getMd5Hash(merchantId),
                    EvidenceFilterParams.builder()
                            .merchantId(merchantId)
                            .disputeId(disputeId)
                            .disputeWorkflowIds(Collections.singletonList(dwId))
                            .build());
            Assertions.assertEquals(1, responseDwFilter.getEvidenceList().size());
            Assertions.assertEquals(evidenceDetail2.getEvidenceId(), responseDwFilter.getEvidenceList().get(0).getEvidenceId());
        } catch (final Exception e) {
            log.error("Error Occurred", e);
            Assertions.fail("Error shouldn't occur for this test case");
        }
    }

    private String addDisputeWorkflow(String disputeId) {
        String disputeWorkflowId = IdGenerator.generate("DW").getId();
        disputeWorkflowRepository.save(FinancialDisputeWorkflow.builder()
                .transactionReferenceId("TXN1234")
                .raisedAt(LocalDateTime.now())
                .respondBy(LocalDateTime.now().plusDays(15))
                .key(StorageUtils.primaryKey())
                .gandalfUserId("user-1234")
                .disputeWorkflowVersion(DisputeWorkflowVersion.V1)
                .disputeWorkflowId(disputeWorkflowId)
                .userType(UserType.USER)
                .disputeType(DisputeType.UPI_CHARGEBACK)
                .disputeStage(DisputeStage.FIRST_LEVEL)
                .disputeSourceType(SourceType.FILE)
                .disputeSourceId("file-1234")
                .disputedAmount(100)
                .currentState(DisputeWorkflowState.REFUND_BLOCKED)
                .currentEvent(DisputeWorkflowEvent.BLOCK_REFUND)
                .disputeId(disputeId)
                .build());
        return disputeWorkflowId;
    }

    private String createDispute(DisputeWorkflowState state) {
        String disputeId = IdGenerator.generate("D").getId();
        String disputeWorkflowId = IdGenerator.generate("DW").getId();
        disputeWorkflowRepository.save(FinancialDisputeWorkflow.builder()
                .transactionReferenceId("TXN1234")
                .raisedAt(LocalDateTime.now())
                .respondBy(LocalDateTime.now().plusDays(15))
                .key(StorageUtils.primaryKey())
                .gandalfUserId("user-1234")
                .disputeWorkflowVersion(DisputeWorkflowVersion.V1)
                .disputeWorkflowId(disputeWorkflowId)
                .userType(UserType.USER)
                .disputeType(DisputeType.UPI_CHARGEBACK)
                .disputeStage(DisputeStage.FIRST_LEVEL)
                .disputeSourceType(SourceType.FILE)
                .disputeSourceId("file-1234")
                .disputedAmount(100)
                .currentState(state)
                .currentEvent(DisputeWorkflowEvent.BLOCK_REFUND)
                .disputeId(disputeId)
                .build());
        disputeRepository.save(Dispute.builder()
                .currentDisputeStage(DisputeStage.FIRST_LEVEL)
                .disputeId(disputeId)
                .disputeReferenceId("reference-1234")
                .disputeType(DisputeType.UPI_CHARGEBACK)
                .disputeIssuer(DisputeIssuer.YES_NPCI)
                .instrumentTransactionId("inst1234")
                .key(StorageUtils.primaryKey())
                .merchantId(merchantId)
                .merchantTransactionId("MTXN1234")
                .rrn("rrn1234")
                .disputeCategory(DisputeCategory.SERVICE_CHARGEBACK)
                .transactionAmount(100)
                .transactionReferenceId("TXN1234")
                .build());
        return disputeId;
    }

}
