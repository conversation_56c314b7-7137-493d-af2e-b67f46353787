package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.netbanking.firstLevel;

import com.phonepe.merchant.platform.stratos.server.core.clients.kratos.KratosRecommendedAction;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.services.refund.orchestrator.models.RefundStatus;
import java.util.concurrent.TimeUnit;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

public class NBFirstLevelMerchantAcceptedRecoveryFlowTest extends NBFirstLevelBaseTest {

    @SneakyThrows
    @ParameterizedTest
    @EnumSource(names = {"NOOP", "ALLOW"})
    void testMerchantAcceptedChargeback(KratosRecommendedAction kratosRecommendedAction) {

        createAndTestTillMerchantAcceptedChargeback(kratosRecommendedAction);

        testTillRefundInitiated(RefundStatus.ACCEPTED);

        triggerCallBackAndAssertStatus(0);

        testRecoveryFlow();
    }



    @ParameterizedTest
    @EnumSource(names = {"NOOP", "ALLOW"})
    void testRetriggerAlreadyRefundExistsSuccess(KratosRecommendedAction kratosRecommendedAction) {

        String merchantTxnId = String.format("CB%s-%d", disputeWorkflow.getTransactionReferenceId(),
            0);
        testTillRefundFailure(kratosRecommendedAction);
        MockingUtils.setUpRefundResponseMocking(RefundStatus.INITIATED.name(),
            disputeWorkflow.getDisputedAmount());
        MockingUtils.setUpRefundStatusMocking(RefundStatus.FAILED.name(),
            disputeWorkflow.getDisputedAmount(), dispute.getMerchantId(), merchantTxnId);

        netBankingChargebackServiceImpl.saveDisputeMeta(disputeWorkflow, merchantTxnId);
        clearAerospike();
        assertTriggerEventWithDelay(
            DisputeWorkflowEvent.INITIATE_CHARGEBACK_REFUND,
            DisputeWorkflowState.CB_REFUND_INITIATED,
            Constants.EMPTY_TRANSITION_CONTEXT, 25L
        );
        triggerCallBackAndAssertStatus(0);

        testRecoveryFlow();

    }

    @ParameterizedTest
    @EnumSource(names = {"NOOP", "ALLOW"})
    void testRetriggerWhenPreviousRefundInAcceptedState(KratosRecommendedAction kratosRecommendedAction) {

        String merchantTxnId = String.format("CB%s-%d", disputeWorkflow.getTransactionReferenceId(),
            0);

        testTillRefundFailure(kratosRecommendedAction);

        netBankingChargebackServiceImpl.saveDisputeMeta(disputeWorkflow, merchantTxnId);

        MockingUtils.setUpRefundStatusMocking(RefundStatus.ACCEPTED.name(),
            disputeWorkflow.getDisputedAmount(), dispute.getMerchantId(), merchantTxnId);

        clearAerospike();

        assertTriggerEventWithDelay(DisputeWorkflowEvent.INITIATE_CHARGEBACK_REFUND, DisputeWorkflowState.CB_REFUND_ACCEPTED,
            Constants.EMPTY_TRANSITION_CONTEXT, 20L);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            disputeWorkflow.getTransactionReferenceId(),
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.CB_REFUND_ACCEPTED,
            20L, TimeUnit.SECONDS);

        testRecoveryFlow();
    }

    @ParameterizedTest
    @EnumSource(names = {"NOOP", "ALLOW"})
    void testRetriggerFailure(KratosRecommendedAction kratosRecommendedAction) {

        String merchantTxnId = String.format("CB%s-%d", disputeWorkflow.getTransactionReferenceId(),
            0);

        testTillRefundFailure(kratosRecommendedAction);

        netBankingChargebackServiceImpl.saveDisputeMeta(disputeWorkflow, merchantTxnId);

        MockingUtils.setUpRefundStatusMocking(RefundStatus.INITIATED.name(),
            disputeWorkflow.getDisputedAmount(), dispute.getMerchantId(), merchantTxnId);

        Assertions.assertThrows(DisputeException.class, () ->
            disputeResource.triggerEvent(serviceUserPrincipal,
                disputeWorkflow.getTransactionReferenceId(),
                disputeWorkflow.getDisputeWorkflowId(),
                DisputeWorkflowEvent.INITIATE_CHARGEBACK_REFUND,
                Constants.EMPTY_TRANSITION_CONTEXT));

    }

}
