package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.netbanking.firstLevel.basetest;

import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.services.refund.orchestrator.models.RefundStatus;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
public class NBRetriggerFirstLevelPartialAcceptanceFlowTest extends NBFirstLevelBaseTest {

    @Test
    void reTriggerPartialRefundSuccess() {

        testTillPartialRefundFailure();

        MockingUtils.setUpRefundResponseMocking(RefundStatus.ACCEPTED.name(),
                disputeWorkflow.getDisputedAmount() - 50L);

        clearAerospike();
        assertTriggerEventWithDelay(
                DisputeWorkflowEvent.INITIATE_CHARGEBACK_REFUND,
                DisputeWorkflowState.CB_REFUND_INITIATED,
                Constants.EMPTY_TRANSITION_CONTEXT, 30L);

        triggerCallBackAndAssertStatus(0);

        testRecoveryFlow();

    }



}
