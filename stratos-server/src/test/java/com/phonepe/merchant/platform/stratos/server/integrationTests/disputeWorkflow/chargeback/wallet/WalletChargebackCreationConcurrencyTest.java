package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.wallet;

import com.phonepe.merchant.platform.stratos.models.commons.TransactionType;
import com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeCategory;
import com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeStage;
import com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.DisputeCreationCommand;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.keys.DisputeCreationLockKey;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import io.dropwizard.testing.junit5.DropwizardExtensionsSupport;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(DropwizardExtensionsSupport.class)
class WalletChargebackCreationConcurrencyTest  extends WalletBaseTest{
    String USER_ID = "USER_ID";
    private DisputeCreationCommand disputeCreationCommand;

    @BeforeEach
    void setup() {
        disputeCreationCommand = guiceInjector.getInstance(DisputeCreationCommand.class);
        var lockKey = DisputeCreationLockKey.builder()
            .transactionType(TransactionType.WALLET)
            .disputeStage(DisputeStage.FIRST_LEVEL)
            .disputeType(DisputeType.CHARGEBACK)
            .transactionId(dispute.getTransactionReferenceId())
            .build();
        disputeCreationCommand.delete(lockKey);
        truncateDb();
    }

    @Test
    void createDisputeFlowTest() throws IOException, InterruptedException {

        final int CONCURRENT_REQUESTS = 2;
        final ExecutorService executor = Executors.newFixedThreadPool(CONCURRENT_REQUESTS);
        final CountDownLatch latch = new CountDownLatch(CONCURRENT_REQUESTS);
        final List<Throwable> exceptions = Collections.synchronizedList(new ArrayList<>());
        final AtomicInteger successCount = new AtomicInteger(0);


        var lockKey = DisputeCreationLockKey.builder()
            .transactionType(TransactionType.WALLET)
            .disputeStage(DisputeStage.FIRST_LEVEL)
            .disputeType(DisputeType.CHARGEBACK)
            .transactionId(dispute.getTransactionReferenceId())
            .build();
        createEntrySetupForAPI("WALLET", String.valueOf(LocalDateTime.now()), dispute.getTransactionReferenceId());

        var request = TestDataUtils.getWalletDisputeRequest(DisputeStage.FIRST_LEVEL, DisputeCategory.SERVICE, dispute.getTransactionReferenceId(),
            (int) dispute.getTransactionAmount());
        for (int concurrent_request = 0; concurrent_request < CONCURRENT_REQUESTS; concurrent_request++) {
            executor.submit(() -> {
                try {
                    disputeService.createDispute(request, USER_ID);
                    successCount.incrementAndGet();
                } catch (Exception e) {
                    exceptions.add(e);
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await(); // Wait for both threads to finish
        executor.shutdown();

        Assertions.assertEquals(1, successCount.get(), "Exactly one call to createDispute should succeed.");
        Assertions.assertEquals(CONCURRENT_REQUESTS - 1, exceptions.size(), "All other calls should fail with an exception.");
        Throwable thrown = exceptions.get(0);
        Assertions.assertTrue(thrown instanceof DisputeException, "The exception thrown should be a DisputeException.");
        String record = disputeCreationCommand.get(lockKey);
        Assertions.assertNotNull(record, "The record should have been successfully created in Aerospike.");
    }

    @Override
    protected com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage getDisputeStage() {
        return com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage.FIRST_LEVEL;
    }
}
