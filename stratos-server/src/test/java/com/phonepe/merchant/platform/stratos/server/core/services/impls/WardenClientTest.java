package com.phonepe.merchant.platform.stratos.server.core.services.impls;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.error.configurator.model.service.ResourceErrorService;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.ErrorConfiguratorBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.clients.WardenClient;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.olympus.im.models.user.UserDetails;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import com.phonepe.platform.http.v2.executor.factory.HttpExecutorBuilderFactory;
import io.dropwizard.setup.Environment;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

/**
 * <AUTHOR>
 */
public class WardenClientTest extends ErrorConfiguratorBaseTest {

    private WardenClient wardenClient;

    private OlympusIMClient olympusIMClient;

    private HttpExecutorBuilderFactory httpExecutorBuilderFactory;

    @Mock
    private ServiceEndpointProviderFactory serviceEndpointProviderFactory;

    @Mock
    private HttpConfiguration httpConfiguration;

    @Mock
    private Environment environment;


    @BeforeEach
    public void setup() throws URISyntaxException, MalformedURLException {
        MockitoAnnotations.initMocks(this);
        wardenClient = new WardenClient(serviceEndpointProviderFactory, httpConfiguration, environment,
                new ObjectMapper(), new MetricRegistry(), olympusIMClient);
        ResourceErrorService<StratosErrorCodeKey> resourceErrorService = getResourceErrorService();
        MapperUtils.init(new ObjectMapper());
        DisputeExceptionUtil.init(resourceErrorService);
    }

    @Test
    public void testCreateNewWorkflowType() {
        List<UserDetails> userDetails = new ArrayList<>();
        Assertions.assertThrows(Exception.class,
                () -> wardenClient.createNewWorkFlowType("WardenWorkflowType", "Description", userDetails));
    }


}
