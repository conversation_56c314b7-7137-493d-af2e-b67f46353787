package com.phonepe.merchant.platform.stratos.server.integrationTests.file.pg.beans;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PgFirstLevelChargebackBean {

    @JsonProperty("CB Received Date")
    private String cbReceivedDate;
    @JsonProperty("Source")
    private String source;
    @JsonProperty("Bank Ref No")
    private String bankRefNo;
    @JsonProperty("Bank Disputed Amount")
    private String bankDisputedAmount;
    @JsonProperty("SM Transaction id(SRC_PRN)")
    private String transactionId;
    @JsonProperty("PG_ID(RET_BID)")
    private String pgId;
    @JsonProperty("Reason Code")
    private String reasonCode;
    @JsonProperty("Dispute Reason")
    private String disputeReason;
}
