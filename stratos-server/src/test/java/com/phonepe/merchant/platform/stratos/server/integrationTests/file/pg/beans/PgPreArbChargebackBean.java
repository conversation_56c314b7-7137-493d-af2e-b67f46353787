package com.phonepe.merchant.platform.stratos.server.integrationTests.file.pg.beans;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PgPreArbChargebackBean {

    @JsonProperty("CB Received Date")
    private String cbReceivedDate;
    @JsonProperty("Source Of CB")
    private String sourceOfCb;
    @JsonProperty("Amount")
    private String amount;
    @JsonProperty("Transaction ID")
    private String transactionId;
}
