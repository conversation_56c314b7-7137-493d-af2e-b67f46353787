package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.wallet;


import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.NPCI_ACK_CHARGEBACK;
import static org.junit.Assert.assertEquals;

import com.fasterxml.jackson.dataformat.csv.CsvMapper;
import com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeCategory;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeTypeDto;
import com.phonepe.merchant.platform.stratos.models.files.FileFormat;
import com.phonepe.merchant.platform.stratos.models.files.FileTypeDto;
import com.phonepe.merchant.platform.stratos.models.files.responses.UpiFileHistoryProcessingSummaryRow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.FileRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.RowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.RowState;
import com.phonepe.merchant.platform.stratos.server.core.resources.FileResource;
import com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.wallet.firstLevel.WalletFirstLevelBaseTest;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.files.WalletFileRowProcessorImpl;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.models.payments.pay.TransactionDetail;
import com.phonepe.olympus.im.client.OlympusIMClient;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;

public class FileMetaAPIIntegrationTest extends WalletFirstLevelBaseTest {

    private FileResource fileResource;
    private FileRepository fileRepository;
    private RowRepository rowRepository;
    private OlympusIMClient olympusIMClient;
    private DisputeRepository disputeRepository;
    private DisputeWorkflowRepository disputeWorkflowRepository;
    private WalletFileRowProcessorImpl walletFileRowProcessor;

    @BeforeEach
    void setUpGuiceInjection() {
        truncateDb();
        fileResource = guiceInjector.getInstance(FileResource.class);
        fileRepository = guiceInjector.getInstance(FileRepository.class);
        rowRepository = guiceInjector.getInstance(RowRepository.class);
        olympusIMClient = guiceInjector.getInstance(OlympusIMClient.class);
        disputeRepository = guiceInjector.getInstance(DisputeRepository.class);
        disputeWorkflowRepository = guiceInjector.getInstance(DisputeWorkflowRepository.class);
        walletFileRowProcessor = guiceInjector.getInstance(WalletFileRowProcessorImpl.class);
    }

    @Test
    void testDownloadProcessingRowForWallet() throws Exception {

        createEntrySetupForAPI("WALLET", String.valueOf(LocalDateTime.now()), dispute.getTransactionReferenceId());
        DisputeWorkflow walletDisputeWorkflow = disputeWorkflow;

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForCreateChargebackApiMockingForWallet(String.valueOf(((double) walletDisputeWorkflow.getDisputedAmount())), String.valueOf(dispute.getTransactionAmount()));

        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        final var walletFileResponse = fileResource.upload(DisputeTypeDto.WALLET_CHARGEBACK,
            FileTypeDto.PHP,
            TestDataUtils.WalletFirstLevelChargebackCSVFileStream(String.valueOf(((double) walletDisputeWorkflow.getDisputedAmount()/100)), String.valueOf(dispute.getTransactionAmount())),
            FormDataContentDisposition.name("randomName123").fileName("file123.csv")
                .build(), "AuthToken", serviceUserPrincipal);
        AssertionUtils.assertRowStateEquals(RowState.FAILED, walletFileResponse.getId(), 30, TimeUnit.SECONDS);

        final var walletiResponse = fileResource.downloadProcessingRow(FileFormat.CSV,
            walletFileResponse.getId());
        final var list = new CsvMapper().readerWithTypedSchemaFor(
                UpiFileHistoryProcessingSummaryRow.class)
            .readValues((byte[]) walletiResponse.getEntity()).readAll();
        assertEquals(2, list.size());
        assertEquals("RRN1234", ((UpiFileHistoryProcessingSummaryRow) list.get(1)).getRrn());
        assertEquals("YBL123", ((UpiFileHistoryProcessingSummaryRow) list.get(1)).getUpiTransactionId());
        assertEquals("Chargeback Raise",((UpiFileHistoryProcessingSummaryRow) list.get(1)).getAdjType());
        assertEquals("YES",((UpiFileHistoryProcessingSummaryRow) list.get(1)).getBeneficiary());
        assertEquals("DISPUTE_WORKFLOW_NOT_FOUND",((UpiFileHistoryProcessingSummaryRow) list.get(1)).getCode());
    }
    @Test
    void testFullWalletFlowForAccepted() throws Exception {

        createEntrySetupForAPI("WALLET", String.valueOf(LocalDateTime.now()), dispute.getTransactionReferenceId());
        disputeRepository.save(dispute, storedDispute -> {
            disputeWorkflowRepository.save(disputeWorkflow);
            return storedDispute;
        });

        disputeWorkflow.setCurrentState(NPCI_ACK_CHARGEBACK);

        walletFileRowProcessor.triggerEvent(disputeWorkflow,dispute, TransactionDetail.builder().build());

        AssertionUtils.assertDisputeWorkflowStateEquals(
            dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            NPCI_ACK_CHARGEBACK, 5, TimeUnit.SECONDS);

        disputeWorkflow.setCurrentState(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK);

        walletFileRowProcessor.triggerEvent(disputeWorkflow,dispute, TransactionDetail.builder().build());

        AssertionUtils.assertDisputeWorkflow(
            disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK, dispute.getDisputeCategory(), disputeWorkflow.getDisputeType());
    }

    @Test
    void testFullWalletFlowForRejected() throws Exception {

        createEntrySetupForAPI("WALLET", String.valueOf(LocalDateTime.now()), dispute.getTransactionReferenceId());
        disputeRepository.save(dispute, storedDispute -> {
            disputeWorkflowRepository.save(disputeWorkflow);
            return storedDispute;
        });

        disputeWorkflow.setCurrentState(NPCI_ACK_CHARGEBACK);

        walletFileRowProcessor.triggerEvent(disputeWorkflow,dispute, TransactionDetail.builder().build());

        AssertionUtils.assertDisputeWorkflowStateEquals(
            dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            NPCI_ACK_CHARGEBACK, 5, TimeUnit.SECONDS);

        disputeWorkflow.setCurrentState(DisputeWorkflowState.NPCI_REPRESENTMENT_COMPLETED);

        walletFileRowProcessor.triggerEvent(disputeWorkflow,dispute, TransactionDetail.builder().build());

        AssertionUtils.assertDisputeWorkflow(
            disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.NPCI_REPRESENTMENT_COMPLETED, dispute.getDisputeCategory(), disputeWorkflow.getDisputeType());
    }

    @Test
    void testFullWalletFlowForPartialRejected() throws Exception {

        createEntrySetupForAPI("WALLET", String.valueOf(LocalDateTime.now()), dispute.getTransactionReferenceId());
        disputeRepository.save(dispute, storedDispute -> {
            disputeWorkflowRepository.save(disputeWorkflow);
            return storedDispute;
        });

        disputeWorkflow.setCurrentState(NPCI_ACK_CHARGEBACK);

        walletFileRowProcessor.triggerEvent(disputeWorkflow,dispute, TransactionDetail.builder().build());

        AssertionUtils.assertDisputeWorkflowStateEquals(
            dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            NPCI_ACK_CHARGEBACK, 5, TimeUnit.SECONDS);

        disputeWorkflow.setCurrentState(DisputeWorkflowState.NPCI_REPRESENTMENT_COMPLETED);

        walletFileRowProcessor.triggerEvent(disputeWorkflow,dispute, TransactionDetail.builder().build());

        AssertionUtils.assertDisputeWorkflow(
            disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.NPCI_REPRESENTMENT_COMPLETED, dispute.getDisputeCategory(), disputeWorkflow.getDisputeType());
    }
}
