package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.udir;

import com.phonepe.merchant.platform.stratos.models.disputes.DisputeStageDto;
import com.phonepe.merchant.platform.stratos.models.disputes.UdirComplaintStateDto;
import com.phonepe.merchant.platform.stratos.models.udir.requests.UdirOutgoingComplaintStatusRequest;
import com.phonepe.merchant.platform.stratos.models.udir.requests.UdirOutgoingRaiseComplaintRequest;
import com.phonepe.merchant.platform.stratos.models.udir.response.UdirOutgoingComplaintStatusResponse;
import com.phonepe.merchant.platform.stratos.server.BaseTest;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeCategory;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.resources.DisputeResource;
import com.phonepe.merchant.platform.stratos.server.core.resources.UdirResource;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.payments.upiclientmodel.complaint.UPIClientOutgoingComplaintResponse;
import com.phonepe.payments.upiclientmodel.enums.ComplaintRequestType;
import com.phonepe.payments.upiclientmodel.enums.ComplaintState;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

public class UdirDeemedRaiseComplaintValidDeemedWorkflowTest extends BaseTest {

    private UdirResource udirResource;
    private DisputeService disputeService;
    private DisputeResource disputeResource;

    private OlympusIMClient olympusIMClient;

    @BeforeEach
    void setUpGuiceInjection() {
        udirResource = guiceInjector.getInstance(UdirResource.class);
        disputeService = guiceInjector.getInstance(DisputeService.class);
        disputeResource = guiceInjector.getInstance(DisputeResource.class);
        olympusIMClient = guiceInjector.getInstance(OlympusIMClient.class);
        truncateDb();
    }

    @Test
    void testValidDeemedTransaction() {
        var transactionId = IdGenerator.generate("T").getId();
        var complaintId = IdGenerator.generate("CS").getId();

        MockingUtils.setupPaymentTransactionDetail(transactionId, 100, "DEEMED");
        MockingUtils.setupPaymentsRaiseComplaintMocking();
        Mockito.doReturn(true).when(olympusIMClient).verifyPermission(Mockito.any(), Mockito.any());

        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        udirResource.raiseComplaint(UdirOutgoingRaiseComplaintRequest.builder()
            .paymentTransactionId(transactionId)
            .requestCode(ComplaintRequestType.COMPLAINT_REQ_P2P_DEEMED)
            .adjAmount(100)
            .disputeStage(DisputeStageDto.FIRST_LEVEL)
            .complaintId(complaintId)
            .build(), serviceUserPrincipal);

        var response1 = (UdirOutgoingComplaintStatusResponse) disputeResource.status(
            UdirOutgoingComplaintStatusRequest
                .builder()
                .complaintId(complaintId)
                .transactionId(transactionId)
                .complaintId(complaintId)
                .build(), serviceUserPrincipal);

        Assertions.assertEquals(UdirComplaintStateDto.ACCEPTED, response1.getComplaintState());
        Assertions.assertEquals(complaintId, response1.getComplaintId());

        final var disputeWorkflow = disputeService.
            getDisputeWorkflow(transactionId, DisputeType.UDIR_OUTGOING_COMPLAINT,
                DisputeStage.FIRST_LEVEL);

        AssertionUtils.assertDisputeWorkflowStateEquals(transactionId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.RECEIVED);

        Assertions.assertEquals(DisputeCategory.UDIR_DEEMED,
            disputeWorkflow.getDispute().getDisputeCategory());

        udirResource.complaintCallback(UPIClientOutgoingComplaintResponse.builder()
            .complaintId(disputeWorkflow.getDisputeWorkflowId())
            .state(ComplaintState.INITIATED)
            .build());

        var response2 = (UdirOutgoingComplaintStatusResponse) disputeResource.status(
            UdirOutgoingComplaintStatusRequest
                .builder()
                .complaintId(complaintId)
                .transactionId(transactionId)
                .complaintId(complaintId)
                .build(),
                serviceUserPrincipal);

        Assertions.assertEquals(UdirComplaintStateDto.PENDING, response2.getComplaintState());
        Assertions.assertEquals(complaintId, response2.getComplaintId());
        Assertions.assertNull(response2.getCrn());

        AssertionUtils.assertDisputeWorkflowStateEquals(transactionId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.UDIR_COMPLAINT_ACCEPTED);

        udirResource.complaintCallback(UPIClientOutgoingComplaintResponse.builder()
            .complaintId(disputeWorkflow.getDisputeWorkflowId())
            .state(ComplaintState.COMPLETED)
            .crn("1234Abcd")
            .build());

        AssertionUtils.assertDisputeWorkflowStateEquals(transactionId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.UDIR_RESPONSE_RECEIVED);

        var response3 = (UdirOutgoingComplaintStatusResponse) disputeResource.status(
            UdirOutgoingComplaintStatusRequest
                .builder()
                .complaintId(complaintId)
                .transactionId(transactionId)
                .complaintId(complaintId)
                .build(),
                serviceUserPrincipal);

        Assertions.assertEquals(UdirComplaintStateDto.COMPLETED, response3.getComplaintState());
        Assertions.assertEquals("1234Abcd", response3.getCrn());
        Assertions.assertEquals(complaintId, response2.getComplaintId());
    }

}
