package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.udir;

import com.phonepe.merchant.platform.stratos.models.commons.DateRange;
import com.phonepe.merchant.platform.stratos.server.BaseTest;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.resources.UdirResource;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import java.time.LocalDate;
import java.time.LocalDateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class UdirResponseNotReceivedTest extends BaseTest {

    private DisputeWorkflowRepository disputeWorkflowRepository;
    private UdirResource udirResource;
    private DisputeRepository disputeRepository;

    @BeforeEach
    void setUpGuiceInjection() {
        udirResource = guiceInjector.getInstance(UdirResource.class);
        disputeWorkflowRepository = guiceInjector.getInstance(DisputeWorkflowRepository.class);
        disputeRepository = guiceInjector.getInstance(DisputeRepository.class);
    }

    @Test
    void test() {

        final var currentDateTime = LocalDateTime.now();

        // NPCI Not Responded No Ack Callback
        final Dispute dispute1 = TestDataUtils.getServiceChargeBackDispute(DisputeType.UDIR_OUTGOING_COMPLAINT,
            DisputeStage.FIRST_LEVEL);
        final DisputeWorkflow disputeWorkflow1 = TestDataUtils.getDisputeWorkflow(
            DisputeType.UDIR_OUTGOING_COMPLAINT, DisputeStage.FIRST_LEVEL, dispute1.getDisputeId(),
            dispute1.getTransactionReferenceId(),
            DisputeWorkflowEvent.CREATE_ENTRY, DisputeWorkflowState.RECEIVED,
            currentDateTime, currentDateTime);
        disputeWorkflowRepository.save(disputeWorkflow1);
        disputeRepository.save(dispute1);

        // NPCI Not Responded After Ack Callback
        final Dispute dispute2 = TestDataUtils.getServiceChargeBackDispute(DisputeType.UDIR_OUTGOING_COMPLAINT,
            DisputeStage.FIRST_LEVEL);
        final DisputeWorkflow disputeWorkflow2 = TestDataUtils.getDisputeWorkflow(
            DisputeType.UDIR_OUTGOING_COMPLAINT, DisputeStage.FIRST_LEVEL, dispute2.getDisputeId(),
            dispute2.getTransactionReferenceId(),
            DisputeWorkflowEvent.NPCI_ACCEPTED_COMPLAINT,
            DisputeWorkflowState.UDIR_COMPLAINT_ACCEPTED,
            currentDateTime, currentDateTime);
        disputeWorkflowRepository.save(disputeWorkflow2);
        disputeRepository.save(dispute2);

        // NPCI has 1 more day to respond, Ack Callback Received
        final Dispute dispute3 = TestDataUtils.getServiceChargeBackDispute(DisputeType.UDIR_OUTGOING_COMPLAINT,
            DisputeStage.FIRST_LEVEL);
        final DisputeWorkflow disputeWorkflow3 = TestDataUtils.getDisputeWorkflow(
            DisputeType.UDIR_OUTGOING_COMPLAINT, DisputeStage.FIRST_LEVEL, dispute3.getDisputeId(),
            dispute3.getTransactionReferenceId(),
            DisputeWorkflowEvent.NPCI_ACCEPTED_COMPLAINT,
            DisputeWorkflowState.UDIR_COMPLAINT_ACCEPTED,
            currentDateTime, currentDateTime.plusDays(1));
        disputeWorkflowRepository.save(disputeWorkflow3);
        disputeRepository.save(dispute3);

        // Timeline has already past for NPCI to respond
        // Note: Yesterday's job must have failed due to which this Complaint is not in TTL state
        final Dispute dispute4 = TestDataUtils.getServiceChargeBackDispute(DisputeType.UDIR_OUTGOING_COMPLAINT,
            DisputeStage.FIRST_LEVEL);
        final DisputeWorkflow disputeWorkflow4 = TestDataUtils.getDisputeWorkflow(
            DisputeType.UDIR_OUTGOING_COMPLAINT, DisputeStage.FIRST_LEVEL,
            dispute4.getDisputeId(), dispute4.getTransactionReferenceId(),
            DisputeWorkflowEvent.NPCI_ACCEPTED_COMPLAINT,
            DisputeWorkflowState.UDIR_COMPLAINT_ACCEPTED,
            currentDateTime, currentDateTime.minusDays(1));
        disputeWorkflowRepository.save(disputeWorkflow4);
        disputeRepository.save(dispute4);

        // Today is TTL day but NPCI Response Received
        // Hence no need to mark it to not responded within TTL state
        final Dispute dispute5 = TestDataUtils.getServiceChargeBackDispute(DisputeType.UDIR_OUTGOING_COMPLAINT,
            DisputeStage.FIRST_LEVEL);
        final DisputeWorkflow disputeWorkflow5 = TestDataUtils.getDisputeWorkflow(
            DisputeType.UDIR_OUTGOING_COMPLAINT, DisputeStage.FIRST_LEVEL,
            dispute5.getDisputeId(), dispute5.getTransactionReferenceId(),
            DisputeWorkflowEvent.UDIR_NPCI_RESPONSE_RECEIVED,
            DisputeWorkflowState.UDIR_RESPONSE_RECEIVED,
            currentDateTime, currentDateTime);
        disputeWorkflowRepository.save(disputeWorkflow5);
        disputeRepository.save(dispute5);

        // NPCI has 1 more day to respond, Ack Callback Not Received
        final Dispute dispute6 = TestDataUtils.getServiceChargeBackDispute(DisputeType.UPI_CHARGEBACK,
            DisputeStage.FIRST_LEVEL);
        final DisputeWorkflow disputeWorkflow6 = TestDataUtils.getDisputeWorkflow(
            DisputeType.UDIR_OUTGOING_COMPLAINT, DisputeStage.FIRST_LEVEL,
            dispute6.getDisputeId(), dispute6.getTransactionReferenceId(),
            DisputeWorkflowEvent.CREATE_ENTRY,
            DisputeWorkflowState.RECEIVED,
            currentDateTime, currentDateTime.plusDays(1));
        disputeWorkflowRepository.save(disputeWorkflow6);
        disputeRepository.save(dispute6);

        final var currentDate = LocalDate.now();
        final var dateRange = DateRange.builder()
            .startDate(currentDate)
            .endDate(currentDate)
            .build();

        udirResource.updateChargebackStateBreachingTtl(dateRange);

        assertDisputeWorkflowState(
            DisputeWorkflowState.UDIR_RESPONSE_NOT_RECEIVED_WITHIN_TTL, disputeWorkflow1);
        assertDisputeWorkflowState(
            DisputeWorkflowState.UDIR_RESPONSE_NOT_RECEIVED_WITHIN_TTL, disputeWorkflow2);
        assertDisputeWorkflowState(DisputeWorkflowState.UDIR_COMPLAINT_ACCEPTED, disputeWorkflow3);
        assertDisputeWorkflowState(DisputeWorkflowState.UDIR_COMPLAINT_ACCEPTED, disputeWorkflow4);
        assertDisputeWorkflowState(
            DisputeWorkflowState.UDIR_RESPONSE_RECEIVED, disputeWorkflow5);
        assertDisputeWorkflowState(DisputeWorkflowState.RECEIVED, disputeWorkflow6);
    }

    private void assertDisputeWorkflowState(
        final DisputeWorkflowState state,
        final DisputeWorkflow disputeWorkflow) {
        AssertionUtils.assertDisputeWorkflowStateEquals(disputeWorkflow.getTransactionReferenceId(),
            disputeWorkflow.getDisputeWorkflowId(), state);
    }
}
