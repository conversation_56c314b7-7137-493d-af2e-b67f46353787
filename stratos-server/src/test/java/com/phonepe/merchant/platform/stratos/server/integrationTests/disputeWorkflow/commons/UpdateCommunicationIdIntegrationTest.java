package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.commons;

import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.DisputeWorkflowKey;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.UpdateCommunicationIdRequest;
import com.phonepe.merchant.platform.stratos.server.BaseTest;
import com.phonepe.merchant.platform.stratos.server.LoadOnlyOnClassLevelBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.resources.DisputeResource;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.olympus.im.models.security.ServiceUserPrincipal;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import io.dropwizard.primer.exception.PrimerException;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class UpdateCommunicationIdIntegrationTest extends LoadOnlyOnClassLevelBaseTest {

    private static final String COMMUNICATION_ID = "C1234567890";

    private DisputeService disputeService;

    private DisputeResource disputeResource;

    private DisputeWorkflowRepository disputeWorkflowRepository;
    private OlympusIMClient olympusIMClient;

    @BeforeEach
    void setUpGuiceInjection() {
        disputeService = guiceInjector.getInstance(DisputeService.class);
        disputeResource = guiceInjector.getInstance(DisputeResource.class);
        disputeWorkflowRepository = guiceInjector.getInstance(DisputeWorkflowRepository.class);
        olympusIMClient = guiceInjector.getInstance(OlympusIMClient.class);
    }

    @Test
    void test() {

        // Build Dispute & Dispute Workflow for Test Cases
        final var dispute = TestDataUtils
            .getServiceChargeBackDispute(DisputeType.UPI_CHARGEBACK, DisputeStage.FIRST_LEVEL);
        final var disputeWorkflow = TestDataUtils.getDisputeWorkflow(dispute);
        Mockito.doReturn(true).when(olympusIMClient).verifyPermission(Mockito.any(), Mockito.any());

        final var dispute2 = TestDataUtils
            .getServiceChargeBackDispute(DisputeType.UPI_CHARGEBACK, DisputeStage.FIRST_LEVEL);
        final var disputeWorkflow2 = TestDataUtils.getDisputeWorkflow(dispute2);

        final var savedDisputeWorkflow = disputeWorkflowRepository.save(disputeWorkflow);
        Assertions.assertTrue(savedDisputeWorkflow.isPresent());
        Assertions.assertNull(savedDisputeWorkflow.get().getCommunicationId());

        final var savedDisputeWorkflow2 = disputeWorkflowRepository.save(disputeWorkflow2);
        Assertions.assertTrue(savedDisputeWorkflow2.isPresent());
        Assertions.assertNull(savedDisputeWorkflow2.get().getCommunicationId());

        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        disputeResource.updateCommunicationIdInDisputeWorkflow(serviceUserPrincipal,
            UpdateCommunicationIdRequest.builder()
                .disputeWorkflowKeys(
                    List.of(
                        DisputeWorkflowKey.builder()
                            .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
                            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
                            .build(),
                        DisputeWorkflowKey.builder()
                            .transactionReferenceId(disputeWorkflow2.getTransactionReferenceId())
                            .disputeWorkflowId(disputeWorkflow2.getDisputeWorkflowId())
                            .build()))
                .communicationId(COMMUNICATION_ID)
                .build());

        final var updatedDisputeWorkflow = disputeService.validateAndGetDisputeWorkflow(
            disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId());
        Assertions.assertEquals(COMMUNICATION_ID, updatedDisputeWorkflow.getCommunicationId());

        final var updatedDisputeWorkflow2 = disputeService.validateAndGetDisputeWorkflow(
            disputeWorkflow2.getTransactionReferenceId(), disputeWorkflow2.getDisputeWorkflowId());
        Assertions.assertEquals(COMMUNICATION_ID, updatedDisputeWorkflow2.getCommunicationId());
    }
}
