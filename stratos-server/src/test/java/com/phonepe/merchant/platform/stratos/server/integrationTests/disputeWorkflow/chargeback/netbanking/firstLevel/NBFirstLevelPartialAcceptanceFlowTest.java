package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.netbanking.firstLevel;

import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.services.refund.orchestrator.models.RefundStatus;
import org.junit.jupiter.api.Test;

public class NBFirstLevelPartialAcceptanceFlowTest extends NBFirstLevelBaseTest {

    @Test
    void testPartialRepresentmentCompleted() {

        testTillPartialRepresentmentCompleted();

        MockingUtils.setUpRefundResponseMocking(RefundStatus.ACCEPTED.name(),
            disputeWorkflow.getDisputedAmount() - 50L);

        testTillRefundInitiated(RefundStatus.INITIATED);

        triggerCallBackAndAssertStatus(0);

        testRecoveryFlow();
    }


    @Test
    void testPartialRepresentmentCompletedAbsorption() {

        testTillPartialRepresentmentCompleted();

        MockingUtils.setUpRefundResponseMocking(RefundStatus.ACCEPTED.name(),
            disputeWorkflow.getDisputedAmount() - 50L);

        testTillRefundInitiated(RefundStatus.INITIATED);

        triggerCallBackAndAssertStatus(0);

        testAbsorbtionFlow();
    }

}
