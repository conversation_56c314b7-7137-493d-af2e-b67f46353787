package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.toa.p2pm;


import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.DisputeWorkflowKey;
import com.phonepe.merchant.platform.stratos.server.BaseTest;
import com.phonepe.merchant.platform.stratos.server.LoadOnlyOnClassLevelBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.NeuronTestUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.resources.ToaResource;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.models.payments.merchant.MerchantTransactionState;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import java.util.concurrent.TimeUnit;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class P2pmToaKillSwitchWorkflowTest extends LoadOnlyOnClassLevelBaseTest {

    private ToaResource toaResource;

    @BeforeEach
    void setUpGuiceInjection() {
        toaResource = guiceInjector.getInstance(ToaResource.class);
        MockingUtils.setupEventIngestionApiMocking();
        truncateDb();
    }

    /*
    Test: Kill Switch Enabled Test
    WORKFLOW: PULSE ->
                    RECEIVED -> TOA_BLOCKED_DUE_TO_KS -> TOA_COMPLETED_EXTERNALLY
    */
    @Test
    void testKSEnabledToExternallyProcessed(){

        final var transactionReferenceId = IdGenerator.generate("T").getId();

        MockingUtils.enableKillSwitchMocking(DisputeType.P2PM_TOA);
        MockingUtils.setDeemFailureUpiTransactionDetail(transactionReferenceId, 120);  // SUCCESS txn details api

        // Create a pulse
        NeuronTestUtils.createP2pmToaPulse(transactionReferenceId);

        // wait till the entry for workflow is created
        DisputeWorkflow disputeWorkflow = AssertionUtils
            .assertAndGetDisputeWorkflowCreation(transactionReferenceId, DisputeType.P2PM_TOA,
                DisputeStage.FIRST_LEVEL);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.TOA_BLOCKED_DUE_TO_KS,
            10L, TimeUnit.SECONDS);



        // Trigger event to move the dispute to externally processed
        final var manualRoleUser = TestDataUtils.getOlympusUser();

        var response = toaResource.processToaExternally(manualRoleUser, transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(), TestDataUtils.getCommentContext(), DisputeType.P2PM_TOA);


        Assertions.assertTrue(response.isSuccess());
        AssertionUtils.assertToaSummary(response.getData(), disputeWorkflow, DisputeWorkflowState.TOA_COMPLETED_EXTERNALLY);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.TOA_COMPLETED_EXTERNALLY,
            0, TimeUnit.SECONDS);

    }

    /*
  Test: Kill Switch Enabled Test
  WORKFLOW: PULSE ->
                  RECEIVED -> TOA_BLOCKED_DUE_TO_KS
                    retryTOA
                  TOA_BLOCKED_DUE_TO_KS -> P2PM_TOA_INITIATED -> P2PM_TOA_COMPLETED
  */
    @Test
    void testKSEnabledToRetryToa(){

        final var transactionReferenceId = IdGenerator.generate("T").getId();
        String merchantOrderId = "DEEM0-" + transactionReferenceId;

        MockingUtils.enableKillSwitchMocking(DisputeType.P2PM_TOA);
        MockingUtils.setDeemFailureUpiTransactionDetail(transactionReferenceId, 120);  // SUCCESS txn details api

        // Create a pulse
        NeuronTestUtils.createP2pmToaPulse(transactionReferenceId);

        // wait till the entry for workflow is created
        DisputeWorkflow disputeWorkflow = AssertionUtils
            .assertAndGetDisputeWorkflowCreation(transactionReferenceId, DisputeType.P2PM_TOA,
                DisputeStage.FIRST_LEVEL);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.TOA_BLOCKED_DUE_TO_KS,
            10L, TimeUnit.SECONDS);


        MockingUtils.setUpPaymentPayApi(true, 200); // SUCCESS pay api

        // mock transaction details status api completed
        MockingUtils.setMerchantTransactionsStatus("IRCTCINAPP", merchantOrderId,
            MerchantTransactionState.COMPLETED, 120);

        // Retry TOA
        final var manualRoleUser = TestDataUtils.getOlympusUser();

        var response = toaResource.retryToa(manualRoleUser, DisputeWorkflowKey.builder()
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .transactionReferenceId(transactionReferenceId)
            .build(), DisputeType.P2PM_TOA);


        Assertions.assertTrue(response.isSuccess());
        AssertionUtils.assertToaSummary(response.getData(), disputeWorkflow, DisputeWorkflowState.RECEIVED);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.P2PM_TOA_COMPLETED,
            20L, TimeUnit.SECONDS);

    }
}
