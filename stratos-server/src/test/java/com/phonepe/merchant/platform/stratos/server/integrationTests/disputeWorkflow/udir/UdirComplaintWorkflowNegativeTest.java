package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.udir;

import com.phonepe.merchant.platform.stratos.models.disputes.DisputeStageDto;
import com.phonepe.merchant.platform.stratos.models.disputes.UdirComplaintStateDto;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.models.udir.requests.UdirOutgoingComplaintStatusRequest;
import com.phonepe.merchant.platform.stratos.models.udir.requests.UdirOutgoingRaiseComplaintRequest;
import com.phonepe.merchant.platform.stratos.models.udir.response.UdirOutgoingComplaintStatusResponse;
import com.phonepe.merchant.platform.stratos.server.BaseTest;
import com.phonepe.merchant.platform.stratos.server.LoadOnlyOnClassLevelBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.resources.DisputeResource;
import com.phonepe.merchant.platform.stratos.server.core.resources.UdirResource;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.models.payments.pay.destination.DestinationType;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.payments.upiclientmodel.complaint.UPIClientOutgoingComplaintResponse;
import com.phonepe.payments.upiclientmodel.enums.ComplaintRequestType;
import com.phonepe.payments.upiclientmodel.enums.ComplaintState;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

public class UdirComplaintWorkflowNegativeTest extends LoadOnlyOnClassLevelBaseTest {

    private UdirResource udirResource;
    private DisputeService disputeService;
    private DisputeResource disputeResource;
    private OlympusIMClient olympusIMClient;

    @BeforeEach
    void setUpGuiceInjection() {
        udirResource = guiceInjector.getInstance(UdirResource.class);
        disputeService = guiceInjector.getInstance(DisputeService.class);
        disputeResource = guiceInjector.getInstance(DisputeResource.class);
        olympusIMClient = guiceInjector.getInstance(OlympusIMClient.class);
        truncateDb();
    }

    @Test
    void testPaymentCallFailed() {

        var transactionId = IdGenerator.generate("T").getId();
        var complaintId = IdGenerator.generate("CS").getId();
        MockingUtils.setupP2MExternalTransactionDetail(transactionId, 100,
            DestinationType.VPA.name(), "1234");
        MockingUtils.setupFailedPaymentsRaiseComplaintMocking();
        Mockito.doReturn(true).when(olympusIMClient).verifyPermission(Mockito.any(), Mockito.any());

        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        var stratosError = Assertions.assertThrows(DisputeException.class,
            () -> udirResource.raiseComplaint(UdirOutgoingRaiseComplaintRequest.builder()
                .paymentTransactionId(transactionId)
                .requestCode(ComplaintRequestType.COMPLAINT_REQ_FULFILLMENT_ISSUE_PAID_AGAIN)
                .adjAmount(100)
                .disputeStage(DisputeStageDto.FIRST_LEVEL)
                .complaintId(complaintId)
                .build(), serviceUserPrincipal));

        Assertions.assertEquals(StratosErrorCodeKey.COMMUNICATION_ERROR, stratosError.getErrorCode());

        final var disputeWorkflow = disputeService.
            getDisputeWorkflow(transactionId, DisputeType.UDIR_OUTGOING_COMPLAINT,
                DisputeStage.FIRST_LEVEL);

        AssertionUtils.assertDisputeWorkflowStateEquals(transactionId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.FAILURE);

        var response1 = (UdirOutgoingComplaintStatusResponse) disputeResource.status(
            UdirOutgoingComplaintStatusRequest
                .builder()
                .complaintId(complaintId)
                .transactionId(transactionId)
                .build(), serviceUserPrincipal);

        Assertions.assertEquals(UdirComplaintStateDto.FAILURE, response1.getComplaintState());
        Assertions.assertEquals(complaintId, response1.getComplaintId());

        var error = Assertions.assertThrows(DisputeException.class,
            () -> udirResource.complaintCallback(UPIClientOutgoingComplaintResponse.builder()
                .complaintId(disputeWorkflow.getDisputeWorkflowId())
                .state(ComplaintState.INITIATED)
                .crn("1234")
                .build()));

        Assertions.assertEquals(StratosErrorCodeKey.TRANSITION_NOT_ALLOWED, error.getErrorCode());
    }


    @Test
    void testNpciRejected() {

        var transactionId = IdGenerator.generate("T").getId();
        var complaintId = IdGenerator.generate("CS").getId();
        MockingUtils.setupP2MExternalTransactionDetail(transactionId, 100,
            DestinationType.VPA.name(), "1234");
        MockingUtils.setupPaymentsRaiseComplaintMocking();
        Mockito.doReturn(true).when(olympusIMClient).verifyPermission(Mockito.any(), Mockito.any());

        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        udirResource.raiseComplaint(UdirOutgoingRaiseComplaintRequest.builder()
            .paymentTransactionId(transactionId)
            .requestCode(ComplaintRequestType.COMPLAINT_REQ_MERCHANT_CONFIRMATION_PENDING)
            .adjAmount(100)
            .disputeStage(DisputeStageDto.FIRST_LEVEL)
            .complaintId(complaintId)
            .build(), serviceUserPrincipal);

        final var disputeWorkflow = disputeService.
            getDisputeWorkflow(transactionId, DisputeType.UDIR_OUTGOING_COMPLAINT,
                DisputeStage.FIRST_LEVEL);

        AssertionUtils.assertDisputeWorkflowStateEquals(transactionId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.RECEIVED);

        udirResource.complaintCallback(UPIClientOutgoingComplaintResponse.builder()
            .complaintId(disputeWorkflow.getDisputeWorkflowId())
            .state(ComplaintState.FAILED)
            .errorCode("ABCD")
            .build());

        AssertionUtils.assertDisputeWorkflowStateEquals(transactionId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.UDIR_COMPLAINT_REJECTED);

        var response = (UdirOutgoingComplaintStatusResponse) disputeResource.status(
            UdirOutgoingComplaintStatusRequest
                .builder()
                .complaintId(complaintId)
                .transactionId(transactionId)
                .build(), serviceUserPrincipal);

        Assertions.assertEquals(UdirComplaintStateDto.FAILURE, response.getComplaintState());
        Assertions.assertEquals("ABCD", response.getErrorCode());
        Assertions.assertEquals(complaintId, response.getComplaintId());
    }

    @Test
    void testRaiseComplaintHystrixTimeout() {

        var transactionId = IdGenerator.generate("T").getId();
        var complaintId = IdGenerator.generate("CS").getId();

        MockingUtils.setupP2MExternalTransactionDetail(transactionId, 100,
            DestinationType.VPA.name(), "1234");
        MockingUtils.setupPaymentsRaiseComplaintTimeoutMocking();
        Mockito.doReturn(true).when(olympusIMClient).verifyPermission(Mockito.any(), Mockito.any());

        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        udirResource.raiseComplaint(UdirOutgoingRaiseComplaintRequest.builder()
            .paymentTransactionId(transactionId)
            .requestCode(ComplaintRequestType.COMPLAINT_REQ_FULFILLMENT_ISSUE_PAID_AGAIN)
            .adjAmount(100)
            .disputeStage(DisputeStageDto.FIRST_LEVEL)
            .complaintId(complaintId)
            .build(), serviceUserPrincipal);

        var response = (UdirOutgoingComplaintStatusResponse) disputeResource.status(
            UdirOutgoingComplaintStatusRequest
                .builder()
                .complaintId(complaintId)
                .transactionId(transactionId)
                .build(), serviceUserPrincipal);

        Assertions.assertEquals(UdirComplaintStateDto.ACCEPTED, response.getComplaintState());

        final var disputeWorkflow = disputeService.
            getDisputeWorkflow(transactionId, DisputeType.UDIR_OUTGOING_COMPLAINT,
                DisputeStage.FIRST_LEVEL);

        AssertionUtils.assertDisputeWorkflowStateEquals(transactionId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.RECEIVED);
        Assertions.assertEquals(complaintId, response.getComplaintId());
    }


    @Test
    void testInvalidReceivedType() {
        var transactionId = IdGenerator.generate("T").getId();
        var complaintId = IdGenerator.generate("CS").getId();

        MockingUtils.setupP2MExternalTransactionDetail(transactionId, 100,
            DestinationType.ACCOUNT.name(), "1234");
        MockingUtils.setupPaymentsRaiseComplaintTimeoutMocking();
        Mockito.doReturn(true).when(olympusIMClient).verifyPermission(Mockito.any(), Mockito.any());

        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        final var stratosError = Assertions.assertThrows(DisputeException.class, () ->
            udirResource.raiseComplaint(UdirOutgoingRaiseComplaintRequest.builder()
                .paymentTransactionId(transactionId)
                .requestCode(ComplaintRequestType.COMPLAINT_REQ_FULFILLMENT_ISSUE_PAID_AGAIN)
                .adjAmount(100)
                .disputeStage(DisputeStageDto.FIRST_LEVEL)
                .complaintId(complaintId)
                .build(), serviceUserPrincipal));

        Assertions.assertEquals(StratosErrorCodeKey.INVALID_UDIR_COMPLAINT,
            stratosError.getErrorCode());
    }

    @Test
    void testChargeBackAcceptanceCheckForNonFraudImpl() {

        var transactionId = IdGenerator.generate("T").getId();
        var complaintId = IdGenerator.generate("CS").getId();
        MockingUtils.setupP2MExternalTransactionDetail(transactionId, 100,
            DestinationType.VPA.name(), "1234");
        MockingUtils.setupPaymentsRaiseComplaintMocking();
        Mockito.doReturn(true).when(olympusIMClient).verifyPermission(Mockito.any(), Mockito.any());

        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        udirResource.raiseComplaint(UdirOutgoingRaiseComplaintRequest.builder()
            .paymentTransactionId(transactionId)
            .requestCode(ComplaintRequestType.COMPLAINT_REQ_MERCHANT_CONFIRMATION_PENDING)
            .adjAmount(100)
            .disputeStage(DisputeStageDto.FIRST_LEVEL)
            .complaintId(complaintId)
            .build(), serviceUserPrincipal);

        final var disputeWorkflow = disputeService.
            getDisputeWorkflow(transactionId, DisputeType.UDIR_OUTGOING_COMPLAINT,
                DisputeStage.FIRST_LEVEL);

        MockingUtils.setupGetMerchantProfileApiMocking(disputeWorkflow.getDispute().getMerchantId(),
            "P2P_MERCHANT");

        UnsupportedOperationException exception = Assertions.assertThrows(
            UnsupportedOperationException.class, () -> {
                disputeService.buildChargebackPayload(disputeWorkflow);
            });

        Assertions.assertEquals(
            "Invalid build Chargeback kratos Payload for dispute type UDIR_OUTGOING_COMPLAINT",
            exception.getMessage());
    }
}
