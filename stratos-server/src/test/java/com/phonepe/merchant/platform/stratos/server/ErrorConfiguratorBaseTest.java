package com.phonepe.merchant.platform.stratos.server;

import com.phonepe.error.configurator.model.ErrorConfiguratorConfig;
import com.phonepe.error.configurator.model.service.ResourceErrorService;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.net.URL;

@Slf4j
public abstract class ErrorConfiguratorBaseTest {

    @NotNull
    public ResourceErrorService<StratosErrorCodeKey> getResourceErrorService() throws MalformedURLException, URISyntaxException {
        ErrorConfiguratorConfig errorConfiguratorConfig = getErrorConfiguratorConfig();

        URL[] urlsTest = new URL[]{BaseTest.class.getProtectionDomain().getCodeSource().getLocation().toURI().toURL()};
        log.error("File Name for code source : {}", urlsTest[0].getFile());
        String baseClassPath =urlsTest[0].getFile() + "../../../"+ errorConfiguratorConfig.getErrorPropertiesPath();
        File resourceBundlePath = new File(baseClassPath);


        URL[] urls = new URL[]{resourceBundlePath.toURI().toURL()};
        log.error("File Name with error file: {}", urls[0].getFile());
        ResourceErrorService<StratosErrorCodeKey> resourceErrorService = new ResourceErrorService<>(errorConfiguratorConfig, resourceBundlePath, StratosErrorCodeKey.class);
        return resourceErrorService;
    }

    @NotNull
    public ErrorConfiguratorConfig getErrorConfiguratorConfig() {
        ErrorConfiguratorConfig errorConfiguratorConfig =new ErrorConfiguratorConfig() {
            @Override
            public String getErrorPropertiesPath() {
                return "stratos-server/src/main/resources/resourcebundle/";
            }

            @Override
            public String getBaseName() {
                return "resource";
            }
        };
        return errorConfiguratorConfig;
    }

}
