package com.phonepe.merchant.platform.stratos.server.integrationTests.row;

import com.phonepe.merchant.platform.stratos.models.commons.DateRange;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeTypeDto;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.filters.DateRangeFilter;
import com.phonepe.merchant.platform.stratos.models.files.FileTypeDto;
import com.phonepe.merchant.platform.stratos.models.files.RowStateDto;
import com.phonepe.merchant.platform.stratos.models.row.EdcRowDto;
import com.phonepe.merchant.platform.stratos.models.row.RowTransactionType;
import com.phonepe.merchant.platform.stratos.models.row.RowTypeDto;
import com.phonepe.merchant.platform.stratos.models.row.requests.RowHistoryRequest;
import com.phonepe.merchant.platform.stratos.server.LoadOnlyOnClassLevelBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.RowState;
import com.phonepe.merchant.platform.stratos.server.core.resources.FileResource;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.RowService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.olympus.im.models.security.ServiceUserPrincipal;
import com.phonepe.olympus.im.models.user.HumanUserDetails;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class EdcRowProcessingIntegrationTest extends LoadOnlyOnClassLevelBaseTest {

    private DisputeService disputeService;
    private FileResource fileResource;
    private RowService rowService;
    private OlympusIMClient olympusIMClient;


    @BeforeEach
    void setUpGuiceInjection() {
        rowService = guiceInjector.getInstance(RowService.class);
        disputeService = guiceInjector.getInstance(DisputeService.class);
        fileResource = guiceInjector.getInstance(FileResource.class);
        olympusIMClient = guiceInjector.getInstance(OlympusIMClient.class);
        truncateDb();
    }

    @Test
    public void testEdcRowProcessingTest() throws Exception {
        final var transactionReferenceId = IdGenerator.generate("T").getId();
        final var merchantTransactionId = IdGenerator.generate("MT").getId();
        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForEdcCreateChargebackApiMocking();
        MockingUtils.setEdcTransactionDetails("BijliPay", "MERCHANTID", "TERMINALID", "RRN",
            transactionReferenceId, 100, 0, merchantTransactionId);
        MockingUtils.setupEdcTransactionsFromMerchantTransactionId(transactionReferenceId, 100, 0, merchantTransactionId);
        MockingUtils.setupEventIngestionApiMocking();
        MockingUtils.setEdcUnBlockReversals(merchantTransactionId);
        MockingUtils.setEdcBlockReversals(merchantTransactionId);
        final var userDetailsOlympus = HumanUserDetails.builder()
            .userId("test-user")
            .build();
        final var userAuthDetails = UserAuthDetails.builder()
            .userDetails(userDetailsOlympus)
            .build();
        final var serviceUserPrincipal = ServiceUserPrincipal.builder()
            .userAuthDetails(userAuthDetails)
            .build();

        var response = fileResource.upload(DisputeTypeDto.EDC_CHARGEBACK,
            FileTypeDto.EDC_FIRST_LEVEL, TestDataUtils.edcChargebackCSVFileStream(),
            FormDataContentDisposition.name("randomName").fileName("file.csv")
                .build(),"AuthToken", serviceUserPrincipal);
        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);

        final var disputeWorkflow = disputeService.
            getDisputeWorkflow(transactionReferenceId, DisputeType.EDC_CHARGEBACK,
                DisputeStage.FIRST_LEVEL);

        Assertions.assertTrue(disputeWorkflow instanceof FinancialDisputeWorkflow);
        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.REFUND_BLOCKED,
            5L, TimeUnit.SECONDS);
        disputeService.triggerEvent(
            userAuthDetails,
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK,
            Constants.EMPTY_TRANSITION_CONTEXT);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK);

        disputeService.triggerEvent(
            userAuthDetails,
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowEvent.COMPLETE_ACCEPTANCE,
            Constants.EMPTY_TRANSITION_CONTEXT);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.ACCEPTANCE_COMPLETED);

        Assertions.assertThrows((DisputeException.class), ()->{
            rowService.processSignal(TestDataUtils.getEdcReportRequest(transactionReferenceId,
                RowTransactionType.CHARGEBACK, 110.0, "mis_reports_file1"));
        });

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.ACCEPTANCE_COMPLETED);

        rowService.processSignal(TestDataUtils.getEdcReportRequest(transactionReferenceId,
            RowTransactionType.CHARGEBACK, 100.0, "mis_reports_file2"));

        AssertionUtils.assertRowStateEquals(RowState.PROCESSED, "mis_reports_file2", 15,
            TimeUnit.SECONDS);
        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.DEBIT_RECEIVED);
        rowService.processSignal(TestDataUtils.getEdcReportRequest(transactionReferenceId,
            RowTransactionType.CHARGEBACK_REVERSED, 100.0, "mis_reports_file3"));

        AssertionUtils.assertRowStateEquals(RowState.FAILED, "mis_reports_file3", 15,
            TimeUnit.SECONDS);
        var RowResponse = rowService.history(RowHistoryRequest.builder()
            .rowTypes(Set.of(RowTypeDto.EDC_MIS_ROW))
            .dateRangeFilter(DateRangeFilter.builder()
                .dateRange(DateRange.builder().startDate(LocalDate.now().minusDays(2))
                    .endDate(LocalDate.now())
                    .build())
                .build())
            .build());
        Assertions.assertEquals(2, RowResponse.getRows().size());
        Assertions.assertEquals("mis_reports_file2",((EdcRowDto)RowResponse.getRows().get(0)).getSourceId());
        Assertions.assertEquals(RowStateDto.PROCESSED,((EdcRowDto)RowResponse.getRows().get(0)).getRowState());
        Assertions.assertEquals("mis_reports_file3",((EdcRowDto)RowResponse.getRows().get(1)).getSourceId());
        Assertions.assertEquals(RowStateDto.FAILED,((EdcRowDto)RowResponse.getRows().get(1)).getRowState());
    }
}
