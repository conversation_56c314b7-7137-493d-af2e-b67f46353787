package com.phonepe.merchant.platform.stratos.server;

import com.phonepe.error.configurator.model.service.ResourceErrorService;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.neuron.NotionalCreditPulseHandler;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.core.visitors.disputeworkflow.BlockRefundDisputeWorkflowVisitor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockitoAnnotations;

import javax.ws.rs.core.Response;
import java.net.MalformedURLException;
import java.net.URISyntaxException;

/**
 * <AUTHOR>
 */
@Slf4j
public class BlockRefundDisputeWorkflowVisitorTest extends ErrorConfiguratorBaseTest{

    private BlockRefundDisputeWorkflowVisitor blockRefundDisputeWorkflowVisitor;
    @BeforeEach
    public void setup()throws URISyntaxException, MalformedURLException {
        MockitoAnnotations.initMocks(this);
        ResourceErrorService<StratosErrorCodeKey> resourceErrorService = getResourceErrorService();
        log.error("resourceErrorService  : {}", resourceErrorService);
        DisputeExceptionUtil.init(resourceErrorService);

        blockRefundDisputeWorkflowVisitor =BlockRefundDisputeWorkflowVisitor.builder().build();;
    }

    @Test
    public void testVisitNotionalCreditToa(){
        Response response = blockRefundDisputeWorkflowVisitor.visitNotionalCreditToa();
        Assertions.assertEquals(400,response.getStatus());
    }

    @Test
    public void testVisitWalletChargeback(){
        Response response = blockRefundDisputeWorkflowVisitor.visitWalletChargeback();
        Assertions.assertEquals(400,response.getStatus());
    }
}
