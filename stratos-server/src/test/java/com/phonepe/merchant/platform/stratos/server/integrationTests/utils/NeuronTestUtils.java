package com.phonepe.merchant.platform.stratos.server.integrationTests.utils;

import com.phonepe.growth.neuron.pulse.Pulse;
import com.phonepe.growth.neuron.pulse.Signal;
import com.phonepe.merchant.platform.stratos.server.core.models.ToaState;
import com.phonepe.merchant.platform.stratos.server.core.neuron.NotionalCreditPulseHandler;
import com.phonepe.merchant.platform.stratos.server.core.neuron.P2pmToaPulseHandler;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import java.util.List;
import lombok.experimental.UtilityClass;

@UtilityClass
public class NeuronTestUtils {

    private P2pmToaPulseHandler p2pmToaPulsehandler;
    private NotionalCreditPulseHandler notionalCreditPulseHandler;

    public void init(final P2pmToaPulseHandler p2pmToaPulsehandler, final NotionalCreditPulseHandler notionalCreditPulseHandler) {
        NeuronTestUtils.p2pmToaPulsehandler = p2pmToaPulsehandler;
        NeuronTestUtils.notionalCreditPulseHandler = notionalCreditPulseHandler;
    }

    public String createP2pmToaPulse(String transactionReferenceId) {
        String eventId = IdGenerator.generate("E").getId()+"LONG-ID-34-CHAR-FROM_FOXTROT-EVENT";
        p2pmToaPulsehandler.handle(Pulse.builder()
            .signals(List.of(
                Signal.builder().name("transactionId").value(transactionReferenceId).build(),
                Signal.builder().name("eventId").value(eventId).build()
            ))
            .build());
        return eventId;
    }

    public void createNotionalToaPulse(Pulse pulse) {
        notionalCreditPulseHandler.handle(pulse);
    }

    public Pulse getNeuronPulse(String transactionReferenceId, String catalystId, ToaState toaState, String date) {

        String eventId = IdGenerator.generate("E").getId()+"LONG-ID-34-CHAR-FROM_FOXTROT-EVENT";
        return Pulse.builder()
                .signals(List.of(
                        Signal.builder().name("transactionId").value(transactionReferenceId).build(),
                        Signal.builder().name("catalystId").value(catalystId).build(),
                        Signal.builder().name("eventId").value(eventId).build(),
                        Signal.builder().name("toaState").value(toaState).build(),
                        Signal.builder().name("ticketCreationDate").value(date).build()
                ))
                .build();
    }

}
