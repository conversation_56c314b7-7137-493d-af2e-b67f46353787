package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.wallet.PreArb;

import com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeCategory;
import com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeStage;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.responses.CreateDisputeResponse;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeWorkflowUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import java.time.LocalDateTime;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;

class WalletPreArbRaiseDisputeFlowTest extends WalletPreArbLevelBaseTest {

    String USER_ID = "USER_ID";

    @ParameterizedTest(name = "{0}")
    @MethodSource(API_SERVICE_CATEGORY)
    void createDisputeForPreArbWithoutFirstLevelFlowTest(DisputeCategory disputeCategory) {

        var request = TestDataUtils.getWalletDisputeRequest(DisputeStage.PRE_ARBITRATION,
            disputeCategory, dispute.getTransactionReferenceId(),
            (int) dispute.getTransactionAmount());

        createEntrySetupForAPI("WALLET", String.valueOf(LocalDateTime.now()),
            request.getTransactionId());
        final var exception = Assertions.assertThrows(DisputeException.class,
            () -> disputeService.createDispute(request,
                USER_ID));
        Assertions.assertEquals(StratosErrorCodeKey.FIRST_LEVEL_DISPUTE_NOT_FOUND,
             exception.getErrorCode());
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource(API_SERVICE_CATEGORY)
    void createDisputeForPreArbWithoutFirstLevelTerminalState(
        com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeCategory disputeCategory) {

        var FirstLevelRequest = TestDataUtils.getWalletDisputeRequest(DisputeStage.FIRST_LEVEL,
            disputeCategory, dispute.getTransactionReferenceId(),
            (int) dispute.getTransactionAmount());

        createEntrySetupForAPI("WALLET", String.valueOf(LocalDateTime.now()),
            FirstLevelRequest.getTransactionId());
        CreateDisputeResponse firstLevelResponse = disputeService.createDispute(
            FirstLevelRequest, USER_ID);

        var request = TestDataUtils.getWalletDisputeRequest(DisputeStage.PRE_ARBITRATION,
            disputeCategory, dispute.getTransactionReferenceId(),
            (int) dispute.getTransactionAmount());

        createEntrySetupForAPI("WALLET", String.valueOf(LocalDateTime.now()),
            request.getTransactionId());
        final var exception = Assertions.assertThrows(DisputeException.class,
            () -> disputeService.createDispute(request,
                USER_ID));

        Assertions.assertEquals(StratosErrorCodeKey.FIRST_LEVEL_NOT_IN_TERMINAL_STATE,
            exception.getErrorCode());
        Assertions.assertEquals("First Level not in terminal state", exception.getMessage());
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource(API_SERVICE_CATEGORY)
    void testCreateDisputeForFraudPreArb(
        com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeCategory disputeCategory) {

        var FirstLevelRequest = TestDataUtils.getWalletDisputeRequest(DisputeStage.FIRST_LEVEL,
            disputeCategory, dispute.getTransactionReferenceId(),
            (int) dispute.getTransactionAmount());

        createEntrySetupForAPI("WALLET", String.valueOf(LocalDateTime.now()),
            FirstLevelRequest.getTransactionId());
        CreateDisputeResponse firstLevelResponse = disputeService.createDispute(
            FirstLevelRequest, USER_ID);

        Assertions.assertNotNull(firstLevelResponse);

        var request = TestDataUtils.getWalletDisputeRequest(DisputeStage.PRE_ARBITRATION,
            com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeCategory.FRAUD,
            dispute.getTransactionReferenceId(),
            (int) dispute.getTransactionAmount());

        createEntrySetupForAPI("WALLET", String.valueOf(LocalDateTime.now()),
            request.getTransactionId());
        final var exception = Assertions.assertThrows(DisputeException.class,
            () -> disputeService.createDispute(request,
                USER_ID));
        Assertions.assertEquals(StratosErrorCodeKey.FRAUD_CHARGEBACK_NOT_ALLOWED, exception.getErrorCode());
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource(API_SERVICE_CATEGORY)
    void createDisputeForPreArbWithMoreAmount(
        com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeCategory disputeCategory) {

        Dispute firstLevelDispute = TestDataUtils.getDispute(DisputeType.WALLET_CHARGEBACK,
            com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage.FIRST_LEVEL,
            com.phonepe.merchant.platform.stratos.server.core.models.DisputeCategory.SERVICE_CHARGEBACK);
        DisputeWorkflow firstLevelDisputeWorkflow = TestDataUtils.getDisputeWorkflow(
            firstLevelDispute);

        FinancialDisputeWorkflow financialDisputeWorkflow = DisputeWorkflowUtils.getFinancialDisputeWorkflow(
            firstLevelDisputeWorkflow);

        financialDisputeWorkflow.setAcceptedAmount((int) dispute.getTransactionAmount() - 100);
        firstLevelDisputeWorkflow.setCurrentState(DisputeWorkflowState.ACCEPTED_CHARGEBACK);

        disputeService.persistDisputeAndDisputeWorkflow(firstLevelDispute,
            firstLevelDisputeWorkflow);

        var request = TestDataUtils.getWalletDisputeRequest(DisputeStage.PRE_ARBITRATION,
            disputeCategory, firstLevelDispute.getTransactionReferenceId(),
            (int) firstLevelDispute.getTransactionAmount()-1);

        createEntrySetupForAPI("WALLET", String.valueOf(LocalDateTime.now()),
            request.getTransactionId());
        final var exception = Assertions.assertThrows(DisputeException.class,
            () -> disputeService.createDispute(request,
                USER_ID));

        Assertions.assertEquals(StratosErrorCodeKey.INVALID_DISPUTE_AMOUNT, exception.getErrorCode());
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource(API_SERVICE_CATEGORY)
    void createDisputeForPreArb(
        com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeCategory disputeCategory) {

        Dispute firstLevelDispute = TestDataUtils.getDispute(DisputeType.WALLET_CHARGEBACK,
            com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage.FIRST_LEVEL,
            com.phonepe.merchant.platform.stratos.server.core.models.DisputeCategory.SERVICE_CHARGEBACK);
        firstLevelDispute.setTransactionAmount(100000);
        DisputeWorkflow firstLevelDisputeWorkflow = TestDataUtils.getDisputeWorkflow(
            firstLevelDispute);

        FinancialDisputeWorkflow financialDisputeWorkflow = DisputeWorkflowUtils.getFinancialDisputeWorkflow(
            firstLevelDisputeWorkflow);

        financialDisputeWorkflow.setAcceptedAmount((int) dispute.getTransactionAmount() - 1);
        firstLevelDisputeWorkflow.setCurrentState(DisputeWorkflowState.ACCEPTED_CHARGEBACK);

        disputeService.persistDisputeAndDisputeWorkflow(firstLevelDispute,
            firstLevelDisputeWorkflow);

        var request = TestDataUtils.getWalletDisputeRequest(DisputeStage.PRE_ARBITRATION,
            disputeCategory, firstLevelDispute.getTransactionReferenceId(),
            1);

        createEntrySetupForAPIWithAmount("WALLET", String.valueOf(LocalDateTime.now()),
            request.getTransactionId(), String.valueOf(firstLevelDispute.getTransactionAmount()));
        final var response = disputeService.createDispute(request,
            USER_ID);
        Assertions.assertNotNull(response);
    }
}