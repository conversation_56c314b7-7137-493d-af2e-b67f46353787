package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.netbanking.firstLevel;

import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK;

import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.requests.RefundStatusReconRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.requests.SuspectedFraudChargebackDisputeReconcileRequest;
import com.phonepe.merchant.platform.stratos.server.core.clients.kratos.KratosRecommendedAction;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.services.refund.orchestrator.models.RefundStatus;
import java.util.concurrent.TimeUnit;
import lombok.SneakyThrows;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

public class NBFirstLevelMerchantAcceptedAbsorptionFlowTest extends NBFirstLevelBaseTest {

    @SneakyThrows
    @ParameterizedTest
    @EnumSource(names = {"NOOP", "ALLOW"})
    void testMerchantAcceptedChargebackWithoutFraud(KratosRecommendedAction kratosRecommendedAction) {

        createAndTestTillMerchantAcceptedChargeback(kratosRecommendedAction);

        testTillRefundInitiated(RefundStatus.ACCEPTED);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            disputeWorkflow.getTransactionReferenceId(),
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.CB_REFUND_INITIATED,
            10L, TimeUnit.SECONDS);

        triggerCallBackAndAssertStatus(0);

        testAbsorbtionFlow();
    }

    @ParameterizedTest
    @EnumSource(names = {"NOOP", "ALLOW"})
    void testSuspectedFraudThenNotFraudFlow(KratosRecommendedAction actionAfterSuspected) {

        assertFromCreateToSuspectedFraud();

        MockingUtils.mockKratos(kratosService, actionAfterSuspected);

        //reconcile
        disputeResource.reconcile(new SuspectedFraudChargebackDisputeReconcileRequest());

        AssertionUtils.assertDisputeWorkflowStateEquals(
            disputeWorkflow.getTransactionReferenceId(),
            disputeWorkflow.getDisputeWorkflowId(),
            MERCHANT_ACCEPTED_CHARGEBACK,
            5L, TimeUnit.SECONDS);

        testTillRefundInitiated(RefundStatus.ACCEPTED);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            disputeWorkflow.getTransactionReferenceId(),
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.CB_REFUND_INITIATED,
            10L, TimeUnit.SECONDS);

        triggerCallBackAndAssertStatus(0);

        testAbsorbtionFlow();
    }

    @ParameterizedTest
    @EnumSource(names = {"NOOP", "ALLOW"})
    void retriggerRefundSuccess(KratosRecommendedAction kratosRecommendedAction) {
        testTillRefundFailure(kratosRecommendedAction);

        MockingUtils.setUpRefundResponseMocking(RefundStatus.ACCEPTED.name(),
            disputeWorkflow.getDisputedAmount());

        AssertionUtils.assertDisputeWorkflowStateEquals(
            disputeWorkflow.getTransactionReferenceId(),
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.CB_REFUND_INITIATED,
            30L, TimeUnit.SECONDS);

        triggerCallBackAndAssertStatus(0);

        testAbsorbtionFlow();
    }

    @SneakyThrows
    @ParameterizedTest
    @EnumSource(names = {"NOOP", "ALLOW"})
    void testRetriggerAlreadyRefundExistsSuccess(KratosRecommendedAction kratosRecommendedAction) {

        String merchantTxnId = String.format("CB%s-%d", disputeWorkflow.getTransactionReferenceId(),
            0);
        testTillRefundFailure(kratosRecommendedAction);
        MockingUtils.setUpRefundResponseMocking(RefundStatus.INITIATED.name(),
            disputeWorkflow.getDisputedAmount());
        MockingUtils.setUpRefundStatusMocking(RefundStatus.FAILED.name(),
            disputeWorkflow.getDisputedAmount(), dispute.getMerchantId(), merchantTxnId);

        netBankingChargebackServiceImpl.saveDisputeMeta(disputeWorkflow, merchantTxnId);
        clearAerospike();
        assertTriggerEventWithDelay(
            DisputeWorkflowEvent.INITIATE_CHARGEBACK_REFUND,
            DisputeWorkflowState.CB_REFUND_INITIATED,
            Constants.EMPTY_TRANSITION_CONTEXT, 20L
        );
        triggerCallBackAndAssertStatus(0);

        testAbsorbtionFlow();

    }

    @ParameterizedTest
    @EnumSource(names = {"NOOP", "ALLOW"})
    void testRetriggerWhenPreviousRefundInAcceptedState(KratosRecommendedAction kratosRecommendedAction) {

        String merchantTxnId = String.format("CB%s-%d", disputeWorkflow.getTransactionReferenceId(),
            0);

        testTillRefundFailure(kratosRecommendedAction);

        netBankingChargebackServiceImpl.saveDisputeMeta(disputeWorkflow, merchantTxnId);

        MockingUtils.setUpRefundStatusMocking(RefundStatus.ACCEPTED.name(),
            disputeWorkflow.getDisputedAmount(), dispute.getMerchantId(), merchantTxnId);

        clearAerospike();

        assertTriggerEventWithDelay(DisputeWorkflowEvent.INITIATE_CHARGEBACK_REFUND, DisputeWorkflowState.CB_REFUND_ACCEPTED,
            Constants.EMPTY_TRANSITION_CONTEXT, 20L);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            disputeWorkflow.getTransactionReferenceId(),
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.CB_REFUND_ACCEPTED,
            30L, TimeUnit.SECONDS);

        testAbsorbtionFlow();
    }

    @ParameterizedTest
    @EnumSource(names = {"NOOP", "ALLOW"})
    void testReconcileRefundFlowWithoutFraud(KratosRecommendedAction kratosRecommendedAction) {

        createAndTestTillMerchantAcceptedChargeback(kratosRecommendedAction);

        testTillRefundInitiated(RefundStatus.ACCEPTED);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            disputeWorkflow.getTransactionReferenceId(),
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.CB_REFUND_INITIATED,
            10L, TimeUnit.SECONDS);

        String merchantTxnId = String.format("CB%s-%d", disputeWorkflow.getTransactionReferenceId(),
            0);

        MockingUtils.setUpRefundStatusMocking(RefundStatus.ACCEPTED.name(),
            disputeWorkflow.getDisputedAmount(), dispute.getMerchantId(), merchantTxnId);

        RefundStatusReconRequest refundStatusReconRequest = new RefundStatusReconRequest();

        disputeResource.reconcile(refundStatusReconRequest);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            disputeWorkflow.getTransactionReferenceId(),
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.CB_REFUND_ACCEPTED,
            10L, TimeUnit.SECONDS);

    }
}
