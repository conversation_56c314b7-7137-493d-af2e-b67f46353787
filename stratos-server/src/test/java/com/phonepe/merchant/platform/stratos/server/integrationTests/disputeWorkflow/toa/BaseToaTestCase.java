package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.toa;

import com.phonepe.merchant.platform.stratos.server.BaseTest;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.ChargebackBaseTestCase;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.resources.ToaResource;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import org.junit.jupiter.api.BeforeEach;

public abstract class BaseToaTestCase extends ChargebackBaseTestCase {


    @BeforeEach
    void setUpGuiceInjection() {
        MockingUtils.setupEventIngestionApiMocking();
        truncateDb();
    }

    @Override
    protected abstract DisputeStage getDisputeStage();

    @Override
    protected abstract DisputeType getDisputeType();
}
