package com.phonepe.merchant.platform.stratos.server.integrationTests.row;

import com.phonepe.merchant.platform.stratos.models.commons.DateRange;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.filters.DateRangeFilter;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.models.row.PgMisRowDto;
import com.phonepe.merchant.platform.stratos.models.row.RowTransactionType;
import com.phonepe.merchant.platform.stratos.models.row.RowTypeDto;
import com.phonepe.merchant.platform.stratos.models.row.requests.PgMisRowOffsetRequest;
import com.phonepe.merchant.platform.stratos.models.row.requests.RowHistoryRequest;
import com.phonepe.merchant.platform.stratos.server.BaseTest;
import com.phonepe.merchant.platform.stratos.server.LoadOnlyOnClassLevelBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.models.RowState;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.PgMisRowMessage;
import com.phonepe.merchant.platform.stratos.server.core.services.RowService;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.pg.row.PgMisRowProcessor;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

public class PgMisOffsetTest extends LoadOnlyOnClassLevelBaseTest {

    private RowService rowService;
    private PgMisRowProcessor pgMisRowProcessor;


    @BeforeEach
    void setUpGuiceInjection() {
        rowService = guiceInjector.getInstance(RowService.class);
        pgMisRowProcessor = guiceInjector.getInstance(PgMisRowProcessor.class);
        MARIA_DB_CONTAINER.executeQuery("""
            TRUNCATE stratos_shard_1.file;
            TRUNCATE stratos_shard_2.file;
            TRUNCATE stratos_shard_1.file_audit;
            TRUNCATE stratos_shard_2.file_audit;
            TRUNCATE stratos_shard_1.row;
            TRUNCATE stratos_shard_2.row;
            TRUNCATE stratos_shard_1.row_audit;
            TRUNCATE stratos_shard_2.row_audit;""", true);
    }

    @Test
    void testSuccess() {
        final var pgTransactionId = IdGenerator.generate("PG").getId();

        //debit row
        Assertions.assertThrows(DisputeException.class,
            () -> pgMisRowProcessor.process(getPgMisRowMessage(pgTransactionId,
                RowTransactionType.CHARGEBACK, 1.00, "mis_reports_file1")));

        AssertionUtils.assertRowStateEquals(RowState.FAILED, "mis_reports_file1", 5,
            TimeUnit.SECONDS);

        var debitRowResponse = (PgMisRowDto) rowService.history(RowHistoryRequest.builder()
            .rowTypes(Set.of(RowTypeDto.PG_MIS_ROW))
            .dateRangeFilter(DateRangeFilter.builder()
                .dateRange(DateRange.builder().startDate(LocalDate.now().minusDays(2))
                    .endDate(LocalDate.now())
                    .build())
                .build())
            .build()).getRows().get(0);

        //credit row
        Assertions.assertThrows(DisputeException.class,
            () -> pgMisRowProcessor.process(getPgMisRowMessage(pgTransactionId,
                RowTransactionType.CHARGEBACK_REVERSED, 1.00, "mis_reports_file2")));

        AssertionUtils.assertRowStateEquals(RowState.FAILED, "mis_reports_file2", 15,
            TimeUnit.SECONDS);

        var creditRowResponse = rowService.history(RowHistoryRequest.builder()
                .rowTypes(Set.of(RowTypeDto.PG_MIS_ROW))
                .dateRangeFilter(DateRangeFilter.builder()
                    .dateRange(DateRange.builder()
                        .startDate(LocalDate.now().minusDays(2))
                        .endDate(LocalDate.now())
                        .build())
                    .build())
                .build()).getRows().stream()
            .map(PgMisRowDto.class::cast)
            .filter(pgMisRowDto -> pgMisRowDto.getTransactionType()
                == RowTransactionType.CHARGEBACK_REVERSED)
            .collect(Collectors.toList()).get(0);

        rowService.offsetPgMisRow(PgMisRowOffsetRequest.builder()
            .debitRowId(debitRowResponse.getRowId())
            .debitSourceId(debitRowResponse.getSourceId())
            .creditRowId(creditRowResponse.getRowId())
            .creditSourceId(creditRowResponse.getSourceId()).build());

        AssertionUtils.assertRowStateEquals(RowState.PROCESSED, "mis_reports_file1", 15,
            TimeUnit.SECONDS);

        AssertionUtils.assertRowStateEquals(RowState.PROCESSED, "mis_reports_file2", 15,
            TimeUnit.SECONDS);
    }


    @Test
    void testDifferentAmount() {
        final var pgTransactionId = IdGenerator.generate("PG").getId();

        //debit row
        Assertions.assertThrows(DisputeException.class,
            () -> pgMisRowProcessor.process(getPgMisRowMessage(pgTransactionId,
                RowTransactionType.CHARGEBACK, 1.00, "mis_reports_file3")));

        AssertionUtils.assertRowStateEquals(RowState.FAILED, "mis_reports_file3", 15,
            TimeUnit.SECONDS);

        var debitRowResponse = (PgMisRowDto) rowService.history(RowHistoryRequest.builder()
            .rowTypes(Set.of(RowTypeDto.PG_MIS_ROW))
            .dateRangeFilter(DateRangeFilter.builder()
                .dateRange(DateRange.builder()
                    .startDate(LocalDate.now().minusDays(2))
                    .endDate(LocalDate.now())
                    .build())
                .build())
            .build()).getRows().get(0);

        //credit row
        Assertions.assertThrows(DisputeException.class,
            () -> pgMisRowProcessor.process(getPgMisRowMessage(pgTransactionId,
                RowTransactionType.CHARGEBACK_REVERSED, 2.00, "mis_reports_file4")));

        AssertionUtils.assertRowStateEquals(RowState.FAILED, "mis_reports_file4", 15,
            TimeUnit.SECONDS);

        var creditRowResponse = rowService.history(RowHistoryRequest.builder()
                .rowTypes(Set.of(RowTypeDto.PG_MIS_ROW))
                .dateRangeFilter(DateRangeFilter.builder()
                    .dateRange(DateRange.builder()
                        .startDate(LocalDate.now().minusDays(2))
                        .endDate(LocalDate.now())
                        .build())
                    .build())
                .build()).getRows().stream()
            .map(PgMisRowDto.class::cast)
            .filter(pgMisRowDto -> pgMisRowDto.getTransactionType()
                == RowTransactionType.CHARGEBACK_REVERSED)
            .collect(Collectors.toList()).get(0);

        var stratosError = Assertions.assertThrows(DisputeException.class,
            () -> rowService.offsetPgMisRow(PgMisRowOffsetRequest.builder()
                .debitRowId(debitRowResponse.getRowId())
                .debitSourceId(debitRowResponse.getSourceId())
                .creditRowId(creditRowResponse.getRowId())
                .creditSourceId(creditRowResponse.getSourceId()).build()));

        Assertions.assertEquals(StratosErrorCodeKey.WRONG_INPUT_ERROR, stratosError.getErrorCode());

        AssertionUtils.assertRowStateEquals(RowState.FAILED, "mis_reports_file3", 15,
            TimeUnit.SECONDS);

        AssertionUtils.assertRowStateEquals(RowState.FAILED, "mis_reports_file4", 15,
            TimeUnit.SECONDS);
    }

    @Test
    void testDifferentTransactionId() {
        final var pgTransactionId1 = IdGenerator.generate("PG").getId();
        final var pgTransactionId2 = IdGenerator.generate("PG").getId();

        //debit row
        Assertions.assertThrows(DisputeException.class,
            () -> pgMisRowProcessor.process(getPgMisRowMessage(pgTransactionId1,
                RowTransactionType.CHARGEBACK, 1.00, "mis_reports_file5")));

        AssertionUtils.assertRowStateEquals(RowState.FAILED, "mis_reports_file5", 15,
            TimeUnit.SECONDS);

        var debitRowResponse = (PgMisRowDto) rowService.history(RowHistoryRequest.builder()
            .rowTypes(Set.of(RowTypeDto.PG_MIS_ROW))
            .dateRangeFilter(DateRangeFilter.builder()
                .dateRange(DateRange.builder()
                    .startDate(LocalDate.now().minusDays(2))
                    .endDate(LocalDate.now())
                    .build())
                .build())
            .build()).getRows().get(0);

        //credit row
        Assertions.assertThrows(DisputeException.class,
            () -> pgMisRowProcessor.process(getPgMisRowMessage(pgTransactionId2,
                RowTransactionType.CHARGEBACK_REVERSED, 1.00, "mis_reports_file6")));

        AssertionUtils.assertRowStateEquals(RowState.FAILED, "mis_reports_file6", 15,
            TimeUnit.SECONDS);

        var creditRowResponse = rowService.history(RowHistoryRequest.builder()
                .rowTypes(Set.of(RowTypeDto.PG_MIS_ROW))
                .dateRangeFilter(DateRangeFilter.builder()
                    .dateRange(DateRange.builder().startDate(LocalDate.now().minusDays(2))
                        .endDate(LocalDate.now())
                        .build())
                    .build())
                .build()).getRows().stream()
            .map(PgMisRowDto.class::cast)
            .filter(pgMisRowDto -> pgMisRowDto.getTransactionType()
                == RowTransactionType.CHARGEBACK_REVERSED)
            .collect(Collectors.toList()).get(0);

        var stratosError = Assertions.assertThrows(DisputeException.class,
            () -> rowService.offsetPgMisRow(PgMisRowOffsetRequest.builder()
                .debitRowId(debitRowResponse.getRowId())
                .debitSourceId(debitRowResponse.getSourceId())
                .creditRowId(creditRowResponse.getRowId())
                .creditSourceId(creditRowResponse.getSourceId()).build()));

        Assertions.assertEquals(StratosErrorCodeKey.WRONG_INPUT_ERROR, stratosError.getErrorCode());

        AssertionUtils.assertRowStateEquals(RowState.FAILED, "mis_reports_file5", 15,
            TimeUnit.SECONDS);

        AssertionUtils.assertRowStateEquals(RowState.FAILED, "mis_reports_file6", 15,
            TimeUnit.SECONDS);
    }


    public PgMisRowMessage getPgMisRowMessage(String instrumentTxnId,
        RowTransactionType transactionType,
        Double amount, String fileId) {
        return PgMisRowMessage.builder()
            .sourceId(fileId)
            .autoRefund(false)
            .bankCode("tmp bank code")
            .bankTransactionId("tmp txn id")
            .cgst(new BigDecimal(0))
            .charges(new BigDecimal(0))
            .igst(new BigDecimal(0))
            .mid("mid 1234")
            .instrumentType("abcd")
            .netAmount(new BigDecimal(amount))
            .paymentDate(new Date(1))
            .pgId("pg")
            .pgTransactionId("pmtmp")
            .sgst(new BigDecimal(0))
            .transactionAmount(new BigDecimal(amount))
            .transactionId(instrumentTxnId)
            .transactionDate(new Date(1))
            .transactionType(transactionType)
            .interchange("HDFC")
            .rowId(IdGenerator.generate("ROW").getId())
            .build();
    }


}
