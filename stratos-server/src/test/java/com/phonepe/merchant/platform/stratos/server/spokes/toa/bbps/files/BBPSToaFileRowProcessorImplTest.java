package com.phonepe.merchant.platform.stratos.server.spokes.toa.bbps.files;

import com.phonepe.merchant.platform.stratos.server.core.handlebars.HandleBarsService;
import com.phonepe.merchant.platform.stratos.server.core.helpers.id.IdHelper;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.MerchantMandateService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;

@ExtendWith(MockitoExtension.class)
public class BBPSToaFileRowProcessorImplTest {

    @Mock
    private HandleBarsService handleBarsService;
    @Mock
    private DisputeService disputeService;
    @Mock
    private MerchantMandateService merchantMandateService;
    @Mock
    private IdHelper idHelper;
    private BBPSToaFileRowProcessorImpl bbpsToaFileRowProcessor;

    @BeforeEach
    public void setup() {
        bbpsToaFileRowProcessor = new BBPSToaFileRowProcessorImpl(handleBarsService, disputeService,
                merchantMandateService, idHelper, new HashMap<>()); // need to fix
    }

    @Test
    public void testEnrichAndGetDisputeWorkflow() {
        final FinancialDisputeWorkflow inputDisputeWorkflow = FinancialDisputeWorkflow.builder()
                .transactionReferenceId("T123")
                .disputeStage(DisputeStage.FIRST_LEVEL)
                .disputedAmount(100000)
                .build();
        final DisputeWorkflow outputDisputeWorkflow = bbpsToaFileRowProcessor
                .enrichAndGetDisputeWorkflow(null, inputDisputeWorkflow, "sample-file-id",
                        DisputeType.BBPS_TAT_BREACH_TOA, null, "sample-dispute-id");
        Assertions.assertEquals(inputDisputeWorkflow.getTransactionReferenceId(), outputDisputeWorkflow.getTransactionReferenceId());
        Assertions.assertEquals(DisputeType.BBPS_TAT_BREACH_TOA, outputDisputeWorkflow.getDisputeType());
        Assertions.assertEquals(100000, outputDisputeWorkflow.getDisputedAmount());
    }

    @Test
    public void testGetDispute() {
        final FinancialDisputeWorkflow inputDisputeWorkflow = FinancialDisputeWorkflow.builder()
                .transactionReferenceId("T123")
                .disputeStage(DisputeStage.FIRST_LEVEL)
                .disputedAmount(100000)
                .build();
        final Dispute inputDispute = Dispute.builder()
                .merchantTransactionId("NX123")
                .transactionReferenceId("T123")
                .merchantId("sample_merchant_id")
                .transactionAmount(100000)
                .build();
        final Dispute outputDispute = bbpsToaFileRowProcessor.getDispute(inputDisputeWorkflow,
                null, inputDispute, DisputeType.BBPS_TAT_BREACH_TOA, null);
        Assertions.assertEquals(inputDispute.getTransactionReferenceId(), outputDispute.getTransactionReferenceId());
    }
}
