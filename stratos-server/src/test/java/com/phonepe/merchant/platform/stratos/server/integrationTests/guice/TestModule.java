package com.phonepe.merchant.platform.stratos.server.integrationTests.guice;

import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.StratosConfiguration;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import io.appform.ranger.discovery.bundle.ServiceDiscoveryBundle;
import io.dropwizard.setup.Environment;
import lombok.RequiredArgsConstructor;
import org.apache.curator.framework.CuratorFramework;

@RequiredArgsConstructor
public class TestModule extends AbstractModule {

    private final Environment environment;

    private final StratosConfiguration stratosConfiguration;

    private final ServiceDiscoveryBundle<StratosConfiguration> serviceDiscoveryBundle;

    @Provides
    public Environment provideEnvironment() {
        return environment;
    }

    @Provides
    public CuratorFramework curatorFramework() {
        return serviceDiscoveryBundle.getCurator();
    }

    @Provides
    public StratosConfiguration provideStratosConfiguration() {
        return stratosConfiguration;
    }

    @Provides
    @Singleton
    public ServiceEndpointProviderFactory serviceEndpointProviderFactory(final StratosConfiguration stratosConfiguration,
        final Environment environment) {
        return new ServiceEndpointProviderFactory(serviceDiscoveryBundle.getCurator(),
            stratosConfiguration.getRangerHubConfiguration(), environment, null,null,null);
    }


}
