package com.phonepe.merchant.platform.stratos.server.integrationTests.row;

import com.phonepe.merchant.platform.stratos.models.commons.DateRange;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.filters.DateRangeFilter;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeTypeDto;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.models.files.FileTypeDto;
import com.phonepe.merchant.platform.stratos.models.row.PgMisRowDto;
import com.phonepe.merchant.platform.stratos.models.row.RowTransactionType;
import com.phonepe.merchant.platform.stratos.models.row.RowTypeDto;
import com.phonepe.merchant.platform.stratos.models.row.requests.RowHistoryRequest;
import com.phonepe.merchant.platform.stratos.server.LoadOnlyOnClassLevelBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.RowState;
import com.phonepe.merchant.platform.stratos.server.core.resources.FileResource;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.RowService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.olympus.im.models.security.ServiceUserPrincipal;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import java.time.LocalDate;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import org.glassfish.jersey.media.multipart.FormDataContentDisposition;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;


public class PgMisRowProcessingIntegrationTest extends LoadOnlyOnClassLevelBaseTest {

    private DisputeService disputeService;
    private FileResource fileResource;
    private RowService rowService;
    private OlympusIMClient olympusIMClient;
    private ServiceUserPrincipal serviceUserPrincipal;

    @BeforeEach
    void setUpGuiceInjection() {
        rowService = guiceInjector.getInstance(RowService.class);
        disputeService = guiceInjector.getInstance(DisputeService.class);
        fileResource = guiceInjector.getInstance(FileResource.class);
        olympusIMClient = guiceInjector.getInstance(OlympusIMClient.class);
        MARIA_DB_CONTAINER.executeQuery("TRUNCATE stratos_shard_1.file;\n"
            + "TRUNCATE stratos_shard_2.file;\n"
            + "TRUNCATE stratos_shard_1.file_audit;\n"
            + "TRUNCATE stratos_shard_2.file_audit;\n"
            + "TRUNCATE stratos_shard_1.row;\n"
            + "TRUNCATE stratos_shard_2.row;\n"
            + "TRUNCATE stratos_shard_1.row_audit;\n"
            + "TRUNCATE stratos_shard_2.row_audit;", true);

        serviceUserPrincipal  = TestDataUtils.getOlympusUser();
    }

    @Test
    public void testPgMisRowProcessingTest() throws Exception {
        final var transactionReferenceId = IdGenerator.generate("T").getId();
        final var pgTransactionId = IdGenerator.generate("PG").getId();

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForCreateChargebackApiMocking(
            TestDataUtils.pgFirstLevelChargebackCSVFileStreamFromPgTransactionId(pgTransactionId));
        MockingUtils.setupPgTransportTransactionDetailMocking(pgTransactionId,
            transactionReferenceId);
        Mockito.doReturn(true).when(olympusIMClient).verifyPermission(Mockito.any(), Mockito.any());

        MockingUtils.setupPaymentsApiNoRefundExistsMockingPaidFromDebitCard(
            transactionReferenceId, 100);
        MockingUtils.setupEventIngestionApiMocking();

        final var userAuthDetails = TestDataUtils.getOlympusAuthDetails();
        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        var response = fileResource.upload(DisputeTypeDto.PG_CHARGEBACK,
            FileTypeDto.PG_FIRST_LEVEL,
            TestDataUtils.pgFirstLevelChargebackCSVFileStreamFromPgTransactionId(pgTransactionId),
            FormDataContentDisposition.name("randomName").fileName("file- 20-12-2021.csv")
                .build(), "AuthToken",serviceUserPrincipal);

        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);

        final var disputeWorkflow = disputeService.
            getDisputeWorkflow(transactionReferenceId, DisputeType.PG_CHARGEBACK,
                DisputeStage.FIRST_LEVEL);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.REFUND_BLOCKED,
            5L, TimeUnit.SECONDS);

        disputeService.triggerEvent(
            userAuthDetails,
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK,
            Constants.EMPTY_TRANSITION_CONTEXT);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK);

        disputeService.triggerEvent(
            userAuthDetails,
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowEvent.COMPLETE_PG_ACCEPTANCE,
            Constants.EMPTY_TRANSITION_CONTEXT);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.PG_ACCEPTANCE_COMPLETED);

        rowService.processSignal(TestDataUtils.getPgMisReportRequest(transactionReferenceId,
            RowTransactionType.CHARGEBACK, 1.20, "mis_reports_file1"));

        AssertionUtils.assertRowStateEquals(RowState.FAILED, "mis_reports_file1", 15,
            TimeUnit.SECONDS);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.PG_ACCEPTANCE_COMPLETED,
            30L, TimeUnit.SECONDS);

        rowService.processSignal(TestDataUtils.getPgMisReportRequest(transactionReferenceId,
            RowTransactionType.CHARGEBACK, 1.00, "mis_reports_file2"));

        AssertionUtils.assertRowStateEquals(RowState.PROCESSED, "mis_reports_file2", 15,
            TimeUnit.SECONDS);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.DEBIT_RECEIVED,
            30L, TimeUnit.SECONDS);

        rowService.processSignal(TestDataUtils.getPgMisReportRequest(transactionReferenceId,
            RowTransactionType.CHARGEBACK_REVERSED, 1.00, "mis_reports_file3"));

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.DEBIT_RECEIVED,
            30L, TimeUnit.SECONDS);

        AssertionUtils.assertRowStateEquals(RowState.FAILED, "mis_reports_file3", 15,
            TimeUnit.SECONDS);

        var pgMisRowResponse = rowService.history(RowHistoryRequest.builder()
            .rowTypes(Set.of(RowTypeDto.PG_MIS_ROW))
            .dateRangeFilter(DateRangeFilter.builder()
                .dateRange(DateRange.builder().startDate(LocalDate.now().minusDays(2))
                    .endDate(LocalDate.now())
                    .build())
                .build())
            .build());

        Assertions.assertEquals(3, pgMisRowResponse.getRows().size());
        Assertions.assertEquals("HDFC",
            ((PgMisRowDto) pgMisRowResponse.getRows().get(0)).getInterchange());
    }

    @Test
    void testMerchantChargebackAcceptedToDebitReceived() throws Exception {
        final var transactionReferenceId = IdGenerator.generate("T").getId();
        final var pgTransactionId = IdGenerator.generate("PG").getId();

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForCreateChargebackApiMocking(
            TestDataUtils.pgFirstLevelChargebackCSVFileStreamFromPgTransactionId(pgTransactionId));
        MockingUtils.setupPgTransportTransactionDetailMocking(pgTransactionId,
            transactionReferenceId);

        MockingUtils.setupPaymentsApiNoRefundExistsMockingPaidFromDebitCard(
            transactionReferenceId, 100);
        MockingUtils.setupEventIngestionApiMocking();

        var response = fileResource.upload(DisputeTypeDto.PG_CHARGEBACK,
            FileTypeDto.PG_FIRST_LEVEL,
            TestDataUtils.pgFirstLevelChargebackCSVFileStreamFromPgTransactionId(pgTransactionId),
            FormDataContentDisposition.name("randomName").fileName("file- 20-12-2021.csv")
                .build(), "AuthToken",serviceUserPrincipal);

        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);

        final var disputeWorkflow = disputeService.
            getDisputeWorkflow(transactionReferenceId, DisputeType.PG_CHARGEBACK,
                DisputeStage.FIRST_LEVEL);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.REFUND_BLOCKED,
            5L, TimeUnit.SECONDS);

        disputeService.triggerEvent(serviceUserPrincipal.getUserAuthDetails(),
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK,
            Constants.EMPTY_TRANSITION_CONTEXT);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK);

        disputeService.triggerEvent(Constants.STRATOS_SYSTEM_USER_OLYMPUS,
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowEvent.MERCHANT_ACCEPTED_CHARGEBACK_TO_DEBIT_RECEIVED,
            TestDataUtils
                .getDebitTransitionContext(disputeWorkflow.getDisputedAmount()));

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.DEBIT_RECEIVED);

    }

    @Test
    void testMisRowReplayWithMisSignalInMerchantAcceptedChargebackState() throws Exception {
        final var transactionReferenceId = IdGenerator.generate("T").getId();
        final var pgTransactionId = IdGenerator.generate("PG").getId();

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForCreateChargebackApiMocking(
            TestDataUtils.pgFirstLevelChargebackCSVFileStreamFromPgTransactionId(pgTransactionId));
        MockingUtils.setupPgTransportTransactionDetailMocking(pgTransactionId,
            transactionReferenceId);

        MockingUtils.setupPaymentsApiNoRefundExistsMockingPaidFromDebitCard(
            transactionReferenceId, 100);

        var response = fileResource.upload(DisputeTypeDto.PG_CHARGEBACK,
            FileTypeDto.PG_FIRST_LEVEL,
            TestDataUtils.pgFirstLevelChargebackCSVFileStreamFromPgTransactionId(pgTransactionId),
            FormDataContentDisposition.name("randomName").fileName("file- 20-12-2021.csv")
                .build(), "AuthToken",serviceUserPrincipal);

        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);

        final var disputeWorkflow = disputeService.
            getDisputeWorkflow(transactionReferenceId, DisputeType.PG_CHARGEBACK,
                DisputeStage.FIRST_LEVEL);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.REFUND_BLOCKED,
            5L, TimeUnit.SECONDS);

        disputeService.triggerEvent(serviceUserPrincipal.getUserAuthDetails(),
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK,
            Constants.EMPTY_TRANSITION_CONTEXT);

        // Mis Row Signal Received
        rowService.processSignal(TestDataUtils.getPgMisReportRequest(transactionReferenceId,
            RowTransactionType.CHARGEBACK, 1.00, "mis_reports_file"));

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.DEBIT_RECEIVED,
            5L, TimeUnit.SECONDS);
    }

    @Test
    void testWithInvalidTransactionID(){
        final var transactionReferenceId = IdGenerator.generate("T").getId();

        final var thrown = Assertions.assertThrows(DisputeException.class,
                () -> rowService.processSignal(TestDataUtils.getPgMisReportRequest(transactionReferenceId,
                        RowTransactionType.CHARGEBACK, 1.00, "mis_reports_file")));
        Assertions.assertEquals(StratosErrorCodeKey.INVALID_TRANSACTION,
                thrown.getErrorCode());
    }
    @Test
    void testWithInvalidTransactionResponse(){
        final var transactionReferenceId = IdGenerator.generate("T").getId();
        MockingUtils.setupPaymentsApiWithFailureResponse(transactionReferenceId);
        final var thrown = Assertions.assertThrows(DisputeException.class,
                () -> rowService.processSignal(TestDataUtils.getPgMisReportRequest(transactionReferenceId,
                        RowTransactionType.CHARGEBACK, 1.00, "mis_reports_file")));
        Assertions.assertEquals(StratosErrorCodeKey.INVALID_TRANSACTION,
                thrown.getErrorCode());
    }
}
