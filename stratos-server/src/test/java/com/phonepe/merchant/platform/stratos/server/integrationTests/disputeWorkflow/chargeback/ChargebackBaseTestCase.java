package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback;

import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.ABSORB_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.APPROVE_ABSORB_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.APPROVE_RECOVER_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.APPROVE_REVERSAL_OF_RECOVERED_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_NPCI_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.MAX_RETRY_TO_PROCESSED_EXTERNALLY;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.NO_RESPONSE_FROM_MERCHANT_WITHIN_TTL;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.RECEIVE_CREDIT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.RECEIVE_DEBIT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.RECEIVE_FULFILMENT_DOCUMENTS;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.RECEIVE_PARTIAL_CREDIT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.RECEIVE_PARTIAL_DEBIT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.RECEIVE_PARTIAL_FULFILMENT_DOCUMENTS;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.REJECT_ABSORB_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.REJECT_RECOVER_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.REJECT_REVERSAL_OF_RECOVERED_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.REQUEST_ABSORB_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.REQUEST_RECOVER_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.REQUEST_REVERSAL_OF_RECOVERED_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.RESET_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.REVERSE_ABSORB_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.ABSORB_CHARGEBACK_APPROVED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.ABSORB_CHARGEBACK_REJECTED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.ABSORB_CHARGEBACK_REQUESTED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.ACCEPTED_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.CB_REFUND_INITIATED_COMPLETED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.CB_REFUND_PROCESSED_EXTERNALLY;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.CHARGEBACK_ABSORBED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.CHARGEBACK_ABSORB_REVERSED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.CHARGEBACK_CANCELLED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.CREDIT_RECEIVED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.DEBIT_RECEIVED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.INTERNAL_MID_REPRESENTMENT_REQUIRED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.MERCHANT_NOT_RESPONDED_WITHIN_TTL;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.NPCI_ACK_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.NPCI_REPRESENTMENT_COMPLETED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.PARTIAL_CREDIT_RECEIVED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.PARTIAL_DEBIT_RECEIVED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_ACCEPTED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.RECOVER_CHARGEBACK_REJECTED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.RECOVER_CHARGEBACK_REQUESTED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.REFUND_BLOCKED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.REJECTED_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.REPRESENTMENT_REQUIRED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT_ACCEPTED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_REQUESTED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.SUSPECTED_FRAUD;

import com.phonepe.merchant.platform.stratos.models.commons.TransitionContext;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.CommentContext;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.PartialAcceptanceTransitionContext;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.RefundContext;
import com.phonepe.merchant.platform.stratos.models.disputes.wallet.RefundReceiverInstrument;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.LoadOnlyOnClassLevelBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.TransitionLockCommand;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.keys.TransitionLockKey;
import com.phonepe.merchant.platform.stratos.server.core.clients.kratos.KratosRecommendedAction;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeCategory;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.resources.CallBackResource;
import com.phonepe.merchant.platform.stratos.server.core.resources.DisputeResource;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.resources.ChargebackResource;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.olympus.im.models.security.ServiceUserPrincipal;
import com.phonepe.services.refund.orchestrator.models.RefundStatus;
import com.phonepe.services.refund.orchestrator.models.v1.RefundResponseVersion;
import com.phonepe.services.refund.orchestrator.models.v1.payer.RefundResponseV2;
import java.io.ByteArrayInputStream;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.params.provider.Arguments;
import org.mockito.Mockito;

@Slf4j
public abstract class ChargebackBaseTestCase extends LoadOnlyOnClassLevelBaseTest {


    protected DisputeService disputeService;
    protected DisputeResource disputeResource;
    protected Dispute dispute;
    protected DisputeWorkflow disputeWorkflow;
    protected OlympusIMClient olympusIMClient;
    protected ServiceUserPrincipal serviceUserPrincipal;
    protected ChargebackResource chargebackResource;
    protected CallBackResource callbackResource;


    public static final String CATEGORY_ARGS = "provideArgumentWithChargebackCategories";
    public static final String PENALTY_AMOUNT_ARGS = "provideArgumentWithPenaltyAmount";
    public static final String API_DTO_CATEGORY_ARGS = "provideArgumentWithChargebackDtoCategoriesForApiBased";
    public static final String API_CATEGORY_ARGS = "provideArgumentWithChargebackCategoriesForApiBased";
    public static final String API_SERVICE_CATEGORY = "provideArgumentWithServiceCategoryForApiBased";
    public static final String DISPUTE_CATEGORY_ARGS = "provideArgumentWithCategoryForApiBased";

    static Stream<Arguments> provideArgumentWithChargebackDtoCategoriesForApiBased() {
        return Stream.of(
            Arguments.of(
                com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeCategory.SERVICE),
            Arguments.of(
                com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeCategory.FRAUD)
        );
    }

    static Stream<Arguments> provideArgumentWithChargebackCategoriesForApiBased() {
        return Stream.of(
            Arguments.of(DisputeCategory.SERVICE_CHARGEBACK),
            Arguments.of(DisputeCategory.FRAUD_CHARGEBACK)
        );
    }

    static Stream<Arguments> provideArgumentWithServiceCategoryForApiBased() {
        return Stream.of(
            Arguments.of(
                com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeCategory.SERVICE)
        );
    }

    static Stream<Arguments> provideArgumentWithCategoryForApiBased() {
        return Stream.of(
            Arguments.of(
                com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeCategory.SERVICE),
            Arguments.of(
                com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeCategory.FRAUD)
        );
    }

    static Stream<Arguments> provideArgumentWithServiceCategory() {
        return Stream.of(
            Arguments.of(
                DisputeCategory.SERVICE_CHARGEBACK)
        );
    }

    static Stream<Arguments> provideArgumentWithChargebackCategories() {
        return Stream.of(
            Arguments.of(DisputeCategory.SERVICE_CHARGEBACK),
            Arguments.of(DisputeCategory.FRAUD_CHARGEBACK),
            Arguments.of(DisputeCategory.DIFFERED_CHARGEBACK),
            Arguments.of(DisputeCategory.DUPLICATE_CHARGEBACK)
        );
    }

    static Stream<Arguments> provideArgumentForFraNoActionAndPenaltyAmount() {
        return Stream.of(
            Arguments.of(KratosRecommendedAction.NOOP, 0L),
            Arguments.of(KratosRecommendedAction.NOOP, 50L),
            Arguments.of(KratosRecommendedAction.ALLOW, 0L),
            Arguments.of(KratosRecommendedAction.ALLOW, 50L)
        );
    }


    static Stream<Arguments> provideArgumentWithPenaltyAmount() {
        return Stream.of(
            Arguments.of(0L),
            Arguments.of(50L)
        );
    }

    @BeforeEach
    void initBeforeEach() {
        truncateDb();

        disputeService = guiceInjector.getInstance(DisputeService.class);
        disputeResource = guiceInjector.getInstance(DisputeResource.class);
        olympusIMClient = guiceInjector.getInstance(OlympusIMClient.class);
        chargebackResource = guiceInjector.getInstance(ChargebackResource.class);
        callbackResource = guiceInjector.getInstance(CallBackResource.class);
        final var olympusIMClient = guiceInjector.getInstance(OlympusIMClient.class);

        // default is set to merchant ops
        serviceUserPrincipal = MockingUtils.getAndMockMerchantOpsUser(olympusIMClient);

        dispute = TestDataUtils
            .getServiceChargeBackDispute(getDisputeType(), getDisputeStage());
        disputeWorkflow = TestDataUtils.getDisputeWorkflow(dispute);

        baseTestSetup();
    }

    protected void clearAerospike() {
        final TransitionLockCommand transitionLockCommand = guiceInjector.getInstance(
            TransitionLockCommand.class);
        transitionLockCommand.delete(TransitionLockKey.builder()
            .transitionKey(disputeWorkflow.getDisputeWorkflowId())
            .build());
    }

    protected void baseTestSetup() {

    }

    protected abstract DisputeStage getDisputeStage();

    protected abstract DisputeType getDisputeType();

    protected void assertRefundBlock() {
        // Assert Status of Dispute Workflow to be Representment Required
        AssertionUtils.assertDisputeWorkflowStateEquals(
            dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            REFUND_BLOCKED,
            20L, TimeUnit.SECONDS);

        // Test Allowed Transitions from Current State
        final var permissibleEvents = Set
            .of(RECEIVE_FULFILMENT_DOCUMENTS,
                RECEIVE_PARTIAL_FULFILMENT_DOCUMENTS,
                MERCHANT_ACCEPT_CHARGEBACK,
                NO_RESPONSE_FROM_MERCHANT_WITHIN_TTL);

        final var upcomingEvents = disputeResource
            .getUpcomingEvents(serviceUserPrincipal, disputeWorkflow.getDisputeType(),
                disputeWorkflow.getDisputeStage(), disputeWorkflow.getDisputeWorkflowVersion(),
                REFUND_BLOCKED);
        Assertions.assertEquals(permissibleEvents, upcomingEvents);
    }


    protected void assertTriggerEvent(final DisputeWorkflowEvent event,
        final DisputeWorkflowState state) {
        assertTriggerEvent(event, state, Constants.EMPTY_TRANSITION_CONTEXT);
    }

    protected void assertTriggerEvent(final DisputeWorkflowEvent event,
        final DisputeWorkflowState state, final TransitionContext transitionContext) {

        // Trigger Event
        disputeResource.triggerEvent(
            serviceUserPrincipal,
            disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            event, transitionContext);

        // Fetch The latest Dispute Workflow from DB and Assert it's Current Status
        AssertionUtils.assertDisputeWorkflow(
            disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            state, dispute.getDisputeCategory(), disputeWorkflow.getDisputeType());
    }

    protected void assertTriggerEvent(final DisputeWorkflowEvent event,
        final DisputeWorkflowState state, final TransitionContext transitionContext,
        Dispute dispute, DisputeWorkflow disputeWorkflow) {

        // Trigger Event
        disputeResource.triggerEvent(
            serviceUserPrincipal,
            disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            event, transitionContext);

        // Fetch The latest Dispute Workflow from DB and Assert it's Current Status
        AssertionUtils.assertDisputeWorkflow(
            disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            state, dispute.getDisputeCategory(), disputeWorkflow.getDisputeType());
    }

    protected void assertTriggerEventWithDelay(final DisputeWorkflowEvent event,
        final DisputeWorkflowState state) {
        assertTriggerEventWithDelay(event, state, Constants.EMPTY_TRANSITION_CONTEXT);
    }

    protected void assertTriggerEventWithDelay(final DisputeWorkflowEvent event,
        final DisputeWorkflowState state, final TransitionContext transitionContext) {

        // Trigger Event
        disputeResource.triggerEvent(
            serviceUserPrincipal,
            disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            event, transitionContext);

        // Fetch The latest Dispute Workflow from DB and Assert it's Current Status
        AssertionUtils.assertDisputeWorkflowStateEquals(
            disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            state, 10L, TimeUnit.SECONDS);
    }

    protected void assertTriggerEventWithDelay(final DisputeWorkflowEvent event,
        final DisputeWorkflowState state, final TransitionContext transitionContext,
        final long delay) {

        // Trigger Event
        disputeResource.triggerEvent(serviceUserPrincipal,
            disputeWorkflow.getTransactionReferenceId(),
            disputeWorkflow.getDisputeWorkflowId(),
            event,
            transitionContext);

        // Fetch The latest Dispute Workflow from DB and Assert it's Current Status
        AssertionUtils.assertDisputeWorkflowStateEquals(
            disputeWorkflow.getTransactionReferenceId(),
            disputeWorkflow.getDisputeWorkflowId(),
            state, delay, TimeUnit.SECONDS);

    }

    protected void createEntrySetup(KratosRecommendedAction recommendedAction) {

        // Setup Payment API to respond with Full Refund & RGCS Transaction
        MockingUtils.setupPaymentsApiMockingSubmitFulfillmentDocument(
            dispute.getTransactionReferenceId(),
            disputeWorkflow.getDisputedAmount());

        // Create Dispute Entry in System
        disputeService.createEntry(dispute, disputeWorkflow);

        MockingUtils.mockKratos(kratosService, recommendedAction);

    }

    protected void createEntrySetupForDisputeCategoryNull() {

        // Setup Payment API to respond with Full Refund & RGCS Transaction
        MockingUtils.setupPaymentsApiMockingSubmitFulfillmentDocument(
            dispute.getTransactionReferenceId(),
            disputeWorkflow.getDisputedAmount());

        // Create Dispute Entry in System
        dispute.setDisputeCategory(null);
        disputeService.createEntry(dispute, disputeWorkflow);

    }

    protected void createEntrySetupForAPI(String type, String date, String transactionId) {
        MockingUtils.setupPaymentsApiMockingForWallet(transactionId, disputeWorkflow.getDisputedAmount(), type, date);
    }

    protected void createEntrySetupForAPIException(String transactionId) {
        MockingUtils.setupPaymentsApiMockingForWalletException(transactionId);
    }

    protected void createEntrySetupForAPIForExternalUser(String type, String date, String transactionId) {
        MockingUtils.setupPaymentsApiMockingForWalletForExternalUSer(transactionId, disputeWorkflow.getDisputedAmount(), type, date);
    }

    protected void createEntrySetupForAPIWithAmount(String type, String date, String transactionId, String amount) {
        MockingUtils.setupPaymentsApiMockingForWalletWrongUpiId(transactionId,
            Long.parseLong(amount), type, date);
    }

    protected void createEntrySetupForInternalRail(String type, String date) {
        MockingUtils.setupPaymentsApiMockingForInternalRailWallet(dispute.getTransactionReferenceId(),
            disputeWorkflow.getDisputedAmount(), type, date);
    }

    protected void createEntrySetup() {
        createEntrySetup(KratosRecommendedAction.ALLOW);
    }


    protected void testAbsorbtionFlow() {

        CommentContext commentContext = CommentContext.builder()
            .comment("Chargeback absorb required")
            .build();

        // Merchant Ops requests for Absorb Chargeback with Comment
        assertTriggerEvent(REQUEST_ABSORB_CHARGEBACK, ABSORB_CHARGEBACK_REQUESTED,
            commentContext);

        // Reject absorb
        assertTriggerEvent(REJECT_ABSORB_CHARGEBACK, ABSORB_CHARGEBACK_REJECTED,
            commentContext);

        //request absorb
        assertTriggerEvent(REQUEST_ABSORB_CHARGEBACK, ABSORB_CHARGEBACK_REQUESTED,
            commentContext);

        // finance manager approve absorb request
        serviceUserPrincipal = MockingUtils.getAndMockFinanceManagerUser(olympusIMClient);
        assertTriggerEvent(APPROVE_ABSORB_CHARGEBACK, ABSORB_CHARGEBACK_APPROVED,
            commentContext);

        // finance ops  marks Chargeback as Absorbed
        serviceUserPrincipal = MockingUtils.getAndMockFinanceOpsUser(olympusIMClient);
        assertTriggerEvent(ABSORB_CHARGEBACK, CHARGEBACK_ABSORBED,
            commentContext);

        // finance ops  marks Chargeback Absorbed reversed
        serviceUserPrincipal = MockingUtils.getAndMockFinanceOpsUser(olympusIMClient);
        assertTriggerEvent(REVERSE_ABSORB_CHARGEBACK, CHARGEBACK_ABSORB_REVERSED,
            commentContext);

    }

    protected void testRecoveryFlow() {

        // Setup Plutus Ingestor and Status Check API to respond 204
        MockingUtils.setupPlutusEventIngestionApiMocking(disputeWorkflow);
        MockingUtils.setupPlutusStatusCheckApiMocking(disputeWorkflow);

        CommentContext commentContext = CommentContext.builder()
            .comment("Chargeback absorb required")
            .build();

        // Merchant Ops requests for recovery
        assertTriggerEvent(REQUEST_RECOVER_CHARGEBACK, RECOVER_CHARGEBACK_REQUESTED,
            commentContext);

        // Reject recovery
        assertTriggerEvent(REJECT_RECOVER_CHARGEBACK, RECOVER_CHARGEBACK_REJECTED,
            commentContext);

        // request absorb
        assertTriggerEvent(REQUEST_ABSORB_CHARGEBACK, ABSORB_CHARGEBACK_REQUESTED,
            commentContext);

        // Reject absorb
        assertTriggerEvent(REJECT_ABSORB_CHARGEBACK, ABSORB_CHARGEBACK_REJECTED,
            commentContext);

        //request recovery
        assertTriggerEvent(REQUEST_RECOVER_CHARGEBACK, RECOVER_CHARGEBACK_REQUESTED,
            commentContext);

        // Finance Manager approves Recovery of Chargeback
        serviceUserPrincipal = MockingUtils.getAndMockFinanceManagerUser(olympusIMClient);
        assertTriggerEventWithDelay(APPROVE_RECOVER_CHARGEBACK,
            RECOVER_CHARGEBACK_EVENT_ACCEPTED, commentContext);

        serviceUserPrincipal = MockingUtils.getAndMockFinanceOpsUser(olympusIMClient);
        assertTriggerEventWithDelay(REQUEST_REVERSAL_OF_RECOVERED_CHARGEBACK,
            REVERSAL_OF_RECOVERED_CHARGEBACK_REQUESTED, commentContext);

        assertTriggerEventWithDelay(REJECT_REVERSAL_OF_RECOVERED_CHARGEBACK,
            RECOVER_CHARGEBACK_EVENT_ACCEPTED, commentContext);

        assertTriggerEventWithDelay(REQUEST_REVERSAL_OF_RECOVERED_CHARGEBACK,
            REVERSAL_OF_RECOVERED_CHARGEBACK_REQUESTED, commentContext);

        serviceUserPrincipal = MockingUtils.getAndMockFinanceManagerUser(olympusIMClient);
        assertTriggerEventWithDelay(APPROVE_REVERSAL_OF_RECOVERED_CHARGEBACK,
            REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT_ACCEPTED, commentContext);
    }

    protected void testResetChargeback() {

        serviceUserPrincipal = MockingUtils.getAndMockMerchantManagerUser(olympusIMClient);

        // reset chargeback
        assertTriggerEvent(RESET_CHARGEBACK, CHARGEBACK_CANCELLED, CommentContext.builder()
            .comment("Wrong entry")
            .build()
        );

        final var newDisputeWorkflow = disputeService
            .getDisputeWorkflow(dispute.getTransactionReferenceId(),
                getDisputeType(), getDisputeStage());

        AssertionUtils.assertDisputeWorkflowStateEquals(
            dispute.getTransactionReferenceId(), newDisputeWorkflow.getDisputeWorkflowId(),
            REFUND_BLOCKED, 5L, TimeUnit.SECONDS);
        Assertions.assertEquals(0L,
            ((FinancialDisputeWorkflow) newDisputeWorkflow).getAcceptedAmount());
        Assertions.assertEquals(disputeWorkflow.getDisputedAmount(),
            newDisputeWorkflow.getDisputedAmount());
    }

    protected void triggerPartialDebit(final long debitAmount) {
        // partial debit
        MockingUtils.mockOlympusPermission("chargeback/transition-receive_partial_debit",
            olympusIMClient);
        assertTriggerEvent(RECEIVE_PARTIAL_DEBIT,
            PARTIAL_DEBIT_RECEIVED, TestDataUtils.getDebitTransitionContext(debitAmount));
    }

    protected void triggerPartialCredit(final long creditAmount) {

        MockingUtils.mockOlympusPermission("chargeback/transition-receive_partial_credit",
            olympusIMClient);
        assertTriggerEvent(RECEIVE_PARTIAL_CREDIT,
            PARTIAL_CREDIT_RECEIVED, TestDataUtils.getRefundCreditTransitionContext(creditAmount));
    }

    protected void triggerDebitSignal() {

        // Receive Refund Credit from NPCI upon successful Representment and mark Refund Received
        final var debitTransitionContext = TestDataUtils
            .getDebitTransitionContext(dispute.getTransactionAmount());

        MockingUtils.mockOlympusPermission("chargeback/transition-receive_debit", olympusIMClient);
        assertTriggerEventWithDelay(RECEIVE_DEBIT, DEBIT_RECEIVED, debitTransitionContext);

    }

    protected void triggerCreditSignal() {

        final var refundCreditTransitionContext = TestDataUtils
            .getRefundCreditTransitionContext(dispute.getTransactionAmount());

        // credit received
        Mockito.when(olympusIMClient.verifyPermission(Mockito.any(),
            Mockito.eq("chargeback/transition-receive_credit"))).thenReturn(true);
        assertTriggerEvent(RECEIVE_CREDIT, CREDIT_RECEIVED, refundCreditTransitionContext);

    }

    protected void assertFromCreateToMerchantNotResponded(
        final Set<DisputeWorkflowEvent> permissibleEventsMerchantNotResponding) {

        createEntrySetup();
        assertRefundBlock();

        assertTriggerEventWithDelay(NO_RESPONSE_FROM_MERCHANT_WITHIN_TTL,
            MERCHANT_NOT_RESPONDED_WITHIN_TTL);

        final var upcomingEventsMerchantNotResponding = disputeResource
            .getUpcomingEvents(serviceUserPrincipal, disputeWorkflow.getDisputeType(),
                disputeWorkflow.getDisputeStage(), disputeWorkflow.getDisputeWorkflowVersion(),
                MERCHANT_NOT_RESPONDED_WITHIN_TTL);
        Assertions.assertEquals(permissibleEventsMerchantNotResponding,
            upcomingEventsMerchantNotResponding);


    }

    protected void assertFromCreateToRepresentmentRequirement(
        final Set<DisputeWorkflowEvent> expectedUpcomingEvents) {

        MockingUtils.setupPaymentsApiFullRefundExistsMocking(dispute.getTransactionReferenceId(),
            disputeWorkflow.getDisputedAmount(), "YBL1234");

        // Create Dispute Entry in System
        disputeService.createEntry(dispute, disputeWorkflow);

        // Assert Status of Dispute Workflow to be Representment Required
        AssertionUtils.assertDisputeWorkflowStateAndCategoryEquals(
            dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            REPRESENTMENT_REQUIRED, dispute.getDisputeCategory(), 5L, TimeUnit.SECONDS);

        // Test Allowed Transitions from Current State
        final var upcomingEvents = disputeResource
            .getUpcomingEvents(serviceUserPrincipal, disputeWorkflow.getDisputeType(),
                disputeWorkflow.getDisputeStage(), disputeWorkflow.getDisputeWorkflowVersion(),
                REPRESENTMENT_REQUIRED);
        Assertions
            .assertEquals(expectedUpcomingEvents, upcomingEvents);
    }

    protected void assertFromCreateToNpciAck(
        final Set<DisputeWorkflowEvent> expectedUpcomingEvents) {

        disputeService.triggerNpciAck(dispute, disputeWorkflow);

        AssertionUtils.assertDisputeWorkflow(
            disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            NPCI_ACK_CHARGEBACK, dispute.getDisputeCategory(), disputeWorkflow.getDisputeType());

        // Test Allowed Transitions from Current State
        final var upcomingEvents = disputeResource
            .getUpcomingEvents(serviceUserPrincipal, disputeWorkflow.getDisputeType(),
                disputeWorkflow.getDisputeStage(), disputeWorkflow.getDisputeWorkflowVersion(),
                NPCI_ACK_CHARGEBACK);
        Assertions
            .assertEquals(expectedUpcomingEvents, upcomingEvents);
    }

    protected void assertFromNpciAckToCBRefundInitiationComplete() {

        assertTriggerEvent(MERCHANT_ACCEPT_CHARGEBACK, MERCHANT_ACCEPTED_CHARGEBACK,
            PartialAcceptanceTransitionContext.builder()
                .acceptedAmount(disputeWorkflow.getDisputedAmount())
                .build());

        final var newDisputeWorkflow = disputeService
            .getDisputeWorkflow(dispute.getTransactionReferenceId(),
                getDisputeType(), getDisputeStage());

        AssertionUtils.assertDisputeWorkflowStateEquals(
            dispute.getTransactionReferenceId(), newDisputeWorkflow.getDisputeWorkflowId(),
            CB_REFUND_INITIATED_COMPLETED, 5L, TimeUnit.SECONDS);
    }

    protected void assertFromRefundFailureToAcceptedFromExternalSource() {

        assertTriggerEvent(MAX_RETRY_TO_PROCESSED_EXTERNALLY, CB_REFUND_PROCESSED_EXTERNALLY,
            RefundContext.builder()
                .originalTransactionId(disputeWorkflow.getTransactionReferenceId())
                .refundTransactionId(disputeWorkflow.getTransactionReferenceId())
                .refundReceiverInstrument(RefundReceiverInstrument.UPI_ACCOUNT)
                .build());

        final var newDisputeWorkflow = disputeService
            .getDisputeWorkflow(dispute.getTransactionReferenceId(),
                getDisputeType(), getDisputeStage());

        AssertionUtils.assertDisputeWorkflowStateEquals(
            dispute.getTransactionReferenceId(), newDisputeWorkflow.getDisputeWorkflowId(),
            ACCEPTED_CHARGEBACK, 5L, TimeUnit.SECONDS);
    }

    @SneakyThrows
    protected void assertFromCBRefundInitiationCompleteToAcceptedChargeback(
        RefundStatus status, DisputeWorkflowState state, String disbursementId) {
        RefundResponseV2 response1 = RefundResponseV2.builder()
                .status(status)
                .reversalTxnId("CBT12431412")
                .paymentForwardTxnId("UWTID1234")
                .version(RefundResponseVersion.V2)
                .build();
        String statusString = objectMapper.writeValueAsString(response1);
        var olympusClient = guiceInjector.getInstance(OlympusIMClient.class);
        serviceUserPrincipal = MockingUtils.getAndMockCallbackComponent(olympusClient);

        final var exception = Assertions.assertThrows(
            DisputeException.class, ()->callbackResource.processCallback(serviceUserPrincipal, new ByteArrayInputStream(statusString.getBytes()), "refund-status"));
        Assertions.assertEquals(StratosErrorCodeKey.INVALID_REFUND_ID, exception.getErrorCode());

        RefundResponseV2 refundResponse = RefundResponseV2.builder()
            .status(status)
            .reversalTxnId(disbursementId)
            .paymentForwardTxnId("UWTID1234")
            .version(RefundResponseVersion.V2)
            .build();
        String statusString1 = objectMapper.writeValueAsString(refundResponse);
        callbackResource.processCallback(serviceUserPrincipal, new ByteArrayInputStream(statusString1.getBytes()), "refund-status");

        final var newDisputeWorkflow = disputeService
            .getDisputeWorkflow(dispute.getTransactionReferenceId(),
                getDisputeType(), getDisputeStage());

        AssertionUtils.assertDisputeWorkflowStateEquals(
            dispute.getTransactionReferenceId(), newDisputeWorkflow.getDisputeWorkflowId(),
            state, 5L, TimeUnit.SECONDS);
        //give merchant ops permission again
        serviceUserPrincipal = MockingUtils.getAndMockMerchantOpsUser(olympusClient);

    }

    protected void assertFromCBRefundInitiationCompleteToAcceptedChargebackWithReconcileAPI(
        DisputeWorkflowState state) {

        final var newDisputeWorkflow = disputeService
            .getDisputeWorkflow(dispute.getTransactionReferenceId(),
                getDisputeType(), getDisputeStage());

        AssertionUtils.assertDisputeWorkflowStateEquals(
            dispute.getTransactionReferenceId(), newDisputeWorkflow.getDisputeWorkflowId(),
            state, 5L, TimeUnit.SECONDS);
    }

    protected void assertFromCreateToPartiallyAcceptance() {

        assertTriggerEvent(MERCHANT_ACCEPT_CHARGEBACK, MERCHANT_ACCEPTED_CHARGEBACK,
            PartialAcceptanceTransitionContext.builder()
                .acceptedAmount(disputeWorkflow.getDisputedAmount() - 1)
                .build());

        final var newDisputeWorkflow = disputeService
            .getDisputeWorkflow(dispute.getTransactionReferenceId(),
                getDisputeType(), getDisputeStage());

        AssertionUtils.assertDisputeWorkflowStateEquals(
            dispute.getTransactionReferenceId(), newDisputeWorkflow.getDisputeWorkflowId(),
            CB_REFUND_INITIATED_COMPLETED, 5L, TimeUnit.SECONDS);
    }

    protected void assertFromCreateToRejected() {

        assertTriggerEvent(COMPLETE_NPCI_REPRESENTMENT, NPCI_REPRESENTMENT_COMPLETED);

        final var newDisputeWorkflow = disputeService
            .getDisputeWorkflow(dispute.getTransactionReferenceId(),
                getDisputeType(), getDisputeStage());

        AssertionUtils.assertDisputeWorkflowStateEquals(
            dispute.getTransactionReferenceId(), newDisputeWorkflow.getDisputeWorkflowId(),
            REJECTED_CHARGEBACK, 5L, TimeUnit.SECONDS);
    }

    protected void assertFromCreateToPartiallyRejected() {

        assertTriggerEvent(COMPLETE_NPCI_REPRESENTMENT, NPCI_REPRESENTMENT_COMPLETED);

        final var newDisputeWorkflow = disputeService
            .getDisputeWorkflow(dispute.getTransactionReferenceId(),
                getDisputeType(), getDisputeStage());

        AssertionUtils.assertDisputeWorkflowStateEquals(
            dispute.getTransactionReferenceId(), newDisputeWorkflow.getDisputeWorkflowId(),
            NPCI_ACK_CHARGEBACK, 5L, TimeUnit.SECONDS);
    }

    protected void assertFromCreateToInternalMidRepresentmentRequired(
        Set<DisputeWorkflowEvent> expectedUpcomingEvents) {
        // Create Dispute Entry in System
        disputeService.createEntry(dispute, disputeWorkflow);

        // Assert Status of Dispute Workflow to be Internal Merchant Representment Required
        AssertionUtils.assertDisputeWorkflowStateEquals(
            disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            INTERNAL_MID_REPRESENTMENT_REQUIRED, 5L, TimeUnit.SECONDS);

        // Test Allowed Transitions from Current State
        final var upcomingEvents = disputeResource
            .getUpcomingEvents(serviceUserPrincipal, disputeWorkflow.getDisputeType(),
                disputeWorkflow.getDisputeStage(), disputeWorkflow.getDisputeWorkflowVersion(),
                INTERNAL_MID_REPRESENTMENT_REQUIRED);

        // Assert Status of Dispute Workflow to be Complete NPCI Representment
        Assertions.assertEquals(expectedUpcomingEvents, upcomingEvents);
    }

    protected void assertFromCreateToSuspectedFraud() {
        createEntrySetup(KratosRecommendedAction.SUSPECT);
        assertRefundBlock();

        //merchant accepted chargeback but was marked suspected
        assertTriggerEventWithDelay(MERCHANT_ACCEPT_CHARGEBACK, SUSPECTED_FRAUD);
    }

    protected void testAutoApprovalFlow() {

        // Setup Plutus Ingestor and Status Check API to respond 204
        MockingUtils.setupPlutusEventIngestionApiMocking(disputeWorkflow);
        MockingUtils.setupPlutusStatusCheckApiMocking(disputeWorkflow);

        CommentContext commentContext = CommentContext.builder()
            .comment("Chargeback recovery required")
            .build();

        assertTriggerEvent(REQUEST_RECOVER_CHARGEBACK, RECOVER_CHARGEBACK_REQUESTED,
            commentContext);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            RECOVER_CHARGEBACK_EVENT_ACCEPTED, 10L, TimeUnit.SECONDS);

    }
}