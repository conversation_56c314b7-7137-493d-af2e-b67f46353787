package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.toa.p2pm;

import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.DisputeWorkflowKey;
import com.phonepe.merchant.platform.stratos.server.BaseTest;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.NeuronTestUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.resources.ToaResource;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import java.util.concurrent.TimeUnit;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class P2pmToaInitiationFailedWorkflowTest extends BaseTest {

    private ToaResource toaResource;


    @BeforeEach
    void setUpGuiceInjection() {
        toaResource = guiceInjector.getInstance(ToaResource.class);

        MockingUtils.setupEventIngestionApiMocking();
        MockingUtils.disableKillSwitchMocking();
    }

    /*
    Test: Communication Failure in payment test pai
    WORKFLOW: PULSE ->
                    RECEIVED -> P2PM_TOA_INITIATION_FAILED
                        processToaExternally
                    P2PM_TOA_INITIATION_FAILED -> TOA_COMPLETED_EXTERNALLY
    */
    @Test
    void testInitiationFailedCommunicationFailureThenProcessedExternally() {

        final var transactionReferenceId = IdGenerator.generate("T").getId();
        // mock transaction details api
        MockingUtils.setDeemFailureUpiTransactionDetail(transactionReferenceId, 120);

        // mock the payment pay api to fail with Communication failure
        MockingUtils.setUpPaymentPayApi(true, 500);

        testInitiationFailed(transactionReferenceId);
    }

    /*
     Test: API Failure in payment test pai
     WORKFLOW: PULSE ->
                     RECEIVED -> P2PM_TOA_INITIATION_FAILED
                        processToaExternally
                     P2PM_TOA_INITIATION_FAILED -> TOA_COMPLETED_EXTERNALLY
     */
    @Test
    void testInitiationFailedApiFailureThenProcessedExternally() {

        final var transactionReferenceId = IdGenerator.generate("T").getId();
        // mock transaction details api
        MockingUtils.setDeemFailureUpiTransactionDetail(transactionReferenceId, 120);

        // mock the payment pay api to fail with API failure
        MockingUtils.setUpPaymentPayApi(false, 200);

        testInitiationFailed(transactionReferenceId);
    }

    /*
    Test: Re-initiate toa test
    WORKFLOW: PULSE ->
                    RECEIVED -> P2PM_TOA_INITIATION_FAILED ->
                        retryToa
                        processToaExternally
                    P2PM_TOA_INITIATION_FAILED -> TOA_COMPLETED_EXTERNALLY
    */
    @Test
    void testInitiationFailedThenReInitiateThenProcessedExternally() {

        final var transactionReferenceId = IdGenerator.generate("T").getId();

        // mock transaction details api
        MockingUtils.setDeemFailureUpiTransactionDetail(transactionReferenceId, 120);

        // mock the payment pay api to fail with communication error
        MockingUtils.setUpPaymentPayApi(false, 500);

        // Create a pulse
        NeuronTestUtils.createP2pmToaPulse(transactionReferenceId);

        // wait till the entry for workflow is created
        DisputeWorkflow disputeWorkflow = AssertionUtils
            .assertAndGetDisputeWorkflowCreation(transactionReferenceId, DisputeType.P2PM_TOA,
                DisputeStage.FIRST_LEVEL);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.P2PM_TOA_INITIATION_FAILED,
            30L, TimeUnit.SECONDS);

        // retry TOA
        final var manualRoleUser = TestDataUtils.getOlympusUser();
        var response = toaResource.retryToa(manualRoleUser, DisputeWorkflowKey.builder()
                .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
                .transactionReferenceId(transactionReferenceId)
            .build(), DisputeType.P2PM_TOA);

        // Assert Retry failed
        Assertions.assertFalse(response.isSuccess());
        AssertionUtils.assertToaSummary(response.getData(), disputeWorkflow, DisputeWorkflowState.P2PM_TOA_INITIATION_FAILED);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.P2PM_TOA_INITIATION_FAILED,
            0, TimeUnit.SECONDS);

        // Process TOA externally
        response = toaResource.processToaExternally(manualRoleUser, transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(), TestDataUtils.getCommentContext(), DisputeType.P2PM_TOA);

        Assertions.assertTrue(response.isSuccess());
        AssertionUtils.assertToaSummary(response.getData(), disputeWorkflow, DisputeWorkflowState.TOA_COMPLETED_EXTERNALLY);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.TOA_COMPLETED_EXTERNALLY,
            0, TimeUnit.SECONDS);

    }


    private void testInitiationFailed(String transactionReferenceId){
        // mock transaction details api
        MockingUtils.setDeemFailureUpiTransactionDetail(transactionReferenceId, 120);

        // mock the payment pay api to fail with communication error
        MockingUtils.setUpPaymentPayApi(false, 500);

        // Create a pulse
        NeuronTestUtils.createP2pmToaPulse(transactionReferenceId);

        // wait till the entry for workflow is created
        DisputeWorkflow disputeWorkflow = AssertionUtils
            .assertAndGetDisputeWorkflowCreation(transactionReferenceId, DisputeType.P2PM_TOA,
                DisputeStage.FIRST_LEVEL);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.P2PM_TOA_INITIATION_FAILED,
            30L, TimeUnit.SECONDS);

        // Trigger event to move the dispute to externally processed
        final var manualRoleUser = TestDataUtils.getOlympusUser();

        var response = toaResource.processToaExternally(manualRoleUser, transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(), TestDataUtils.getCommentContext(), DisputeType.P2PM_TOA);


        Assertions.assertTrue(response.isSuccess());
        AssertionUtils.assertToaSummary(response.getData(), disputeWorkflow, DisputeWorkflowState.TOA_COMPLETED_EXTERNALLY);

        // validate dispute workflow
        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.TOA_COMPLETED_EXTERNALLY,
            0, TimeUnit.SECONDS);
    }
}
