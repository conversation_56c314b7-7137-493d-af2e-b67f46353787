package com.phonepe.merchant.platform.stratos.server.integrationTests.file.edc.beans;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EdcChargebackBean {

    @JsonProperty("CB Received Date")
    private String cbReceivedDate;
    @JsonProperty("Source")
    private String source;
    @JsonProperty("Tenant")
    private String tenant;
    @JsonProperty("External Terminal Id")
    private String externalTerminalId;
    @JsonProperty("External Merchant Id")
    private String externalMerchantId;
    @JsonProperty("Bank Disputed Amount")
    private String bankDisputedAmount;
    @JsonProperty("RRN")
    private String rrn;
    @JsonProperty("Dispute Reason")
    private String disputeReason;

}
