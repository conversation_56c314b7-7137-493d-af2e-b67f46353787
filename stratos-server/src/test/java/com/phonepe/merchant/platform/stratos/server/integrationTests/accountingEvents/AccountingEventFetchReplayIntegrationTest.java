package com.phonepe.merchant.platform.stratos.server.integrationTests.accountingEvents;

import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.DaoBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeMetadataRepository;
import com.phonepe.merchant.platform.stratos.server.core.resources.AccountingEventResource;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

@Slf4j
class AccountingEventFetchReplayIntegrationTest extends DaoBaseTest {

    private AccountingEventResource accountingEventResource;

    private DisputeMetadataRepository disputeMetadataRepository;

    @BeforeEach
    void setUpGuiceInjection() {
        accountingEventResource = guiceInjector.getInstance(AccountingEventResource.class);
        disputeMetadataRepository = guiceInjector.getInstance(DisputeMetadataRepository.class);
        truncateDb();
    }

    @Test
    void testWhenAccountingEventsArePresentForGivenDisputeWorkflowIdThenGetAccountingEventsReturnsAccountingEvents() {

        final var transactionReferenceId = IdGenerator.generate("T").getId();
        final var disputeWorkflowId = IdGenerator.generate("DW").getId();
        final var accountingEventId = IdGenerator.generate("AE").getId();

        final var accountingEventDisputeMetadata = TestDataUtils
            .getAccountingEventDisputeMetadata(transactionReferenceId, disputeWorkflowId,
                accountingEventId);
        disputeMetadataRepository.save(accountingEventDisputeMetadata);

        final var accountingEvents = accountingEventResource
            .getAccountingEvents(accountingEventDisputeMetadata.getDisputeWorkflowId());

        Assertions.assertEquals(1, accountingEvents.size());
        Assertions.assertEquals(accountingEventDisputeMetadata.getAccountingEvent(),
            accountingEvents.get(0));
    }

    @Test
    void testWhenAccountingEventsAreMissingForGivenDisputeWorkflowIdThenGetAccountingEventsReturnsEmptyList() {

        final var transactionReferenceId = IdGenerator.generate("T").getId();
        final var disputeWorkflowId = IdGenerator.generate("DW").getId();

        final var commentDisputeMetadata =
            TestDataUtils.getCommentDisputeMetadata(transactionReferenceId, disputeWorkflowId);
        disputeMetadataRepository.save(commentDisputeMetadata);

        final var accountingEvents = accountingEventResource
            .getAccountingEvents(disputeWorkflowId);

        Assertions.assertEquals(0, accountingEvents.size());
    }

    @Test
    void testWhenAccountingEventForGivenAccountingEventIdIsPresentThenGetAccountingEventReturnsAccountingEvent() {

        final var transactionReferenceId = IdGenerator.generate("T").getId();
        final var disputeWorkflowId = IdGenerator.generate("DW").getId();
        final var accountingEventId = IdGenerator.generate("AE").getId();

        final var accountingEventDisputeMetadata = TestDataUtils
            .getAccountingEventDisputeMetadata(transactionReferenceId, disputeWorkflowId,
                accountingEventId);
        disputeMetadataRepository.save(accountingEventDisputeMetadata);

        final var accountingEvent = accountingEventResource
            .getAccountingEvent(accountingEventDisputeMetadata.getAccountingEventId());

        Assertions
            .assertEquals(accountingEventDisputeMetadata.getAccountingEvent(), accountingEvent);
    }

    @Test
    void testWhenAccountingEventForGivenAccountingEventIdIsMissingThenGetAccountingEventThrowsStratosError() {

        final var transactionReferenceId = IdGenerator.generate("T").getId();
        final var disputeWorkflowId = IdGenerator.generate("DW").getId();
        final var accountingEventId = IdGenerator.generate("AE").getId();

        final var commentDisputeMetadata =
            TestDataUtils.getCommentDisputeMetadata(transactionReferenceId, disputeWorkflowId);
        disputeMetadataRepository.save(commentDisputeMetadata);

        final var stratosError = Assertions.assertThrows(DisputeException.class,
            () -> accountingEventResource.getAccountingEvent(accountingEventId));

        Assertions.assertEquals(StratosErrorCodeKey.RAISED_ACCOUNTING_EVENT_NOT_FOUND,
            stratosError.getErrorCode());
    }

    @Test
    void testWhenAccountingEventIdIsPassedForAReplayAndIfEventIsPresentThenEventIsReplayedViaPlutusEventIngestionClient() {

        final var transactionReferenceId = IdGenerator.generate("T").getId();
        final var disputeWorkflowId = IdGenerator.generate("DW").getId();
        final var accountingEventId = IdGenerator.generate("AE").getId();

        final var accountingEventDisputeMetadata = TestDataUtils
            .getAccountingEventDisputeMetadata(transactionReferenceId, disputeWorkflowId,
                accountingEventId);
        disputeMetadataRepository.save(accountingEventDisputeMetadata);

        MockingUtils.setupPlutusEventIngestionApiMocking(accountingEventDisputeMetadata.getAccountingEventId());

        try {
            accountingEventResource
                .replayAccountingEvent(accountingEventDisputeMetadata.getAccountingEventId());
        } catch (final Exception e) {
            log.error("Error Occurred", e);
            Assertions.fail("Error shouldn't occur for this test case");
        }
    }
}
