package com.phonepe.merchant.platform.stratos.server.integrationTests.file.nb.beans;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class NetBankingChargebackBean {

    @JsonProperty("CB Received Date")
    private String cbReceivedDate;
    @JsonProperty("Transaction Date")
    private String transactionDate;
    @JsonProperty("Transaction ID")
    private String transactionId;
    @JsonProperty("Bank Reference ID")
    private String bankReferenceId;
    @JsonProperty("Amount")
    private Double amount;
    @JsonProperty("Source")
    private String source;
    @JsonProperty("Dispute Reason")
    private String disputeReason;

}
