{"success": true, "data": {"flow": "EXTERNAL_TO_MERCHANT_V2", "sentPayment": {"globalPaymentId": "T2310181510347178229445", "transactionId": "T2310181510347178229445", "userId": "__EXTERNAL__", "to": [{"type": "MERCHANT", "userId": "SWIGGY8", "mcc": "5812", "merchantId": "SWIGGY8", "merchantType": "ONLINE_MERCHANT", "name": "Swiggy", "accountHolderName": "Swiggy", "firstPartyMerchant": false, "amount": 1000, "state": "COMPLETED", "displayName": "Swiggy", "legalName": "Swiggy", "merchantIdentifierType": "SMALL", "ownershipType": "OTHERS", "merchantGenreType": "ONLINE", "onboardingType": "AGGREGATOR"}], "paidFrom": [{"type": "NET_BANKING", "transactionState": "COMPLETED", "flags": 0, "amount": 1000, "actualAmount": 1000, "instrumentId": "************************", "processingRail": "NETPE", "processingModeType": "NETPE_DEFAULT", "bankId": "SBIN", "pgTransactionId": "IGAAAEBDH9", "pgServiceTransactionId": "************************"}], "offerAdjustments": [], "context": {"transferMode": "PEER_TO_MERCHANT", "serviceContext": {}, "merchantOrderId": "OD139924923", "merchantTransactionId": "NTBNKTESTTXN1b4de35a", "message": "Payment ", "retryable": false}, "paymentState": "COMPLETED", "autoFailed": false, "sentAt": *************, "responseCode": "SUCCESS", "flags": 291336, "paymentFlags": ["MANDATE_NOT_APPLICABLE", "OFFER_ADJUSTMENT_NOT_APPLIED", "ENABLE_DEEMED_TO_SUCCESS", "ACCOUNTING_EVENT_SPLIT_ENABLED", "ACCOUNTING_V2", "GLOBAL_PAYMENT_ID_SET"], "fromParty": "MERCHANT_USER", "from": {"type": "MERCHANT_USER", "merchantId": "SWIGGY8", "merchantUserId": "U12344"}, "metaData": {"deviceLatitude": 0, "deviceLongitude": 0, "deviceIp": "************"}, "qcoTransaction": false}, "receivedPayment": {"globalPaymentId": "T2310181510347178229445", "transactionId": "@@paymentsTxnId@@", "from": {"type": "MERCHANT_USER", "merchantId": "SWIGGY8", "merchantUserId": "U12344"}, "to": {"type": "MERCHANT", "merchantId": "SWIGGY8", "name": "Swiggy", "mcc": "5812", "firstPartyMerchant": false}, "amount": "@@amount@@", "context": {"transferMode": "PEER_TO_MERCHANT", "serviceContext": {}, "merchantOrderId": "OD139924923", "merchantTransactionId": "NTBNKTESTTXN1b4de35a", "message": "Payment ", "retryable": false}, "receivedIn": [{"type": "ACCOUNT", "flags": 0, "amount": 1000, "actualAmount": 0, "instrumentId": "************************", "accountId": "A1607211107289453049169", "accountNumber": "XXXXXXXXXXX5684", "accountHolderName": "Swiggy", "ifsc": "YESB0YBLUPI", "accountType": "SAVINGS", "usageDomain": "UPI"}], "paidFrom": [{"type": "NET_BANKING", "transactionState": "COMPLETED", "flags": 0, "amount": "@@amount@@", "actualAmount": "@@amount@@", "instrumentId": "************************", "processingRail": "NETPE", "processingModeType": "NETPE_DEFAULT", "bankId": "SBIN", "pgTransactionId": "IGAAAEBDH9", "pgServiceTransactionId": "************************"}], "receivedAt": *************, "responseCode": "SUCCESS", "autoFailed": false}, "alreadyReversedTransactions": [{"originalTransactionId": "@@paymentsTxnId@@", "transactionId": "T2310181511588068229294", "currencyCode": "INR", "merchantTransactionId": "NETPEREV_35", "merchantId": "SWIGGY8", "amount": "@@amount@@", "totalCancellationAmount": "@@amount@@", "paymentState": "FAILED", "attemptedAt": *************}, {"originalTransactionId": "@@paymentsTxnId@@", "transactionId": "T2310181512126628229823", "currencyCode": "INR", "merchantTransactionId": "NETPEREV_36", "merchantId": "SWIGGY8", "amount": "@@amount@@", "totalCancellationAmount": "@@amount@@", "paymentState": "COMPLETED", "attemptedAt": *************}]}}