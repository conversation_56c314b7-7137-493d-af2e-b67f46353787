{
  "success": true,
  "data": {
    "flow": "CONSUMER_TO_MERCHANT",
    "sentPayment": {
      "globalPaymentId": "T2111081608439068817112",
      "transactionId": "@@paymentsTxnId@@",
      "userId": "U2105121610574904399182",
      "to": [
        {
          "type": "MERCHANT",
          "userId": "M2306160483220675579140",
          "mcc": "7299",
          "merchantId": "M2306160483220675579140",
          "merchantType": "ONLINE_MERCHANT",
          "name": "Test MerchantTT",
          "accountHolderName": "Test MerchantTT",
          "firstPartyMerchant": false,
          "amount": @@amount@@,
          "state": "FAILED"
        }
      ],
      "paidFrom": [
        {
          "type": "CREDIT_CARD",
          "transactionState": "FAILED",
          "flags": 1,
          "amount": @@amount@@,
          "actualAmount": @@amount@@,
          "maskCardNumber": "5519XXXXXXXX8459",
          "pgServiceTransactionId": "************************",
          "cardHolderName": "******3729",
          "cardIssuer": "MASTER_CARD",
          "cardBin": "551912",
          "cardId": "D1CBB62902824F0892C127006F4518B8",
          "cardIdType": "STAGED_CARD",
          "bankId": "DUMMY_BANK_1",
          "refundTurnAroundTime": 0,
          "qcoTransaction": false
        }
      ],
      "offerAdjustments": [],
      "context": {
        "transferMode": "INSTRUMENT_AUTH",
        "serviceContext": {},
        "merchantTransactionId": "543f91d3-ffcf-4e38-bf71-f8304dc1fef9"
      },
      "paymentState": "FAILED",
      "autoFailed": true,
      "sentAt": *************,
      "responseCode": "PG_BACKBONE_ERROR",
      "flags": 94731,
      "paymentFlags": [
        "OFFER_ADJUSTMENT_NOT_APPLIED",
        "ACCOUNTING_V2",
        "AUTO_FAILED",
        "REQUIRES_AUTO_REVERSAL",
        "ACCOUNTING_EVENT_SPLIT_ENABLED",
        "GLOBAL_PAYMENT_ID_SET",
        "UPI_PERSISTENCE_ENABLED",
        "MANDATE_NOT_APPLICABLE"
      ],
      "backendErrorCode": "REDIRECT_URL_NOT_VISITED",
      "fromParty": "INTERNAL_USER",
      "metaData": {
        "deviceFingerprint": "efaa3cf0-3626-4d43-9184-7b580d2f6c8bc3VuZmlzaA-c3VuZmlzaA-",
        "deviceLatitude": 0,
        "deviceLongitude": 0,
        "deviceIp": "************",
        "requestSourceType": "",
        "requestSourcePlatform": ""
      },
      "qcoTransaction": false
    },
    "receivedPayment": {
      "globalPaymentId": "T2111081608439068817112",
      "transactionId": "@@paymentsTxnId@@",
      "from": {
        "type": "INTERNAL_USER",
        "userId": "U2105121610574904399182",
        "name": "******6918",
        "phone": "**********",
        "mcc": "0000"
      },
      "to": {
        "type": "MERCHANT",
        "merchantId": "M2306160483220675579140",
        "name": "Test MerchantTT",
        "mcc": "7299",
        "firstPartyMerchant": false
      },
      "amount": @@amount@@,
      "context": {
        "transferMode": "INSTRUMENT_AUTH",
        "serviceContext": {},
        "merchantTransactionId": "543f91d3-ffcf-4e38-bf71-f8304dc1fef9"
      },
      "receivedIn": [
        {
          "type": "ACCOUNT",
          "flags": 0,
          "amount": @@amount@@,
          "actualAmount": @@amount@@,
          "instrumentId": "************************",
          "receiverInstrumentFlags": [],
          "accountId": "A1607211107289453049169",
          "accountNumber": "XXXXXXXXXXX5684",
          "accountHolderName": "Test MerchantTT",
          "ifsc": "YESB0YBLUPI",
          "accountType": "SAVINGS"
        }
      ],
      "receivedAt": *************,
      "responseCode": "PG_BACKBONE_ERROR",
      "autoFailed": true
    },
    "alreadyReversedTransactions": [
      {
        "originalTransactionId": "@@paymentsTxnId@@",
        "transactionId": "T2107142232477090806943",
        "currencyCode": "INR",
        "merchantTransactionId": "59f9ef4e-8dc0-4b1d-8fe8-d4561054b75d",
        "merchantId": "IRCTCINAPP",
        "amount": @@amount@@,
        "totalCancellationAmount": @@amount@@,
        "paymentState": "COMPLETED",
        "attemptedAt": *************
      },
      {
        "originalTransactionId": "@@paymentsTxnId@@",
        "transactionId": "T2107142232477090806955",
        "currencyCode": "INR",
        "merchantTransactionId": "59f9ef4e-8dc0-4b1d-8fe8-d4561054b777",
        "merchantId": "IRCTCINAPP",
        "amount": @@amount@@,
        "totalCancellationAmount": @@amount@@,
        "paymentState": "AUTO_FAILED",
        "attemptedAt": *************
      }
    ]
  }
}