["chargeback/upload-file", "chargeback/file-history", "chargeback/file-review", "chargeback/summary", "chargeback/transition-complete_rgcs_acceptance", "chargeback/transition-complete_npci_representment", "chargeback/transition-receive_fulfilment_documents", "chargeback/transition-receive_partial_fulfilment_documents", "chargeback/transition-complete_npci_partial_representment", "chargeback/transition-request_absorb_chargeback", "chargeback/transition-reject_absorb_chargeback", "chargeback/transition-request_recover_chargeback", "chargeback/transition-reject_recover_chargeback", "chargeback/transition-complete_npci_acceptance", "chargeback/transition-merchant_accept_chargeback", "chargeback/transition-fraud_rejected_to_representment", "chargeback/update-communication-id", "chargeback/transition-complete_pg_representment", "chargeback/transition-complete_pg_acceptance", "chargeback/transition-no_response_from_merchant_within_ttl", "chargeback/transition-complete_pg_partial_representment", "chargeback/transition-complete_representment", "chargeback/transition-create_chargeback_refund", "chargeback/transition-initiate_chargeback_refund", "chargeback/transition-chargeback_refund_accepted", "chargeback/transition-create_partial_refund", "chargeback/transition-initiate_partial_refund", "chargeback/transition-complete_partial_representment", "dispute/row-history-pg_mis_row", "chargeback/transition-complete_acceptance", "chargeback/transition-npci_ack-chargeback", "chargeback/transition-max_retry_to_processed_externally"]