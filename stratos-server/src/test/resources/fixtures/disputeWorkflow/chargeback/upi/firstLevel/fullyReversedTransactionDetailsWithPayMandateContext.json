{
  "success": true,
  "data": {
    "receivedPayment": {
      "amount": @@amount@@,
      "transactionId": "@@paymentsTxnId@@",
      "to": {
        "type": "MERCHANT",
        "merchantId": "IRCTCINAPP"
      },
      "context": {
        "transferMode": "PEER_TO_MERCHANT",
        "merchantOrderId": "MORD1234",
        "merchantTransactionId": "MTXN1234"
      },
      "mandateContext": {
        "type": "PAY"
      },
      "paidFrom": [
        {
          "type": "ACCOUNT",
          "amount": @@amount@@,
          "upiTransactionId": "YBL123"
        }
      ]
    },
    "sentPayment": {
      "amount": @@amount@@,
      "transactionId": "@@paymentsTxnId@@",
      "paymentState": "COMPLETED",
      "sentAt": *************,
      "userId": "12345",
      "to": [
        {
          "name": "IRCTCINAPP",
          "merchantId": "IRCTCINAPP"
        }
      ],
      "context": {
        "transferMode": "PEER_TO_MERCHANT",
        "merchantOrderId": "MORD1234",
        "merchantTransactionId": "MTXN1234"
      },
      "mandateContext": {
        "type": "PAY"

      },
      "paidFrom": [
        {
          "type": "ACCOUNT",
          "amount": @@amount@@,
          "upiTransactionId": "YBL123"
        }
      ]
    },
    "alreadyReversedTransactions": [
      {
        "originalTransactionId": "@@paymentsTxnId@@",
        "transactionId": "T2107142232477090806943",
        "currencyCode": "INR",
        "merchantTransactionId": "59f9ef4e-8dc0-4b1d-8fe8-d4561054b75d",
        "merchantId": "IRCTCINAPP",
        "amount": @@amount@@,
        "totalCancellationAmount": @@amount@@,
        "paymentState": "COMPLETED",
        "attemptedAt": *************
      },
      {
        "originalTransactionId": "@@paymentsTxnId@@",
        "transactionId": "T2107142232477090806955",
        "currencyCode": "INR",
        "merchantTransactionId": "59f9ef4e-8dc0-4b1d-8fe8-d4561054b777",
        "merchantId": "IRCTCINAPP",
        "amount": @@amount@@,
        "totalCancellationAmount": @@amount@@,
        "paymentState": "AUTO_FAILED",
        "attemptedAt": *************
      }
    ]
  }
}