{
  "success": false,
  "data": {
    "flow": "CONSUMER_TO_CONSUMER_V2",
    "receivedPayment": {
      "amount": @@amount@@,
      "transactionId": "@@paymentsTxnId@@",
      "to": {
        "type": "MERCHANT",
        "merchantId": "IRCTCINAPP"
      },
      "context": {
        "transferMode": "PEER_TO_MERCHANT",
        "merchantOrderId": "MORD1234",
        "merchantTransactionId": "MTXN1234"
      },
      "paidFrom": [
        {
          "type": "ACCOUNT",
          "amount": @@amount@@,
          "upiTransactionId": "YBL123",
          "utl": "********"
        }
      ]
    },
    "sentPayment": {
      "amount": @@amount@@,
      "transactionId": "@@paymentsTxnId@@",
      "paymentState": "COMPLETED",
      "sentAt": *************,
      "to": [
        {
          "name": "IRCTCINAPP",
          "merchantId": "IRCTCINAPP"
        }
      ],
      "context": {
        "transferMode": "PEER_TO_MERCHANT",
        "merchantOrderId": "MORD1234",
        "merchantTransactionId": "MTXN1234"
      },
      "paidFrom": [
        {
          "type": "ACCOUNT",
          "amount": @@amount@@,
          "upiTransactionId": "YBL123"
        }
      ],
      "paymentFlags": [
        "DEEMED_FAILURE",
        "PAYMENT_DEEMED"
      ],
      "metaData": {
        "deviceFingerprint": "qwertyuiujhgfdsasdfghjk",
        "deviceLatitude": 12.9634,
        "deviceLongitude": 77.5855,
        "deviceIp": "1234:1234:1234:1234:1234:1234:1234:1234",
        "requestSourceType": "APP",
        "requestSourcePlatform": "Android"
      }
    }
  }
}
