{
  "success": true,
  "data": {
    "flow": "CONSUMER_TO_CONSUMER_V2",
    "receivedPayment": {
      "amount": @@amount@@,
      "globalPaymentId": "G210101123456",
      "transactionId": "@@paymentsTxnId@@",
      "to": {
        "type": "MERCHANT",
        "merchantId": "IRCTCINAPP"
      },
      "context": {
        "transferMode": "PEER_TO_MERCHANT",
        "merchantOrderId": "MORD1234",
        "merchantTransactionId": "MTXN1234"
      },
      "paidFrom": [
        {
          "type": "ACCOUNT",
          "amount": @@amount@@
        }
      ],
      "receivedIn": [
        {
          "type": "ACCOUNT",
          "amount": @@amount@@,
          "utr": "1234"
        }
      ]
    },
    "sentPayment": {
      "userId": "__EXTERNAL__",
      "transactionId": "@@paymentsTxnId@@",
      "paymentState": "COMPLETED",
      "sentAt": *************,
      "to": [
        {
          "name": "IRCTCINAPP",
          "merchantId": "IRCTCINAPP"
        }
      ],
      "context": {
        "transferMode": "PEER_TO_MERCHANT",
        "merchantOrderId": "MORD1234",
        "merchantTransactionId": "MTXN1234"
      },
      "paidFrom": [
        {
          "type": "ACCOUNT",
          "amount": @@amount@@,
          "upiTransactionId": "YBL123"
        }
      ],
      "metaData": {
        "deviceFingerprint": "qwertyuiujhgfdsasdfghjk",
        "deviceLatitude": 12.9634,
        "deviceLongitude": 77.5855,
        "deviceIp": "1234:1234:1234:1234:1234:1234:1234:1234",
        "requestSourceType": "APP",
        "requestSourcePlatform": "Android"
      }
    },
    "alreadyReversedTransactions": []
  }
}