{"success": true, "data": {"flow": "CONSUMER_TO_CONSUMER", "sentPayment": {"globalPaymentId": "T2201201516327796034371", "transactionId": "T2201201516327796034371", "userId": "U2112141031159344500950", "to": [{"type": "VPA", "phone": "**********", "userId": "U2105121610574904399182", "vpa": "atusha1", "fullVpa": "atusha1@ybl", "mcc": "0000", "name": "******6918", "accountHolderName": "ABC", "firstPartyMerchant": false, "amount": 100, "state": "FAILED"}], "paidFrom": [{"type": "ACCOUNT", "transactionState": "FAILED", "reversalState": "FAILED", "reversalResponseCode": "RR", "flags": 0, "amount": 100, "actualAmount": 100, "accountId": "A2201181436065672168477", "accountNumber": "XXXXXXXXXX890124", "accountHolderName": "ABC", "ifsc": "AABE0876543", "utr": "************", "vpa": "noreqrespnoreqdrrev@ybl", "accountAuthMode": "UPI", "accountType": "SOD"}], "offerAdjustments": [], "context": {"transferMode": "PEER_TO_PEER", "serviceContext": {}, "tag": "miscellaneous"}, "paymentState": "FAILED", "autoFailed": false, "sentAt": *************, "responseCode": "UPI_BACKBONE_ERROR", "flags": 2191872, "paymentFlags": ["MANDATE_NOT_APPLICABLE", "OFFER_ADJUSTMENT_NOT_APPLIED", "UPI_PERSISTENCE_ENABLED", "ACCOUNTING_V2", "GLOBAL_PAYMENT_ID_SET", "DISABLE_CHAT_CARD_PUBLISH"], "fromParty": "INTERNAL_USER", "metaData": {"deviceFingerprint": "E1952B49904248AB889C8C5CF562DA61", "deviceLatitude": 30.***************, "deviceLongitude": 76.**************, "deviceCapability": "capability", "deviceManufacturer": "Apple", "deviceIp": "**************", "deviceAdvertiserId": "E1952B49904248AB889C8C5CF562DA61", "deviceOS": "iOS", "deviceOSVersion": "15.1.1", "deviceBuildVersion": "5.7.60", "deviceType": "iPhone 12 Pro Max", "requestSourceType": "APP", "requestSourcePlatform": "iOS"}, "qcoTransaction": false}, "alreadyReversedTransactions": []}}