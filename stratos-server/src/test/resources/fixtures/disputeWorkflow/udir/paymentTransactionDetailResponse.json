{
  "success": true,
  "data": {
    "flow": "CONSUMER_TO_CONSUMER",
    "sentPayment": {
      "globalPaymentId": "T2202081248578667228700",
      "transactionId": "@@paymentsTxnId@@",
      "userId": "U2107051254447190356925",
      "to": [
        {
          "type": "@@receiverType@@",
          "phone": "**********",
          "userId": "U1908201458325287608088",
          "vpa": "shan",
          "fullVpa": "shan@ybl",
          "mcc": "@@mcc@@",
          "name": "<PERSON><PERSON><PERSON> Se<PERSON>",
          "accountHolderName": "ABC",
          "firstPartyMerchant": false,
          "amount": 100,
          "state": "CREATED"
        }
      ],
      "paidFrom": [
        {
          "type": "ACCOUNT",
          "transactionState": "DEEMED",
          "transactionResponseCode": "DEEMED",
          "flags": 0,
          "amount": @@amount@@,
          "actualAmount": 100,
          "accountId": "A2110291142559471980289",
          "accountNumber": "XXXXXXXXXX890124",
          "accountHolderName": "ABC",
          "ifsc": "AABE0876543",
          "utr": "************",
          "vpa": "6720@ybl",
          "accountAuthMode": "UPI",
          "accountType": "SOD"
        }
      ],
      "offerAdjustments": [],
      "context": {
        "transferMode": "PEER_TO_PEER",
        "serviceContext": {},
        "tag": "miscellaneous"
      },
      "paymentState": "CREATED",
      "autoFailed": false,
      "sentAt": *************,
      "flags": 2191876,
      "paymentFlags": [
        "ACCOUNTING_V2",
        "UPI_PERSISTENCE_ENABLED",
        "OFFER_ADJUSTMENT_NOT_APPLIED",
        "PAYMENT_DEEMED",
        "MANDATE_NOT_APPLICABLE",
        "GLOBAL_PAYMENT_ID_SET",
        "DISABLE_CHAT_CARD_PUBLISH"
      ],
      "fromParty": "INTERNAL_USER",
      "metaData": {
        "deviceFingerprint": "703D626134ED485B9BD75168B9C2B3C7",
        "deviceLatitude": 23.***************,
        "deviceLongitude": 85.**************,
        "deviceCapability": "capability",
        "deviceManufacturer": "Apple",
        "deviceIp": "2405:201:a405:f172:fc64:f7c9:e9d2:c29a",
        "deviceAdvertiserId": "703D626134ED485B9BD75168B9C2B3C7",
        "deviceOS": "iOS",
        "deviceOSVersion": "15.0.2",
        "deviceBuildVersion": "5.7.80",
        "deviceType": "iPhone SE 2",
        "requestSourceType": "APP",
        "requestSourcePlatform": "iOS"
      },
      "qcoTransaction": false
    },
    "alreadyReversedTransactions": []
  }
}