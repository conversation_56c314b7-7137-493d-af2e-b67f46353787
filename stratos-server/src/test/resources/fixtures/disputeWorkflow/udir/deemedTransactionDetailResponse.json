{
  "success": true,
  "data": {
    "flow": "CONSUMER_TO_MERCHANT",
    "sentPayment": {
      "globalPaymentId": "*************************",
      "transactionId": "@@paymentsTxnId@@",
      "userId": "U2101211920221733781904",
      "to": [
        {
          "type": "MERCHANT",
          "userId": "BIL<PERSON><PERSON><PERSON><PERSON>",
          "vpa": "BILLDESKPP",
          "fullVpa": "BILLDESKPP@ybl",
          "mcc": "4899",
          "merchantId": "BILLDESKPP",
          "merchantType": "ONLINE_MERCHANT",
          "name": "PhonePe",
          "accountHolderName": "PhonePe",
          "firstPartyMerchant": true,
          "amount": 2000,
          "state": "COMPLETED",
          "displayName": "PhonePe",
          "legalName": "PhonePe",
          "merchantIdentifierType": "SMALL",
          "ownershipType": "OTHERS",
          "merchantGenreType": "ONLINE",
          "onboardingType": "AGGREGATOR"
        }
      ],
      "paidFrom": [
        {
          "type": "ACCOUNT",
          "transactionState": "@@paymentState@@",
          "flags": 0,
          "amount": @@amount@@,
          "actualAmount": 2000,
          "accountId": "A2101211929580082395699",
          "accountNumber": "XXXXXXXXXX890125",
          "accountHolderName": "ABC",
          "ifsc": "AABF0009009",
          "vpa": "yashodha@ybl",
          "accountAuthMode": "UPI",
          "accountType": "SAVINGS"
        }
      ],
      "offerAdjustments": [],
      "context": {
        "transferMode": "PEER_TO_MERCHANT",
        "serviceContext": {
          "serviceType": "BILLPAY",
          "providerId": "AIRTELDTH",
          "channel": "NEXUS",
          "category": "DTH",
          "source": "PHN"
        },
        "merchantOrderId": "*************************",
        "merchantTransactionId": "*************************",
        "retryable": false
      },
      "paymentState": "COMPLETED",
      "autoFailed": false,
      "sentAt": *************,
      "responseCode": "SUCCESS",
      "flags": 95816,
      "paymentFlags": [
        "OFFER_ADJUSTMENT_NOT_APPLIED",
        "GLOBAL_PAYMENT_ID_SET",
        "MANDATE_NOT_APPLICABLE",
        "UPI_PERSISTENCE_ENABLED",
        "ACCOUNTING_V2",
        "ACCOUNTING_EVENT_SPLIT_ENABLED",
        "MERCHANT_OWNED_ACCOUNTING_EVENT",
        "BLOCK_REVERAL"
      ],
      "fromParty": "INTERNAL_USER",
      "metaData": {
        "deviceFingerprint": "gnCtF33NG4yMRfSzP2tJ3uIlJfBy7bcfc3TYgVD8yGI",
        "deviceLatitude": 12.********,
        "deviceLongitude": 77.********,
        "deviceCapability": "capability",
        "deviceManufacturer": "Xiaomi",
        "deviceIp": "***********",
        "deviceAdvertiserId": "GNCTF33NG4YMRFSZP2TJ3UILJFBY7BCFC3T",
        "deviceOS": "Android",
        "deviceOSVersion": "28",
        "deviceBuildVersion": "xiaomi/tissot/tissot_sprout:9/PKQ1.180917.001/V10.0.24.0.PDHMIXM:user/release-keys",
        "deviceType": "Mobile",
        "requestSourceType": "",
        "requestSourcePlatform": "android"
      },
      "qcoTransaction": false
    },
    "receivedPayment": {
      "globalPaymentId": "*************************",
      "transactionId": "T2111301247078394015067",
      "from": {
        "type": "INTERNAL_USER",
        "userId": "U2101211920221733781904",
        "name": "Yashodha",
        "phone": "**********",
        "mcc": "0000"
      },
      "to": {
        "type": "MERCHANT",
        "merchantId": "BILLDESKPP",
        "name": "PhonePe",
        "mcc": "4899",
        "firstPartyMerchant": true
      },
      "amount": 2000,
      "context": {
        "transferMode": "PEER_TO_MERCHANT",
        "serviceContext": {
          "serviceType": "BILLPAY",
          "providerId": "AIRTELDTH",
          "channel": "NEXUS",
          "category": "DTH",
          "source": "PHN"
        },
        "merchantOrderId": "*************************",
        "merchantTransactionId": "*************************",
        "retryable": false
      },
      "receivedIn": [
        {
          "type": "ACCOUNT",
          "flags": 0,
          "amount": 2000,
          "actualAmount": 0,
          "instrumentId": "************************",
          "receiverInstrumentFlags": [],
          "accountId": "A1607211107289453049169",
          "accountNumber": "XXXXXXXXXXX5684",
          "accountHolderName": "PhonePe",
          "ifsc": "YESB0YBLUPI",
          "vpa": "BILLDESKPP@ybl",
          "accountType": "SAVINGS",
          "usageDomain": "UPI"
        }
      ],
      "paidFrom": [
        {
          "type": "ACCOUNT",
          "transactionState": "COMPLETED",
          "flags": 0,
          "amount": 2000,
          "actualAmount": 2000,
          "accountId": "A2101211929580082395699",
          "accountNumber": "XXXXXXXXXX890125",
          "accountHolderName": "ABC",
          "ifsc": "AABF0009009",
          "vpa": "yashodha@ybl",
          "accountAuthMode": "UPI",
          "accountType": "SAVINGS"
        }
      ],
      "receivedAt": *************,
      "responseCode": "SUCCESS",
      "autoFailed": false
    },
    "alreadyReversedTransactions": []
  }
}