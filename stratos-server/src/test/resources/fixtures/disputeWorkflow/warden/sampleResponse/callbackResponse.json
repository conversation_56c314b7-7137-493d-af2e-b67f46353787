{"workflowId": "@@wfId@@", "state": "@@state@@", "workflowConfig": {"tenantId": "STRATOS", "workflowConfigId": "WC2505051740330119632438", "workflowType": "CHARGEBACK_FILE_UPLOAD", "workflowName": "chargeback file upload", "workflowDescription": "File upload maker checker use case", "data": {"type": "WORKFLOW_CONFIG_DATA", "workflowContext": {"type": "WORKFLOW_CONFIG_WORKFLOW_CONTEXT", "data": {"callbackConfig": {"type": "HTTP_CALLBACK", "httpClientId": "stratos", "path": "/v1/callback/warden-callback"}}}}, "status": "ACTIVE", "checkers": [{"checker": {"type": "STATIC_CHECKER", "name": "testchecker", "description": "Testing", "users": [{"userId": "OU2308091651187530000280", "userType": "HUMAN", "email": "<EMAIL>"}]}, "workflowNodeConfigId": "NC2505051740330349632041", "createdBy": "OU2307191617506340000912", "createdAt": "2025-05-05T17:40:33", "updatedAt": "2025-05-05T17:40:33"}], "createdAt": "2025-05-05T17:40:33", "updatedAt": "2025-05-05T17:40:33", "createdBy": "OU2307191617506340000912"}, "data": {"type": "WORKFLOW_INSTANCE_DATA", "reviewContext": {"type": "JSON_NODE", "data": {"fileType": "YES", "disputeType": "UPI_CHARGEBACK", "rowCount": 1, "fileId": "@@fileId@@", "fileName": "file name"}}}}