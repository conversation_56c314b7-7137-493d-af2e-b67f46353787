{"success": true, "data": {"flow": "CONSUMER_TO_MERCHANT", "sentPayment": {"globalPaymentId": "*************************", "transactionId": "@@paymentsTxnId@@", "userId": "U2302031122400977469036", "to": [{"type": "MERCHANT", "userId": "BBPS", "mcc": "7399", "merchantId": "BBPS", "merchantType": "INAPP_MERCHANT", "name": "PhonePe", "accountHolderName": "PhonePe", "firstPartyMerchant": true, "amount": 200, "state": "COMPLETED", "displayName": "PhonePe", "legalName": "PhonePe", "merchantIdentifierType": "SMALL", "ownershipType": "OTHERS", "merchantGenreType": "ONLINE", "onboardingType": "AGGREGATOR"}], "paidFrom": [{"type": "@@instrumentType@@", "transactionState": "COMPLETED", "flags": 0, "amount": "@@amount@@", "actualAmount": "@@amount@@", "sourceExternalId": "DOP2403202158520482461373", "instrumentId": "************************", "processingRail": "PPI_WALLET", "processingModeType": "WALLET_DEFAULT", "walletId": "W2302031122469649674167", "availableBalance": 62099, "reserveBalance": 0}], "offerAdjustments": [], "context": {"transferMode": "PEER_TO_MERCHANT", "serviceContext": {"serviceType": "DONATION", "providerId": "SHRI00000NAT7X", "channel": "NEXUS", "category": "DEVOTION", "source": "PHN"}, "merchantOrderId": "*************************", "merchantTransactionId": "*************************", "retryable": false}, "paymentState": "COMPLETED", "autoFailed": false, "sentAt": *************, "responseCode": "SUCCESS", "flags": 356936, "paymentFlags": ["ACCOUNTING_V2", "GLOBAL_PAYMENT_ID_SET", "ACCOUNTING_EVENT_SPLIT_ENABLED", "OFFER_ADJUSTMENT_NOT_APPLIED", "MANDATE_NOT_APPLICABLE", "MERCHANT_OWNED_ACCOUNTING_EVENT", "UPI_PERSISTENCE_ENABLED", "ENABLE_DEEMED_TO_SUCCESS"], "fromParty": "INTERNAL_USER", "from": {"type": "INTERNAL_USER", "userId": "U2302031122400977469036", "name": "<PERSON><PERSON><PERSON>", "phone": "**********", "mcc": "0000", "phoneDetails": {"encodedPhoneNumber": "+91**********", "countryCode": "91", "region": "IN"}}, "metaData": {"deviceFingerprint": "B976A814188D4910A32E0E2BFA6EC366", "deviceLatitude": 18.***************, "deviceLongitude": 73.**************, "deviceCapability": "capability", "deviceManufacturer": "Apple", "deviceIp": "**************", "deviceAdvertiserId": "B976A814188D4910A32E0E2BFA6EC366", "deviceOS": "iOS", "deviceOSVersion": "17.3.1", "deviceBuildVersion": "24.0105.09", "deviceType": "iPhone 14 Pro Max", "requestSourceType": "APP", "requestSourcePlatform": "iOS"}, "qcoTransaction": false}, "receivedPayment": {"globalPaymentId": "*************************", "transactionId": "T2403202158551892461944", "from": {"type": "INTERNAL_USER", "userId": "U2302031122400977469036", "name": "<PERSON><PERSON><PERSON>", "phone": "**********", "mcc": "0000", "phoneDetails": {"encodedPhoneNumber": "+91**********", "countryCode": "91", "region": "IN"}}, "to": {"type": "MERCHANT", "merchantId": "BBPS", "name": "PhonePe", "mcc": "7399", "firstPartyMerchant": true}, "amount": 200, "context": {"transferMode": "PEER_TO_MERCHANT", "serviceContext": {"serviceType": "DONATION", "providerId": "SHRI00000NAT7X", "channel": "NEXUS", "category": "DEVOTION", "source": "PHN"}, "merchantOrderId": "*************************", "merchantTransactionId": "*************************", "retryable": false}, "receivedIn": [], "paidFrom": [], "receivedAt": *************, "responseCode": "SUCCESS", "autoFailed": false}}}