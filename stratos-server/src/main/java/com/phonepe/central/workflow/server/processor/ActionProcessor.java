package com.phonepe.central.workflow.server.processor;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.central.workflow.action.ActionType;
import com.phonepe.central.workflow.action.context.StageActionContext;
import com.phonepe.central.workflow.action.libraryaction.ActionTriggerType;
import com.phonepe.central.workflow.action.response.Action;
import com.phonepe.central.workflow.server.libraryaction.BaseAction;
import com.phonepe.central.workflow.server.libraryaction.DocstoreUpload;
import com.phonepe.central.workflow.action.libraryaction.param.ActionParam;
import com.phonepe.central.workflow.action.response.context.ActionResponseContext;
import com.phonepe.central.workflow.server.libraryaction.manual.AcceptContextAction;
import com.phonepe.central.workflow.server.libraryaction.manual.CommentContextAction;
import com.phonepe.central.workflow.server.libraryaction.manual.EmptyContextAction;
import com.phonepe.central.workflow.server.mariadb.entities.WorkflowClassInstanceMetaEntity;
import com.phonepe.central.workflow.server.mariadb.repository.ActionRepository;
import com.phonepe.central.workflow.server.mariadb.repository.WorkflowClassInstanceMetaRepository;
import com.phonepe.central.workflow.server.service.ActionService;
import com.phonepe.central.workflow.server.service.RetryService;
import com.phonepe.central.workflow.server.service.StageService;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Singleton
public class ActionProcessor {

    private final Map<ActionType, BaseAction<? extends ActionParam>> actionMap = new HashMap<>();
    private final ActionService actionService;
    private final WorkflowClassInstanceMetaRepository workflowClassInstanceMetaRepository;
    private final Provider<StageService> stageServiceProvider;
    private final RetryService retryService;
    private final Validator validator;


    @Inject
    public ActionProcessor(
            final DocstoreUpload docstoreUploadAction,
            final AcceptContextAction acceptContextAction,
            final CommentContextAction commentContextAction,
            final EmptyContextAction emptyContextAction,
            final ActionRepository actionRepository,
            ActionService actionService,
            WorkflowClassInstanceMetaRepository workflowClassInstanceMetaRepository,
            Provider<StageService> stageServiceProvider,
            RetryService retryService,
            Validator validator) {
        this.actionService = actionService;
        this.workflowClassInstanceMetaRepository = workflowClassInstanceMetaRepository;
        this.stageServiceProvider = stageServiceProvider;
        this.retryService = retryService;
        this.validator = validator;
        actionMap.put(ActionType.UPLOAD_DOCSTORE, docstoreUploadAction);
        actionMap.put(ActionType.ACCEPT_ACTION_CONTEXT, acceptContextAction);
        actionMap.put(ActionType.COMMENT_ACTION_CONTEXT, commentContextAction);
        actionMap.put(ActionType.EMPTY_CONTEXT_ACTION, emptyContextAction);
    }

    @SneakyThrows
    public ActionResponseContext executeAction(
        String workflowClassInstanceId,
        String stageId,
        String actionId)
    {
        Action action = actionService.getById(actionId);
        List<WorkflowClassInstanceMetaEntity> metadata = workflowClassInstanceMetaRepository
            .getWorkflowClassInstanceMeta(workflowClassInstanceId);
        ActionType actionType = action.getActionType();
        JsonNode actionParam = action.getActionConfig();
        BaseAction<? extends ActionParam> baseAction = actionMap.get(actionType);
        if (baseAction == null) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_ACTION_TYPE,
                    "No action registered for type: " + actionType);
        }
        return processAction(baseAction, actionParam, metadata.get(0));
    }

    private <P extends ActionParam> ActionResponseContext processAction(
            BaseAction<P> baseAction,
            JsonNode actionParam,
            WorkflowClassInstanceMetaEntity stageContext) {
        P param = null;
        if (actionParam != null) {
            param = MapperUtils.deserialize(actionParam, baseAction.getParamClass());
        }
        return baseAction.process(param, stageContext);
    }

    public void hibernateValidationOfActionParam(ActionType actionType,
                                                 JsonNode param) {
        try {
            ActionParam actionParam = MapperUtils.deserialize(param, actionMap.get(actionType)
                    .getParamClass());
            Set<ConstraintViolation<ActionParam>> violations = validator.validate(actionParam);
            if (!violations.isEmpty()) {
                Map<String, String> errorMessage = new HashMap<>();
                violations.forEach(v -> errorMessage.put(String.valueOf(v.getPropertyPath()), v.getMessage()));
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_ACTION_PARAM, errorMessage.toString());
            }
        } catch (DisputeException de) {
            log.error("Validation error for action type: {} with param: {}", actionType, param, de);
            throw de;
        }

    }

    private String getRetryIdentifier(String actionId,
                                      StageActionContext stageContext) {
        return stageContext.getStageId() + "-" + actionId;
    }

    public ActionTriggerType getTriggerType(ActionType actionType) {
        BaseAction<? extends ActionParam> baseAction = actionMap.get(actionType);
        if (baseAction == null) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_ACTION_TYPE,
                "No action registered for type: " + actionType);
        }
        return baseAction.getActionTriggerType();
    }
}
