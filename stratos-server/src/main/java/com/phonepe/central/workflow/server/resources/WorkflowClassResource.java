package com.phonepe.central.workflow.server.resources;

import static com.phonepe.olympus.im.models.core.CoreConstants.BEARER;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.google.inject.Inject;
import com.phonepe.central.workflow.request.workflow.WorkflowClassCreateRequest;
import com.phonepe.central.workflow.request.workflow.WorkflowClassSearchRequest;
import com.phonepe.central.workflow.request.workflow.WorkflowClassUpdateRequest;
import com.phonepe.central.workflow.response.workflow.WorkflowClassResponse;
import com.phonepe.central.workflow.server.service.WorkflowClassService;
import com.phonepe.merchant.platform.stratos.server.core.models.ApiState.ApiStateString;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import io.dropwizard.auth.Auth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Path("/v1/dms/workflow-class")
@Tag(name = "Dispute Management Service Workflow Class related API's")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
@SecurityScheme(name = BEARER, type = SecuritySchemeType.APIKEY, in = SecuritySchemeIn.HEADER, paramName = "Authorization")
@SecurityRequirement(name = BEARER)
public class WorkflowClassResource {

    private final WorkflowClassService workflowClassService;

    @POST
    @ExceptionMetered
    @RolesAllowed("dms_workflow_class/create")
    @Path("/")
    @Operation(summary = "Create Workflow class for Dispute management service")
    @ApiKillerMeta(tags = {ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response createWorkflowClass(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                        @NotNull @Valid WorkflowClassCreateRequest workflowClassCreateRequest) {
        try {
            log.info("Creating workflow class with request: {}", workflowClassCreateRequest);
            WorkflowClassResponse response= workflowClassService.createWorkflowClass(workflowClassCreateRequest,
                    serviceUserPrincipal.getUserAuthDetails().getUserDetails().getUserId());

            return Response.ok(response).build();

        } catch (DisputeException e) {
            log.error("Dispute Exception in creating workflow class with request: {}", workflowClassCreateRequest, e);
            throw e;
        } catch (Exception e) {
            log.error("Error in creating workflow class with request: {}", workflowClassCreateRequest, e);
            throw DisputeExceptionUtil.propagate(e);
        }
    }

    @POST
    @ExceptionMetered
    @RolesAllowed("dms_workflow_class/search")
    @Path("/search")
    @Operation(summary = "Search Workflow class for Dispute management service")
    @ApiKillerMeta(tags = {ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response searchWorkflowClass(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                        @NotNull @Valid WorkflowClassSearchRequest workflowClassSearchRequest) {
        try{
            log.info("Searching workflow class with request: {}", workflowClassSearchRequest);

            List<WorkflowClassResponse> response = workflowClassService.searchWorkflowClass(workflowClassSearchRequest);

            return Response.ok(response)
                    .build();

        } catch (DisputeException e) {
            log.error("Dispute Exception in searching workflow class with request: {}", workflowClassSearchRequest, e);
            throw e;
        } catch (Exception e) {
            log.error("Error in searching workflow class with request: {}", workflowClassSearchRequest, e);
            throw DisputeExceptionUtil.propagate(e);
        }
    }

    @PUT
    @ExceptionMetered
    @RolesAllowed("dms_workflow_class/update")
    @Path("/")
    @Operation(summary = "Update Workflow class for Dispute management service")
    @ApiKillerMeta(tags = {ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response updateWorkflowClass(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                        @NotNull @Valid WorkflowClassUpdateRequest workflowClassUpdateRequest) {
        try {
            log.info("Updating workflow class with ID: {} and request: {}", workflowClassUpdateRequest.getWorkflowClassId(), workflowClassUpdateRequest);

            WorkflowClassResponse response = workflowClassService.updateWorkflowClass(workflowClassUpdateRequest,
                    serviceUserPrincipal.getUserAuthDetails().getUserDetails().getUserId());

            return Response.ok(response)
                    .build();
        } catch (DisputeException e){
            log.error("Dispute Exception in updating workflow class request: {}", workflowClassUpdateRequest, e);
            throw e;
        } catch (Exception e){
            log.error("Error in updating workflow class and request: {}", workflowClassUpdateRequest, e);
            throw DisputeExceptionUtil.propagate(e);
        }
    }

    @DELETE
    @ExceptionMetered
    @RolesAllowed("dms_workflow_class/delete")
    @Path("/{workflowClassId}")
    @Operation(summary = "Delete Workflow class for Dispute management service")
    @ApiKillerMeta(tags = {ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response deleteWorkflowClass(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                        @PathParam("workflowClassId") @Parameter(required = true) String workflowClassId) {
        log.info("Deleting workflow class with ID: {}", workflowClassId);
        try {
            workflowClassService.deleteWorkflowClass(workflowClassId);

            return Response.ok("Workflow class deleted successfully")
                    .build();
        }
        catch (DisputeException e) {
            log.error("Dispute Exception in deleting workflow class with ID: {}", workflowClassId, e);
            throw e;
        }
        catch (Exception e) {
            log.error("Error in deleting workflow class with ID: {}", workflowClassId, e);
            throw DisputeExceptionUtil.propagate(e);
        }
    }

    @POST
    @ExceptionMetered
    @RolesAllowed("dms_workflow_class/update")
    @Path("/duplicate/{workflowClassId}")
    @Operation(summary = "Clone Workflow class for Dispute management service")
    @ApiKillerMeta(tags = {ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response cloneWorkflowClass(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                       @PathParam("workflowClassId") @Parameter(required = true) String workflowClassId) {
        try {
            log.info("Cloning workflow class id: {}", workflowClassId);

            WorkflowClassResponse response = workflowClassService.duplicateWorkflowClass(workflowClassId,
                    serviceUserPrincipal.getUserAuthDetails().getUserDetails().getUserId());

            return Response.ok(response).build();

        } catch (DisputeException e) {
            log.error("Dispute Exception in cloning workflow class id: {}", workflowClassId, e);
            throw e;
        } catch (Exception e) {
            log.error("Error in cloning workflow class id: {}", workflowClassId, e);
            throw DisputeExceptionUtil.propagate(e);
        }
    }

}
