package com.phonepe.central.workflow.server.resources;

import static com.phonepe.olympus.im.models.core.CoreConstants.BEARER;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.google.inject.Inject;
import com.phonepe.central.dispute.request.action.ActionCreateRequest;
import com.phonepe.central.dispute.request.action.ActionFilterParams;
import com.phonepe.central.workflow.action.response.Action;
import com.phonepe.central.workflow.action.response.PaginatedResponse;
import com.phonepe.central.workflow.server.service.ActionService;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.models.ApiState;
import com.phonepe.merchant.platform.stratos.server.core.models.ApiState.ApiStateString;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import io.dropwizard.auth.Auth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Path("/v1/dms/actions")
@Tag(name = "DMS action related API's")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
@SecurityScheme(name = BEARER, type = SecuritySchemeType.APIKEY, in = SecuritySchemeIn.HEADER, paramName = "Authorization")
@SecurityRequirement(name = BEARER)
public class ActionResource {

    private final ActionService actionService;

    @POST
    @ExceptionMetered
    @RolesAllowed("dms_action/create")
    @Path("/")
    @Operation(summary = "Create Action for Dispute management service")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response createAction(@Valid @NotNull final ActionCreateRequest actionCreateRequest,
                                 @Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal) {
        log.info("Create action request for payload : {}", actionCreateRequest);
        try {
            Action action = actionService.create(actionCreateRequest);
            return Response.ok(action)
                    .build();
        } catch (Exception e) {
            log.error("Error while creating action request: {}", actionCreateRequest, e);
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.UNABLE_TO_CREATE_ACTION, e);
        }
    }

    @POST
    @ExceptionMetered
    @Path("/search")
    @RolesAllowed("dms_action/search")
    @Operation(summary = "get list of actions for filter params passed")
    @ApiKillerMeta(tags = {ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response filterActions(@Valid @NotNull final ActionFilterParams actionFilterParams,
                                  @Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal) {
        log.info("Search actions request with filter params : {}", actionFilterParams);
        try {
            PaginatedResponse<Action> actionList = actionService.search(actionFilterParams);
            return Response.ok(actionList)
                    .build();
        } catch (Exception e) {
            log.error("Error while searching actions: {}", actionFilterParams, e);
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.INTERNAL_SERVER_ERROR, e);

        }
    }

    @DELETE
    @ExceptionMetered
    @RolesAllowed("dms_action/remove")
    @Path("/{actionId}")
    @Operation(summary = "Inactive a action for Dispute management service")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response deleteAction(@PathParam("actionId") @NotBlank final String actionId,
                                 @Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal) {
        log.info("Delete action request for actionId : {}", actionId);
        try {
            actionService.deleteAction(actionId);
            return Response.ok()
                    .entity("Action with actionId: " + actionId + " has been removed successfully")
                    .build();
        } catch (Exception e) {
            log.error("Error while deleting action : {}", actionId, e);
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.INTERNAL_SERVER_ERROR, e);

        }
    }
}
