package com.phonepe.central.workflow.server.mariadb.entities;

import com.fasterxml.jackson.databind.JsonNode;
import com.phonepe.central.dispute.request.retry.RetryConfig;
import com.phonepe.central.workflow.action.ActionMode;
import com.phonepe.central.workflow.action.ActionState;
import com.phonepe.central.workflow.action.ActionType;
import com.phonepe.central.workflow.action.libraryaction.ActionTriggerType;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.PrimaryKey;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Sharded;
import java.io.Serializable;
import java.time.LocalDateTime;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * <AUTHOR>
 */

@Getter
@Setter
@ToString
@DynamicInsert
@DynamicUpdate
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "dms_action", uniqueConstraints = {@UniqueConstraint(columnNames = {"tenant_id", "name", "action_mode"}),
        @UniqueConstraint(columnNames = {"action_id"})})
@FieldNameConstants
public class ActionEntity implements  Serializable, Sharded {

    private static final long serialVersionUID = -6548024317040241952L;

    @Id
    @Column(name = "id", unique = true, insertable = false, updatable = false, nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    @NotBlank
    @Column(name = "action_id", nullable = false)
    private String actionId;

    @NotBlank
    @Column(name = "tenant_id", nullable = false)
    private String tenantId;

    @NotBlank
    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "description")
    private String description;


    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "state", nullable = false)
    private ActionState state;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "action_type", nullable = false)
    private ActionType actionType;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "action_trigger_type", nullable = false)
    private ActionTriggerType triggerType;

    @NotNull
    @Type(type = "json")
    @Basic(fetch = FetchType.LAZY)
    @Column(name = "action_config", columnDefinition = "json", nullable = false)
    private JsonNode actionConfig;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "action_mode", nullable = false)
    private ActionMode actionMode;

    @Type(type = "json")
    @Basic(fetch = FetchType.LAZY)
    @Column(name = "retry_config", columnDefinition = "json")
    private RetryConfig retryConfig;

    @NotNull
    @Column(name = "is_action_failure_ignorable", nullable = false)
    private Boolean isActionFailureIgnorable;

    @CreationTimestamp
    @Column(name = "created_at", columnDefinition = "datetime(3) DEFAULT current_timestamp", updatable = false, nullable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", columnDefinition = "datetime(3) DEFAULT current_timestamp ON UPDATE current_timestamp", nullable = false)
    private LocalDateTime updatedAt;

    @Override
    public String getShardingKey() {
        return tenantId;
    }
}
