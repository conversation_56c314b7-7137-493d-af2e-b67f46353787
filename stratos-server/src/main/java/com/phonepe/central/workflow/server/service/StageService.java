package com.phonepe.central.workflow.server.service;

import com.collections.CollectionUtils;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.central.dispute.tenant.Tenant;
import com.phonepe.central.dms.server.service.TenantService;
import com.phonepe.central.dms.server.utils.DMSUtil;
import com.phonepe.central.stratos.penalty.escalation.EscalationLevelConfig;
import com.phonepe.central.stratos.penalty.escalation.EscalationMatrix;
import com.phonepe.central.stratos.penalty.escalation.EscalationType;
import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import com.phonepe.central.stratos.penalty.server.service.EscalationService;
import com.phonepe.central.workflow.action.response.Action;
import com.phonepe.central.workflow.server.mariadb.entities.StageEntity;
import com.phonepe.central.workflow.server.mariadb.repository.impl.StageRepository;
import com.phonepe.central.workflow.server.util.StageConvertorUtils;
import com.phonepe.central.workflow.server.validation.stage.create.StageCreateRequestValidation;
import com.phonepe.central.workflow.server.validation.stage.update.StageUpdateRequestValidation;
import com.phonepe.central.workflow.stage.Stage;
import com.phonepe.central.workflow.stage.StageState;
import com.phonepe.central.workflow.stage.request.StageCreationRequest;
import com.phonepe.central.workflow.stage.request.StageEntitySearchRequest;
import com.phonepe.central.workflow.stage.request.StageEntitySearchRequest.StageEntitySearchRequestBuilder;
import com.phonepe.central.workflow.stage.request.StageSearchRequest;
import com.phonepe.central.workflow.stage.request.StageUpdateRequest;
import com.phonepe.central.workflow.stage.request.StageUpdateRequestVisitor;
import com.phonepe.central.workflow.stage.request.UpdateEscalationLevelConfigRequest;
import com.phonepe.central.workflow.stage.request.UpdateStageActionRequest;
import com.phonepe.central.workflow.stage.response.StageCreationResponse;
import com.phonepe.central.workflow.stage.response.StageResponse;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class StageService {

    private final StageRepository stageRepository;
    private final EscalationService escalationService;
    private final TenantService tenantService;

    private final ActionService actionService;

    private final StageCreateRequestValidation<StageCreationRequest> stageCreateRequestValidation;

    private final StageUpdateRequestValidation<StageUpdateRequest> stageUpdateRequestValidation;

    public StageCreationResponse createStage(StageCreationRequest stageCreationRequest) {
        stageCreateRequestValidation.validate(stageCreationRequest);
        TenantInfo tenantInfo = getTenantInfoFromTenantId(stageCreationRequest.getTenantId());
        StageEntity stageEntity = StageConvertorUtils.toStageEntity(stageCreationRequest);
        StageEntity storeStageEntity = stageRepository.save(stageEntity, storeEntity -> {
                    if (CollectionUtils.isNotEmpty(stageCreationRequest.getEscalationLevelConfigs())) {
                        log.info("Creating escalation for stage with ID: {}", storeEntity.getStageId());
                        createEscalationForStage(tenantInfo, stageEntity.getStageId(),
                                stageCreationRequest.getEscalationLevelConfigs());
                    }
                    return stageEntity;
                })
                .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.STAGE_CREATION_FAILED));
        log.info("Successfully created stage with ID: {}", storeStageEntity.getStageId());
        return StageConvertorUtils.toStageCreationResponse(storeStageEntity);
    }


    public Stage getStageByStageId(final String stageId,
                                   String tenantId) {
        StageEntity stageEntity = getStageEntityByStageId(stageId, tenantId);
        List<Action> actions = actionService.getActions(stageEntity.getActionIds(), tenantId);

        return StageConvertorUtils.toStage(stageEntity, actions, getTenantInfoFromTenantId(tenantId),
                escalationService.getEscalationMatrix(stageId)
                        .getLevelConfig());
    }

    public StageResponse duplicateStage(String stageId) {
        List<StageResponse> availableStageList = searchStages(StageSearchRequest.builder()
                .stageId(stageId)
                .build());
        if (CollectionUtils.isEmpty(availableStageList)) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.STAGE_NOT_FOUND,
                    "Stage not found with stageId: " + stageId);
        }
        StageResponse stageToBeCloned = availableStageList.get(0);
        StageEntity cloneStageEntity = StageConvertorUtils.toCloneStageEntity(stageToBeCloned);
        TenantInfo tenantInfo = getTenantInfoFromTenantId(stageToBeCloned.getTenantId());
        Set<EscalationLevelConfig> escalationLevelConfig = escalationService.getEscalationMatrix(
                        stageToBeCloned.getStageId())
                .getLevelConfig();
        StageEntity storeStageEntity = stageRepository.save(cloneStageEntity, saveEntity -> {
                    if (Objects.nonNull(escalationLevelConfig) && !escalationLevelConfig.isEmpty()) {
                        log.info("Creating escalation for stage with ID: {}", cloneStageEntity.getStageId());
                        createEscalationForStage(tenantInfo, cloneStageEntity.getStageId(), escalationLevelConfig);
                    }
                    return saveEntity;
                })
                .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.STAGE_CREATION_FAILED));
        return buildStageResponse(storeStageEntity);
    }

    public StageResponse updateStage(StageUpdateRequest updateRequest,
                                     String stageId) {
        stageUpdateRequestValidation.validate(updateRequest);
        return updateRequest.accept(new StageUpdateRequestVisitor<>() {
            @Override
            public StageResponse visit(UpdateStageActionRequest updateRequest) {
                log.info("Processing action IDs update for stage: {}", stageId);
                stageRepository.updateActionIds(updateRequest.getActionIds(), updateRequest.getTenantId(), stageId);
                return searchStages(StageSearchRequest.builder()
                        .stageId(stageId)
                        .tenantId(updateRequest.getTenantId())
                        .build()).stream()
                        .findFirst()
                        .orElse(null);
            }

            @Override
            public StageResponse visit(UpdateEscalationLevelConfigRequest updateRequest) {
                log.info("Processing escalation level config update for stage: {}", stageId);
                TenantInfo tenantInfo = getTenantInfoFromTenantId(updateRequest.getTenantId());
                updateEscalationForStage(tenantInfo, updateRequest.getEscalationLevelConfigs(), stageId);
                stageRepository.updateByUserDetails(updateRequest.getTenantId(), stageId);
                return searchStages(StageSearchRequest.builder()
                        .stageId(stageId)
                        .tenantId(updateRequest.getTenantId())
                        .build()).stream()
                        .findFirst()
                        .orElse(null);
            }
        });
    }

    public StageResponse suspendStage(final String stageId,
                                      final String tenantId) {
        StageEntity stageEntity = getStageEntityByStageId(stageId, tenantId);

        stageEntity.setState(StageState.SUSPENDED);
        stageEntity.setUpdatedBy(DMSUtil.getUserId());
        StageEntity updatedStageEntity = (stageRepository.save(stageEntity)
                .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.STAGE_UPDATION_FAILED,
                        Map.of(Constants.MESSAGE, "Failed to suspend stage with ID: " + stageId, "tenantId",
                                tenantId))));
        return buildStageResponse(updatedStageEntity);
    }

    public Boolean activateStage(final Stage stage,
                                 final String activateByUserId) {
        log.info("Activating stage by user Id: {} with stage details : {}", activateByUserId, stage);

        List<StageResponse> availableStageList = this.searchStages(StageSearchRequest.builder()
                .stageId(stage.getStageId())
                .build());
        if (CollectionUtils.isNotEmpty(availableStageList)) {
            Optional<StageResponse> stagePresent = availableStageList.stream()
                    .filter(s -> s.getStageState()
                            .equals(StageState.CREATED))
                    .findAny();
            stagePresent.ifPresentOrElse(
                    s -> stageRepository.activateStage(stage.getStageId(), s.getTenantId(), activateByUserId), () -> {
                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.STAGE_UPDATION_FAILED,
                                "Only stages in CREATED state can be activated");
                    });
            return true;
        }
        return false;
    }

    public List<StageResponse> searchStages(StageSearchRequest searchRequest) {
        StageEntitySearchRequestBuilder builder = StageEntitySearchRequest.builder()
                .tenantId(searchRequest.getTenantId())
                .stageId(searchRequest.getStageId())
                .name(searchRequest.getName())
                .stageState(searchRequest.getStageState());
        List<StageEntity> stageEntities = stageRepository.search(builder.build());
        return stageEntities.stream()
                .map(this::buildStageResponse)
                .toList();
    }

    private void createEscalationForStage(TenantInfo tenantInfo,
                                          String stageId,
                                          Set<EscalationLevelConfig> escalationLevelConfigs) {
        try {
            escalationService.createEscalation(tenantInfo, EscalationMatrix.builder()
                    .escalationType(EscalationType.DMS_STAGE)
                    .levelConfig(escalationLevelConfigs)
                    .build(), stageId);
            log.info("Successfully created escalation for stageId: {}", stageId);
        } catch (Exception e) {
            log.error("Failed to create escalation for stageId: {}", stageId, e);
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.STAGE_CREATION_FAILED,
                    "Failed to create escalation configuration for stage");
        }
    }

    private void updateEscalationForStage(TenantInfo tenantInfo,
                                          Set<EscalationLevelConfig> escalationLevelConfigs,
                                          String stageId) {
        try {
            escalationService.updateEscalation(tenantInfo, EscalationMatrix.builder()
                    .escalationType(EscalationType.DMS_STAGE)
                    .levelConfig(escalationLevelConfigs)
                    .build(), stageId);
            log.info("Successfully updated escalation for stageId: {}", stageId);
        } catch (Exception e) {
            log.error("Failed to update escalation for stageId: {}", stageId, e);
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.STAGE_UPDATION_FAILED,
                    "Failed to update escalation configuration");
        }
    }

    private StageEntity getStageEntityByStageId(final String stageId,
                                                String tenantId) {
        return stageRepository.select(stageId, tenantId)
                .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.STAGE_NOT_FOUND));
    }

    private TenantInfo getTenantInfoFromTenantId(String tenantId) {
        Tenant tenant = tenantService.getTenantByTenantId(tenantId);
        return TenantInfo.builder()
                .name(tenant.getName())
                .subCategory(tenant.getSubCategory())
                .build();
    }

    private StageResponse buildStageResponse(StageEntity stageEntity) {
        Set<EscalationLevelConfig> escalationLevelConfig = escalationService.getEscalationMatrix(
                        stageEntity.getStageId())
                .getLevelConfig();
        return StageConvertorUtils.toStageResponse(stageEntity, escalationLevelConfig);
    }

}
