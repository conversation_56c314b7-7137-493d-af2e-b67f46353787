package com.phonepe.central.workflow.server.mariadb.entities;

import com.phonepe.central.workflow.workflow.instance.WorkflowClassInstanceState;
import com.phonepe.services.ppo.core.storage.mariadb.Sharded;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "dms_workflow_class_instance", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"workflow_class_id", "workflow_class_instance_id"}),
        @UniqueConstraint(columnNames = {"workflow_class_instance_id"})})
@FieldNameConstants
public class WorkflowClassInstanceEntity implements Sharded {

    private static final long serialVersionUID = -6548024317040241952L;

    @Id
    @Column(name = "id", unique = true, insertable = false, updatable = false, nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    @NotBlank
    @Column(name = "workflow_class_instance_id", nullable = false, updatable = false)
    private String workflowClassInstanceId;

    @NotBlank
    @Column(name = "workflow_class_id", nullable = false, updatable = false)
    private String workflowClassId;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "state", nullable = false)
    private WorkflowClassInstanceState state;

    @NotBlank
    @Column(name = "workflow_current_stage_id", nullable = false)
    private String workflowCurrentStageId;

    @NotBlank
    @Column(name = "created_by", updatable = false, nullable = false)
    private String createdBy;

    @Column(name = "partition_id", nullable = false, updatable = false)
    private int partitionId;

    @CreationTimestamp
    @Column(name = "created_at", columnDefinition = "datetime(3) DEFAULT current_timestamp", updatable = false, nullable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", columnDefinition = "datetime(3) DEFAULT current_timestamp ON UPDATE current_timestamp", nullable = false)
    private LocalDateTime updatedAt;

    @Override
    public String getKey() {
        return workflowClassId;
    }


}
