package com.phonepe.central.workflow.server.resources;

import static com.phonepe.olympus.im.models.core.CoreConstants.BEARER;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.google.inject.Inject;
import com.phonepe.central.workflow.request.workflow.WorkflowClassInstanceRegisterRequest;
import com.phonepe.central.workflow.request.workflow.WorkflowClassInstanceSearchRequest;
import com.phonepe.central.workflow.response.workflow.WorkflowClassInstanceResponse;
import com.phonepe.central.workflow.server.service.WorkflowClassInstanceService;
import com.phonepe.merchant.platform.stratos.server.core.models.ApiState.ApiStateString;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import io.dropwizard.auth.Auth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.DefaultValue;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Path("/v1/dms/workflow-class-instance")
@Tag(name = "Dispute Management Service Workflow Class Instance related API's")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
@SecurityScheme(name = BEARER, type = SecuritySchemeType.APIKEY, in = SecuritySchemeIn.HEADER, paramName = "Authorization")
@SecurityRequirement(name = BEARER)
public class WorkflowClassInstanceResource {

    private final WorkflowClassInstanceService workflowClassInstanceService;

    @POST
    @ExceptionMetered
    @RolesAllowed("dms_workflow_class_instance/register")
    @Path("/")
    @Operation(summary = "Register for workflow class for Dispute management service")
    @ApiKillerMeta(tags = {ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response register(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                        @NotNull @Valid WorkflowClassInstanceRegisterRequest workflowClassInstanceRegisterRequest) {
        try {
            log.info("Registering workflow class instance with request: {}", workflowClassInstanceRegisterRequest);

            WorkflowClassInstanceResponse response = workflowClassInstanceService
                    .registerWorkflowClassInstance(workflowClassInstanceRegisterRequest,
                            serviceUserPrincipal.getUserAuthDetails().getUserDetails().getUserId());

            return Response.ok(response)
                    .build();
        }
        catch (DisputeException e) {
            log.error("Dispute Exception in registering workflow instance with request: {}",
                    workflowClassInstanceRegisterRequest, e);
            throw e;
        }
        catch (Exception e) {
            log.error("Error in registering workflow instance with request: {}",
                    workflowClassInstanceRegisterRequest, e);
            throw DisputeExceptionUtil.propagate(e);
        }
    }

    @POST
    @ExceptionMetered
    @RolesAllowed("dms_workflow_instance/search")
    @Path("/search")
    @Operation(summary = "Search Workflow class for Dispute management service")
    @ApiKillerMeta(tags = {ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response searchWorkflowClassInstance(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                        @QueryParam("offset") @DefaultValue("0") @Parameter(description = "Offset for pagination") int offset,
                                        @QueryParam("limit") @DefaultValue("1000") @Parameter(description = "Limit for pagination") int limit,
                                        @NotNull @Valid WorkflowClassInstanceSearchRequest workflowClassInstanceSearchRequest) {
        try {
            log.info("Searching workflow class with request: {}, offset: {}, limit: {}",
                    workflowClassInstanceSearchRequest, offset, limit);

            List<WorkflowClassInstanceResponse> response = workflowClassInstanceService.searchWorkflowClassInstances(
                    workflowClassInstanceSearchRequest, offset, limit);

            return Response.ok(response)
                    .build();
        }
        catch (DisputeException e) {
            log.error("Dispute Exception in searching workflow instances with request: {}",
                    workflowClassInstanceSearchRequest, e);
            throw e;
        }
        catch (Exception e) {
            log.error("Error in searching workflow instance with request: {}", workflowClassInstanceSearchRequest, e);
            throw DisputeExceptionUtil.propagate(e);
        }
    }

}
