package com.phonepe.central.workflow.server.mariadb.entities;

import com.phonepe.central.workflow.stage.StageState;
import com.phonepe.services.ppo.core.storage.mariadb.Sharded;
import io.hypersistence.utils.hibernate.type.json.JsonStringType;
import java.time.LocalDateTime;
import java.util.List;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TypeDefs({@TypeDef(name = "json", typeClass = JsonStringType.class)})
@Entity
@Table(name = "dms_stage", uniqueConstraints = {
        @UniqueConstraint(name = "UK_STAGE_ROW", columnNames = {"tenant_id", "name", "version"}),
        @UniqueConstraint(name = "UK_STAGE_ID", columnNames = {"stage_id"})})
@FieldNameConstants
public class StageEntity implements Sharded {

    private static final long serialVersionUID = -6548024317040241952L;

    @Id
    @Column(name = "id", unique = true, insertable = false, updatable = false, nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;
    @NotBlank
    @Column(name = "stage_id", nullable = false, updatable = false)
    private String stageId;

    @NotBlank
    @Column(name = "name", nullable = false, updatable = false)
    private String name;

    @NotBlank
    @Column(name = "description", nullable = false)
    private String description;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "state", nullable = false)
    private StageState state;

    @NotBlank
    @Column(name = "tenant_id", nullable = false, updatable = false)
    private String tenantId;

    @NotNull
    @Type(type = "json")
    @Basic(fetch = FetchType.LAZY)
    @Column(name = "action_ids", columnDefinition = "json", nullable = false)
    private List<String> actionIds;

    @Column(name = "approved_by", nullable = true)
    private String approvedBy;

    @NotBlank
    @Column(name = "updated_by", nullable = false)
    private String updatedBy;

    @NotBlank
    @Column(name = "created_by", updatable = false, nullable = false)
    private String createdBy;

    @NotNull
    @Column(name = "version", nullable = false, updatable = false)
    private Integer version = 1;


    @CreationTimestamp
    @Column(name = "created_at", columnDefinition = "datetime(3) DEFAULT current_timestamp", updatable = false, nullable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", columnDefinition = "datetime(3) DEFAULT current_timestamp ON UPDATE current_timestamp", nullable = false)
    private LocalDateTime updatedAt;


    @Override
    public String getKey() {
        return tenantId;
    }
}
