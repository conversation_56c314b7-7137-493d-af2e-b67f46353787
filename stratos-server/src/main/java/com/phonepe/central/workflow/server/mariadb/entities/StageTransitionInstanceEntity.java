package com.phonepe.central.workflow.server.mariadb.entities;

import com.phonepe.central.workflow.action.response.context.ActionResponseContextType;
import com.phonepe.services.ppo.core.storage.mariadb.Sharded;
import java.time.LocalDateTime;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "dms_stage_transition_instance", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"workflow_class_instance_id", "source_stage_id", "action_id"})}, indexes = {
        @Index(name = "idx_workflow_instance_trans", columnList = "workflow_class_instance_id,source_stage_id")})
@FieldNameConstants
public class StageTransitionInstanceEntity implements Sharded {

    private static final long serialVersionUID = -6548024317040241952L;

    @Id
    @Column(name = "id", unique = true, insertable = false, updatable = false, nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    @NotBlank
    @Column(name = "workflow_class_instance_id", nullable = false, updatable = false)
    private String workflowClassInstanceId;

    @NotBlank
    @Column(name = "source_stage_id", nullable = false, updatable = false)
    private String sourceStageId;

    @NotBlank
    @Column(name = "target_stage_id", nullable = false, updatable = false)
    private String targetStageId;

    @NotBlank
    @Column(name = "action_id", nullable = false, updatable = false)
    private String actionId;

    @NotBlank
    @Column(name = "processed_by", updatable = false, nullable = false)
    private String processedBy;

    @Column(name = "partition_id", nullable = false, updatable = false)
    private int partitionId;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "action_response_context_type", nullable = false)
    private ActionResponseContextType actionResponseContextType;


    //TODO : It should be one of the class of : ActionResponseContext
    @Type(type = "json")
    @Basic(fetch = FetchType.LAZY)
    @Column(name = "action_response_context", columnDefinition = "json")
    private String actionResponseContext;

    @CreationTimestamp
    @Column(name = "created_at", columnDefinition = "datetime(3) DEFAULT current_timestamp", updatable = false, nullable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", columnDefinition = "datetime(3) DEFAULT current_timestamp ON UPDATE current_timestamp", nullable = false)
    private LocalDateTime updatedAt;

    @Override
    public String getKey() {
        return workflowClassInstanceId;
    }


}
