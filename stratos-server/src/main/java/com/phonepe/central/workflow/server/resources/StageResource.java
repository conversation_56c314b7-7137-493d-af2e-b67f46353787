package com.phonepe.central.workflow.server.resources;

import static com.phonepe.olympus.im.models.core.CoreConstants.BEARER;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.google.inject.Inject;
import com.phonepe.central.dispute.request.stage.StageCreateRequest;
import com.phonepe.central.dispute.request.stage.StageSearchRequest;
import com.phonepe.merchant.platform.stratos.server.core.models.ApiState.ApiStateString;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.olympus.im.models.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import io.dropwizard.auth.Auth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Path("/v1/dms/stages")
@Tag(name = "Dispute Management Service Stage related API's")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
@SecurityScheme(name = BEARER, type = SecuritySchemeType.APIKEY, in = SecuritySchemeIn.HEADER, paramName = "Authorization")
@SecurityRequirement(name = BEARER)
public class StageResource {

    @POST
    @ExceptionMetered
    @RolesAllowed("dms_stage/create")
    @Path("/")
    @Operation(summary = "Create Stage for Dispute management service")
    @ApiKillerMeta(tags = {ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response createStage(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                @HeaderParam(Constants.AUTHORIZATION) @Parameter(required = true, hidden = true) String authToken,
                                @NotNull @Valid StageCreateRequest stageCreateRequest) {
        log.info("Creating stage with request: {}", stageCreateRequest);
        return Response.ok()
                .entity("Stage created successfully with request: " + stageCreateRequest)
                .build();
    }

    @POST
    @ExceptionMetered
    @RolesAllowed("dms_stage/search")
    @Path("/search")
    @Operation(summary = "Search Stage for Dispute management service")
    @ApiKillerMeta(tags = {ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response searchStage(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                @HeaderParam(Constants.AUTHORIZATION) @Parameter(required = true, hidden = true) String authToken,
                                @NotNull @Valid StageSearchRequest stageSearchRequest) {
        log.info("Searching stage with request: {}", stageSearchRequest);
        return Response.ok()
                .entity("Stage search results for request: " + stageSearchRequest)
                .build();
    }

    @DELETE
    @ExceptionMetered
    @RolesAllowed("dms_stage/remove")
    @Path("/{stageId}")
    @Operation(summary = "Inactive a stage for Dispute management service")
    @ApiKillerMeta(tags = {ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response removeStage(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                @PathParam("stageId") final String stageId) {
        log.info("Received request to remove stage with stageId: {}", stageId);
        return Response.ok()
                .entity("Stage with ID " + stageId + " has been successfully removed.")
                .build();

    }

}
