package com.phonepe.central.stratos.penalty.server.resources;


import static com.phonepe.olympus.im.models.core.CoreConstants.BEARER;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.google.inject.Inject;
import com.phonepe.central.stratos.penalty.disbursement.PenaltyDisbursement;
import com.phonepe.central.stratos.penalty.request.penalty.PenaltyDateRangeSearchRequest;
import com.phonepe.central.stratos.penalty.request.penalty.PenaltySearchRequest;
import com.phonepe.central.stratos.penalty.response.PenaltyResponse;
import com.phonepe.central.stratos.penalty.server.service.PenaltyAuthorizationService;
import com.phonepe.central.stratos.penalty.server.service.PenaltyService;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.models.ApiState.ApiStateString;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.PenaltyPermissionsConstants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.olympus.im.models.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import io.dropwizard.auth.Auth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.concurrent.TimeUnit;
import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.Status;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Path("/v1/penalty")
@Tag(name = "Penalty Instance Related APIs")
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
@SecurityScheme(name = BEARER, type = SecuritySchemeType.APIKEY, in = SecuritySchemeIn.HEADER, paramName = "Authorization")
@SecurityRequirement(name = BEARER)
public class PenaltyResource {

    private final PenaltyService penaltyService;

    private final PenaltyAuthorizationService penaltyAuthorizationService;

    @POST
    @ExceptionMetered
    @RolesAllowed(PenaltyPermissionsConstants.PENALTY_INSTANCE_VIEW_PERMISSION)
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/search")
    @Operation(summary = "Search penalties for penaltyClassId")
    @ApiKillerMeta(tags = {ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response getPenalties(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                 @NotNull @Valid PenaltySearchRequest penaltySearchRequest) {
        try {
            boolean isAuthorised = penaltyAuthorizationService.isTenantAuthorizedToViewPenaltyInstance(
                    serviceUserPrincipal.getUserAuthDetails(), penaltySearchRequest.getPenaltyClassId());
            if (!isAuthorised) {
                return Response.status(Status.FORBIDDEN)
                        .entity("User is not authorized to view penalty instance for tenant")
                        .build();
            }
            log.info("Searching for penalty search request classID {}", penaltySearchRequest.getPenaltyClassId());
            List<PenaltyResponse> listOfPenalty = penaltyService.fetchTenantPenalties(penaltySearchRequest);
            return Response.ok(listOfPenalty)
                    .build();
        } catch (Exception exception) {
            log.error("Error in searching for penalty search request classID {} with exception {}",
                    penaltySearchRequest.getPenaltyClassId(), exception);
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.PENALTY_GET_ERROR, exception);
        }
    }

    @POST
    @ExceptionMetered
    @RolesAllowed(PenaltyPermissionsConstants.PENALTY_INSTANCE_RECONCILIATION_PERMISSION)
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/reconcile")
    @Operation(summary = "Reconcile penalties for date range")
    @ApiKillerMeta(tags = {ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response reconcileByDateRange(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                          @NotNull @Valid PenaltyDateRangeSearchRequest dateRangeSearchRequest) {
        try {
            if (dateRangeSearchRequest.getDateRangeRequest().getDifferenceInTimeUnit(TimeUnit.DAYS) > 1) {
                return Response.status(Status.BAD_REQUEST)
                        .entity("More than One day range scan is not supported")
                        .build();
            }
            log.info("Reconcile with dateRangeSearchRequest unit {}", dateRangeSearchRequest);
            penaltyService.reconcile(dateRangeSearchRequest);
            return Response.ok("Success")
                    .build();
        } catch (Exception exception) {
            log.error("Exception in processing the reconcile request {}", dateRangeSearchRequest, exception);
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.PENALTY_RECONCILE_ERROR, exception);
        }
    }

    @POST
    @ExceptionMetered
    @RolesAllowed(PenaltyPermissionsConstants.PENALTY_INSTANCE_RECONCILIATION_PERMISSION)
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/{penaltyClassId}/{penaltyId}/reconcile/callback")
    @Operation(summary = "Reconcile penalty")
    @ApiKillerMeta(tags = {ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response reconcileByPenaltyId(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                          @PathParam("penaltyClassId") final String penaltyClassId,
                                          @PathParam("penaltyId") final String penaltyId) {
        try {
            log.info("Reconcile with penalty with classId {} penaltyId {}", penaltyClassId, penaltyId);
            penaltyService.reconcile(penaltyClassId, penaltyId);
            return Response.ok("Success")
                    .build();
        } catch (Exception exception) {
            log.error("Error in reconcile with penalty with classId {} penaltyId {}", penaltyClassId, penaltyId,
                    exception);
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.PENALTY_RECONCILE_ERROR, exception);
        }
    }

    @GET
    @ExceptionMetered
    @RolesAllowed(PenaltyPermissionsConstants.PENALTY_INSTANCE_VIEW_PERMISSION)
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/{penaltyClassId}/{penaltyId}/disbursement")
    @Operation(summary = "Get Penalty disbursement  for penaltyID")
    @ApiKillerMeta(tags = {ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response getDisbursementDetails(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                         @PathParam("penaltyClassId") final String penaltyClassId,
                                         @PathParam("penaltyId") final String penaltyId) {
        try {
            log.info("Fetching disbursement details for penalty with classId {} penaltyId {}", penaltyClassId, penaltyId);
            PenaltyDisbursement penaltyDisbursement = penaltyService.getDisbursement(penaltyClassId, penaltyId);
            return Response.ok(penaltyDisbursement)
                    .build();
        } catch (Exception exception) {
            log.error("Error in reconcile with penalty with classId {} penaltyId {}", penaltyClassId, penaltyId,
                    exception);
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.PENALTY_GET_ERROR, exception);
        }
    }
}
