package com.phonepe.central.stratos.penalty.server.resources;

import static com.phonepe.olympus.im.models.core.CoreConstants.BEARER;

import com.aerospike.client.query.IndexType;
import com.codahale.metrics.annotation.ExceptionMetered;
import com.google.inject.Inject;
import com.phonepe.central.stratos.penalty.server.aerospike.AeroSpikeRepository;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.models.ApiState;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.olympus.im.client.annotations.AccessAllowed;
import com.phonepe.olympus.im.models.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import io.dropwizard.auth.Auth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Path("/v1/penalty/housekeeping")
@Tag(name = "Penalty Probable Related APIs")
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
@SecurityScheme(name = BEARER, type = SecuritySchemeType.APIKEY, in = SecuritySchemeIn.HEADER, paramName = "Authorization")
@SecurityRequirement(name = BEARER)

public class HouseKeepingResource {

    private final AeroSpikeRepository aeroSpikeRepository;

    @GET
    @ExceptionMetered
    @AccessAllowed
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/createIndex/{binName}/{indexType}")
    @Operation(summary = "Get Penalty probable configs for ID")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response createBinIndex(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                   @PathParam("binName") final String binName,@PathParam("indexType") final IndexType indexType) {
        try {
            aeroSpikeRepository.createSecondaryIndex(binName,indexType);
            return Response.ok("Index created successfully")
                    .build();
        } catch (Exception exception) {
            log.error("Exception in processing the probable getting paginated request {}", "penaltyClassId", exception);
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.PROBABLE_GET_ERROR, exception);
        }

    }

}
