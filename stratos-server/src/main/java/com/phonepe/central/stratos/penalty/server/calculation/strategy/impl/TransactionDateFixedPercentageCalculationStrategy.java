package com.phonepe.central.stratos.penalty.server.calculation.strategy.impl;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.central.stratos.penalty.growth.calculation.impl.TransactionDateFixedPercentageCalculation;
import com.phonepe.central.stratos.penalty.meta.PenaltyClassDetail;
import com.phonepe.central.stratos.penalty.server.calculation.strategy.PenaltyCalculationStrategy;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.PenaltyEntity;
import com.phonepe.central.stratos.penalty.server.util.PenaltyEvaluatorUtil;
import com.phonepe.central.stratos.penalty.server.util.PenaltyUtil;
import com.phonepe.merchant.platform.stratos.server.core.clients.PaymentsTxnlClient;
import lombok.RequiredArgsConstructor;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;

@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class TransactionDateFixedPercentageCalculationStrategy implements PenaltyCalculationStrategy
        <TransactionDateFixedPercentageCalculation> {

    private final PaymentsTxnlClient paymentsTxnlClient;

    @Override
    public Long calculate(PenaltyClassDetail penaltyClassDetail, PenaltyEntity penaltyEntity,
                          TransactionDateFixedPercentageCalculation calculation) {
        BigDecimal percentageValue = calculation.getPercentage()
                .multiply(BigDecimal.valueOf(penaltyEntity.getTransactionAmount()))
                .divide(new BigDecimal("100"));
        var transactionDate = PenaltyUtil.getLocalDateTime(paymentsTxnlClient.getTransactionDetails(penaltyEntity
                .getTransactionId()).getData().getReceivedPayment().getReceivedAt());

        LocalDateTime penaltyAppliedTime = transactionDate.plus(penaltyClassDetail.getCriteria().getLeeway());

        Long noOfUnits = PenaltyEvaluatorUtil.getNumberOfUnits(penaltyClassDetail.getGrowthRate().getUnit(),
                penaltyAppliedTime, penaltyEntity.getPenaltyId());
        return percentageValue.multiply(BigDecimal.valueOf(noOfUnits))
                .plus()
                .add(BigDecimal.valueOf(penaltyEntity.getInitialPenaltyAmount()))
                .longValue();
    }
}
