package com.phonepe.central.stratos.penalty.server.service;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.central.stratos.penalty.Penalty;
import com.phonepe.central.stratos.penalty.probables.PenaltyProbable;
import com.phonepe.central.stratos.penalty.server.util.PenaltyConvertorUtils;
import com.phonepe.platform.clockwork.model.ClockworkResponse;
import com.phonepe.platform.clockwork.model.SchedulingRequest;
import com.phonepe.platform.clockwork.model.SchedulingResponse;
import com.phonepe.verified.kaizen.clients.internal.ClockworkClient;
import io.appform.functionmetrics.MonitoredFunction;
import java.util.Date;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class ClockworkService {

    private final ClockworkClient clockworkClient;

    private static final String CLOCKWORK_PENALTY_CLIENT_ID = "dispute_penalty";

    @MonitoredFunction
    public String scheduleProbableAtDueTime(final PenaltyProbable probableEntity) {
        try {
            log.info("Schedule a job for probable at due time for {}", probableEntity);
            SchedulingRequest schedulingRequest = PenaltyConvertorUtils.penaltyProbableToSchedulingRequest(
                    probableEntity);
            ClockworkResponse<SchedulingResponse> response = clockworkClient.schedule(schedulingRequest,
                    CLOCKWORK_PENALTY_CLIENT_ID);
            log.info("Clockwork response for scheduling probable penalty for ID {}, response {}",
                    probableEntity.getProbableId(), response);
            return probableEntity.getProbableId();
        } catch (Exception exception) {
            log.error("Exception in scheduling a job for probable penalty {}", probableEntity, exception);
        }
        return null;
    }

    public String schedulePenaltyEntityAtGrowthTime(final Penalty penaltyInstance,
                                                    Date growthTime) {
        try {
            log.info("Schedule a job for penalty instance at growth time for {}", penaltyInstance);
            SchedulingRequest schedulingRequest = PenaltyConvertorUtils.penaltyPenaltyInstanceToSchedulingRequest(
                    penaltyInstance, growthTime);
            ClockworkResponse<SchedulingResponse> response = clockworkClient.schedule(schedulingRequest,
                    CLOCKWORK_PENALTY_CLIENT_ID);
            log.info("Clockwork response for scheduling  penalty Instance for classId {} penaltyId {}, response {}",
                    penaltyInstance.getPenaltyClassId(), penaltyInstance.getPenaltyId(), response);
            return penaltyInstance.getPenaltyId();
        } catch (Exception exception) {
            log.error("Exception in scheduling a job for  penalty {}", penaltyInstance.getPenaltyId(), exception);
        }
        return null;
    }
}
