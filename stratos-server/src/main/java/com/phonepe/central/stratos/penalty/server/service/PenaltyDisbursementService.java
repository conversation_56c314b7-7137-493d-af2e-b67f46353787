package com.phonepe.central.stratos.penalty.server.service;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.central.stratos.notification.CommunicationRequest;
import com.phonepe.central.stratos.notification.EmailCommunicationRequest;
import com.phonepe.central.stratos.notification.PenaltyDisbursedEmailContext;
import com.phonepe.central.stratos.penalty.Penalty;
import com.phonepe.central.stratos.penalty.disbursement.PenaltyDisbursement;
import com.phonepe.central.stratos.penalty.disbursement.PenaltyDisbursementState;
import com.phonepe.central.stratos.penalty.meta.PenaltyClass;
import com.phonepe.central.stratos.penalty.server.config.disbursement.PenaltyDisbursementConfig;
import com.phonepe.central.stratos.penalty.server.config.disbursement.PenaltyDisbursementConfig.PenaltyDisbursementConfigVisitor;
import com.phonepe.central.stratos.penalty.server.config.disbursement.toa.MerchantPenaltyToADisbursementConfig;
import com.phonepe.central.stratos.penalty.server.config.disbursement.toa.PhonePePenaltyToADisbursementConfig;
import com.phonepe.central.stratos.penalty.server.convertor.PenaltyDisbursementConvertors;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.PenaltyDisbursementEntity;
import com.phonepe.central.stratos.penalty.server.mariadb.repository.PenaltyDisbursementRepository;
import com.phonepe.central.stratos.penalty.server.util.CommonUtils;
import com.phonepe.central.stratos.penalty.server.util.EmailConstants;
import com.phonepe.central.stratos.penalty.server.util.PenaltyUtil;
import com.phonepe.central.stratos.penalty.server.util.CommonUtils;
import com.phonepe.central.stratos.penalty.server.util.EmailConstants;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.clients.PaymentsTxnlClient;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.FoxtrotEventUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.common.util.ToaUtil;
import com.phonepe.models.common.enums.CurrencyCode;
import com.phonepe.models.common.transaction.impl.PhonePeOriginalTransaction;
import com.phonepe.models.payments.pay.PaymentProcessorResult;
import com.phonepe.models.payments.pay.PaymentRequest;
import com.phonepe.models.payments.pay.TransactionDetail;
import com.phonepe.models.payments.pay.context.impl.ToaPaymentContext;
import com.phonepe.models.payments.pay.destination.Destination;
import com.phonepe.models.payments.pay.instrument.PaymentInstrument;
import com.phonepe.models.payments.pay.instrument.PaymentInstrumentType;
import com.phonepe.models.payments.pay.source.impl.MerchantSource;
import com.phonepe.models.response.GenericResponse;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class PenaltyDisbursementService {

    private final PenaltyDisbursementRepository penaltyDisbursementRepository;

    private final PaymentsTxnlClient paymentsTxnlClient;
    private final PenaltyClassService penaltyClassService;
    private final NotificationService notificationService;

    private final PenaltyRecoveryService penaltyRecoveryService;
    private final EventIngester eventIngester;


    public PenaltyDisbursement initiateDisbursement(final Penalty penaltyInstance) {
        try {
            Optional<PenaltyDisbursementEntity> existingDisbursementEntityOptional = penaltyDisbursementRepository.getDisbursementForTransactionId(
                    penaltyInstance.getPenaltyClassId(), penaltyInstance.getTransactionId());

            PenaltyDisbursementEntity penaltyDisbursementEntity;
            if (existingDisbursementEntityOptional.isPresent()) {
                if (isDisbursementAlreadyCompleted(existingDisbursementEntityOptional.get())) {
                    log.error("Disbursement is already completed for penaltyID {}", penaltyInstance.getPenaltyId());
                    return existingDisbursementEntityOptional.map(PenaltyDisbursementConvertors::convert)
                            .get();
                }
                penaltyDisbursementEntity = existingDisbursementEntityOptional.get();
            } else {
                penaltyDisbursementEntity = PenaltyDisbursementConvertors.convert(penaltyInstance);
            }
            PenaltyDisbursementConfig disbursementConfig = penaltyClassService.getClassDisbursementConfig(
                    penaltyInstance.getPenaltyClassId());
            penaltyDisbursementEntity.setDisbursementMode(disbursementConfig.getDisbursementMode());
            Optional<PenaltyDisbursementEntity> storedEntityOptional = penaltyDisbursementRepository.save(
                    penaltyDisbursementEntity);
            boolean isDisbursementSuccessFul = initiateDisbursementViaDisbursementMode(storedEntityOptional, penaltyInstance, disbursementConfig);
            if(isDisbursementSuccessFul){
                storedEntityOptional.get().setStatus(PenaltyDisbursementState.COMPLETED);
            }else{
                storedEntityOptional.get().setStatus(PenaltyDisbursementState.FAILED);
            }
            return storedEntityOptional.map(PenaltyDisbursementConvertors::convert)
                    .orElseThrow(() -> {
                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.PENALTY_DISBURSEMENT_ERROR);
                    });
        } catch (DisputeException exception) {
            log.error("Exception in initiating a disbursement for penaltyInstance for entityID {}",
                    penaltyInstance.getPenaltyId(), exception);
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.PENALTY_DISBURSEMENT_ERROR, Map.of("message",
                    "Exception in initiating a disbursement for penaltyInstance for entityID "
                            + penaltyInstance.getPenaltyId()));
        }
    }

    private boolean initiateDisbursementViaDisbursementMode(Optional<PenaltyDisbursementEntity> storedEntityOptional,
                                                         Penalty penaltyInstance,
                                                         PenaltyDisbursementConfig disbursementConfig) {

        try {
            String disbursementId = disbursementConfig.accept(new PenaltyDisbursementConfigVisitor<>() {
                @Override
                public String visit(MerchantPenaltyToADisbursementConfig config) {
                    final TransactionDetail transactionDetail = paymentsTxnlClient.getTransactionDetails(
                                    penaltyInstance.getTransactionId())
                            .getData();
                    if (transactionDetail == null) {
                        log.error("Transaction detail is null for penaltyID {}", penaltyInstance.getPenaltyId());
                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.PENALTY_DISBURSEMENT_ERROR,
                                Map.of("message",
                                        "Transaction detail is null for penaltyID " + penaltyInstance.getPenaltyId()));
                    }
                    if (penaltyInstance.getFinalPenaltyAmount() <= 0) {
                        log.error("Penalty amount is less than or equal to zero for penaltyID {}",
                                penaltyInstance.getPenaltyId());
                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.PENALTY_DISBURSEMENT_ERROR,
                                "Penalty amount is less than or equal to zero for penaltyID "
                                        + penaltyInstance.getPenaltyId());
                    }

                    PaymentRequest paymentRequest = getToaPaymentRequest(config, penaltyInstance, transactionDetail);
                    final String merchantId = ToaUtil.getMerchantIdFromTransactionDetail(transactionDetail);
                    log.info("Init pay request {}", paymentRequest);
                    GenericResponse<PaymentProcessorResult> response = paymentsTxnlClient.initToaTransaction(
                            paymentRequest, merchantId, merchantId);
                    return response.getData()
                            .getTransactionId();
                }

                @Override
                public String visit(PhonePePenaltyToADisbursementConfig config) {
                    return null;
                }
            });

            storedEntityOptional.ifPresent(entity -> {
                entity.setDisbursementTransactionId(disbursementId);
                entity.setStatus(PenaltyDisbursementState.COMPLETED);
                penaltyDisbursementRepository.save(entity);
                sendNotification(penaltyInstance, createEmailCommunicationRequest(penaltyInstance.getPenaltyClassId()));
                penaltyRecoveryService.initiateRecovery(entity.getDisbursementId(), penaltyInstance,
                        entity.getDisbursementAmount());
            });
            return true;
        } catch (DisputeException disputeException) {
            storedEntityOptional.ifPresent(entity -> {
                entity.setStatus(PenaltyDisbursementState.FAILED);
                penaltyDisbursementRepository.save(entity);
            });
            log.error("Exception in initiating disbursement for penaltyID {}: {}", penaltyInstance.getPenaltyId(),
                    disputeException.getMessage());
        }
        return false;
    }

    private EmailCommunicationRequest createEmailCommunicationRequest(String penaltyClassID){
        PenaltyClass penaltyClass = penaltyClassService.getClassFor(penaltyClassID).orElseThrow(() ->
            DisputeExceptionUtil.error(StratosErrorCodeKey.PENALTY_CLASS_NOT_PRESENT,
            Map.of(Constants.MESSAGE, String.format("penaltyClass not present for penaltyClass Id %s", penaltyClassID))));
        Set<String> emailIds = new HashSet<>();
            penaltyClass.getEscalationMatrix().getLevelConfig().forEach(
            escalationLevelConfig -> emailIds.addAll(escalationLevelConfig.getEmailIds())
        );
            return EmailCommunicationRequest.builder()
                .emailIDs(emailIds)
                .build();
    }

    private boolean isDisbursementAlreadyCompleted(PenaltyDisbursementEntity existingDisbursementEntity) {
        return existingDisbursementEntity != null && existingDisbursementEntity.isDisbursementCompleted();
    }

    private PaymentRequest getToaPaymentRequest(MerchantPenaltyToADisbursementConfig config,
                                                Penalty penaltyEntity,
                                                TransactionDetail transactionDetail) {
        final String merchantId = ToaUtil.getMerchantIdFromTransactionDetail(transactionDetail);
        ToaPaymentContext toaContext = ToaPaymentContext.builder()
                .merchantTransactionId(penaltyEntity.getPenaltyId())
                .originalTransaction(PhonePeOriginalTransaction.builder()
                        .transactionId(penaltyEntity.getTransactionId())
                        .build())
                .message("Penalty with classID " + penaltyEntity.getPenaltyClassId())
                .upiPayProfile(config.getUpiPayProfile())
                .transferUseCase(config.getTransferUseCase())
                .build();

        return PaymentRequest.builder()
                .from(Collections.singletonList(MerchantSource.builder()
                        .amount(penaltyEntity.getFinalPenaltyAmount())
                        .merchantId(merchantId)
                        .build()))
                .to(Collections.singletonList(
                        getDestination(config.getDestination(), penaltyEntity, transactionDetail)))
                .currencyCode(CurrencyCode.INR)
                .context(toaContext)
                .build();
    }

    public PenaltyDisbursement getDisbursement(String penaltyClassId,
                                               String penaltyId) {
        try {

            Optional<PenaltyDisbursementEntity> entityOptional = penaltyDisbursementRepository.getDisbursementForPenaltyId(
                    penaltyClassId, penaltyId);
            return entityOptional.map(PenaltyDisbursementConvertors::convert)
                    .orElse(null);
        } catch (DisputeException exception) {
            log.error("Exception in fetching disbursement for penaltyID TransactionId {}", penaltyId);
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.PENALTY_DISBURSEMENT_ERROR,
                    "Unable to get disbursement for ID" + penaltyId);
        }
    }




    private Destination getDestination(Destination destination,
                                       Penalty penaltyInstance,
                                       TransactionDetail transactionDetail) {
        if (destination == null) {
            PaymentInstrumentType paymentInstrumentType = transactionDetail.getSentPayment()
                    .getPaidFrom()
                    .stream()
                    .map(PaymentInstrument::getType)
                    .findFirst()
                    .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.PENALTY_DISBURSEMENT_ERROR,
                            "No payment instrument found in transaction detail for penaltyID "
                                    + penaltyInstance.getPenaltyId()));

            return PenaltyUtil.getDestination(paymentInstrumentType, penaltyInstance, transactionDetail);
        }
        destination.setAmount(penaltyInstance.getFinalPenaltyAmount());
        return PenaltyUtil.getCustomDestination(destination, transactionDetail);
    }

    public PenaltyDisbursedEmailContext buildPenaltyDisbursedEmailContext(Penalty penalty, PenaltyClass penaltyClass){
        return PenaltyDisbursedEmailContext.builder()
            .penaltyClassName(penaltyClass.getName())
            .penaltyId(penalty.getPenaltyId())
            .build();
    }

    private String createPenaltyDisbursementEmailTemplate(Penalty penalty){
        PenaltyClass penaltyClass = penaltyClassService.getClassFor(penalty.getPenaltyClassId()).orElseThrow(
            () -> DisputeExceptionUtil.error(StratosErrorCodeKey.PENALTY_CLASS_NOT_PRESENT,
                Map.of(Constants.MESSAGE, "penalty class not present")
            ));
        PenaltyDisbursedEmailContext emailContext = buildPenaltyDisbursedEmailContext(penalty, penaltyClass);

        Map<String, String> emailTemplateContextMap = CommonUtils.convertToMap(emailContext);
        return CommonUtils.getEmailTemplate(emailTemplateContextMap, EmailConstants.PENALTY_DISBURSEMENT_EMAIL_TEMPLATE_PATH);
    }

    public void sendNotification(Penalty penalty, CommunicationRequest communicationRequest){
        String emailTemplate = createPenaltyDisbursementEmailTemplate(penalty);
        try {
            notificationService.sendNotification(communicationRequest,
                EmailConstants.PENALTY_DISBURSEMENT_EMAIL_SUBJECT, emailTemplate, List.of());
        }
        catch (DisputeException e) {
            log.info("error while sending notification for penalty disbursement of penaltyClass Id: {} with exception ",
                penalty.getPenaltyId(), e);
            eventIngester.generateEvent(FoxtrotEventUtils.getNotificationErrorEvent(
                e.getErrorCode(), String.format(
                    "error while sending notification for penalty disbursement of penalty Id: %s with exception %s",
                    penalty.getPenaltyId(), e.getMessage())));
        } catch(Exception e) {
            log.info("error while sending notification for penalty disbursement of penaltyClass Id: {} with exception ",
                penalty.getPenaltyId(), e);
            eventIngester.generateEvent(FoxtrotEventUtils.getNotificationErrorEvent(
                StratosErrorCodeKey.NOTIFICATION_SERVICE_ERROR, String.format(
                    "error while sending notification for penalty disbursement of penalty Id: %s with exception %s",
                    penalty.getPenaltyId(), e.getMessage())
            ));
        }
    }
}
