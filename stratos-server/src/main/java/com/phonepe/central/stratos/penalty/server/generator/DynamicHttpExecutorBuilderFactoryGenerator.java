package com.phonepe.central.stratos.penalty.server.generator;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.clients.DocstoreClient;
import com.phonepe.merchant.platform.stratos.server.core.utils.HttpClientUtils;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import com.phonepe.platform.http.v2.executor.factory.HttpExecutorBuilderFactory;
import io.dropwizard.setup.Environment;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class DynamicHttpExecutorBuilderFactoryGenerator {

    private final ServiceEndpointProviderFactory serviceEndpointProviderFactory;
    private final Environment environment;
    private final ObjectMapper mapper;
    private final MetricRegistry metricRegistry;

    public HttpExecutorBuilderFactory generateHttpExecutorBuilderFactory(final HttpConfiguration httpConfiguration) {
        return HttpClientUtils.getHttpExecutorBuilderFactory(DocstoreClient.class, httpConfiguration,
                serviceEndpointProviderFactory, environment, mapper, metricRegistry);
    }

}
