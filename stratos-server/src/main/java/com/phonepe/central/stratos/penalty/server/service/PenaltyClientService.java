package com.phonepe.central.stratos.penalty.server.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.central.stratos.penalty.probables.PenaltyProbable;
import com.phonepe.central.stratos.penalty.response.PenaltyStateCheckResponse;
import com.phonepe.central.stratos.penalty.server.config.client.ClientPenaltyResolutionConfig;
import com.phonepe.central.stratos.penalty.server.config.client.ClientPenaltyResolutionConfig.ClientPenaltyResolutionVisitor;
import com.phonepe.central.stratos.penalty.server.config.client.NoOpsClientPenaltyResolutionConfig;
import com.phonepe.central.stratos.penalty.server.config.client.PassThroughProbableClientPenaltyResolutionConfig;
import com.phonepe.central.stratos.penalty.server.config.client.ServiceClientClientPenaltyResolutionConfig;
import com.phonepe.central.stratos.penalty.server.generator.DynamicHttpExecutorBuilderFactoryGenerator;
import com.phonepe.merchant.platform.stratos.server.core.utils.HttpClientUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.common.hub.RangerHubConfiguration;
import com.phonepe.platform.http.v2.executor.factory.HttpExecutorBuilderFactory;
import io.appform.functionmetrics.MonitoredFunction;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class PenaltyClientService {

    private final PenaltyClassService penaltyClassService;
    private final RangerHubConfiguration rangerHubConfiguration;
    private final DynamicHttpExecutorBuilderFactoryGenerator httpExecutorBuilderFactoryGenerator;
    private final OlympusIMClient olympusIMClient;
    public static final TypeReference<PenaltyStateCheckResponse> PROBABLE_STATUS_CLIENT_CHECK_RESPONSE = new TypeReference<>() {
    };

    public Boolean isPenaltyResolved(final String penaltyClassId,
                                     final String transactionId) {
        try {
            log.info("Checking is penalty is contender for penalty for classId {} and transactionId {}", penaltyClassId,
                    transactionId);
            PenaltyStateCheckResponse clientStatusResponse = getPenaltyResolvedStatusCheck(penaltyClassId,
                    transactionId);
            if (BooleanUtils.isTrue(clientStatusResponse.getIsResolved())) {
                log.info("Penalty  is already resolved so removing it from  life state {} and transaction ID {}",
                        penaltyClassId, transactionId);
                return true;
            }
        } catch (DisputeException exception) {
            throw DisputeExceptionUtil.propagate(exception);

        }
        return false;
    }

    public Boolean isProbableResolved(final PenaltyProbable penaltyProbable) {
        try {
            log.info("Checking is penaltyProbable is resolved {}", penaltyProbable);
            PenaltyStateCheckResponse clientStatusResponse = getProbableResolvedStatusCheck(penaltyProbable);
            if (BooleanUtils.isTrue(clientStatusResponse.getIsResolved())) {
                log.info("Penalty probable is already resolved so removing it from  life state {}", penaltyProbable);
                return true;
            }
        } catch (Exception exception) {
            log.error("Exception in checking isPenaltyProbableResolved for probableId  {}", penaltyProbable, exception);
        }
        return false;
    }

    private PenaltyStateCheckResponse getProbableResolvedStatusCheck(PenaltyProbable penaltyProbable) {
        ClientPenaltyResolutionConfig clientPenaltyResolutionConfig = penaltyClassService.getClientResolutionConfig(
                penaltyProbable.getPenaltyClassId());
        return clientPenaltyResolutionConfig.accept(new ClientPenaltyResolutionVisitor<>() {
            @Override
            public PenaltyStateCheckResponse visit(NoOpsClientPenaltyResolutionConfig config) {
                return getNo_ops(penaltyProbable.getTransactionId());
            }

            @Override
            public PenaltyStateCheckResponse visit(PassThroughProbableClientPenaltyResolutionConfig config) {
                return getPassThrough(penaltyProbable.getTransactionId(), Boolean.FALSE);
            }

            @Override
            public PenaltyStateCheckResponse visit(ServiceClientClientPenaltyResolutionConfig config) {
                return getPenaltyStateCheckServiceResponse(config, penaltyProbable.getTransactionId());
            }
        });

    }


    @MonitoredFunction
    private PenaltyStateCheckResponse getPenaltyResolvedStatusCheck(String penaltyClassId,
                                                                    String transactionId) {

        ClientPenaltyResolutionConfig clientPenaltyResolutionConfig = penaltyClassService.getClientResolutionConfig(
                penaltyClassId);
        return clientPenaltyResolutionConfig.accept(new ClientPenaltyResolutionVisitor<>() {
            @Override
            public PenaltyStateCheckResponse visit(NoOpsClientPenaltyResolutionConfig config) {
                return getNo_ops(transactionId);
            }

            @Override
            public PenaltyStateCheckResponse visit(PassThroughProbableClientPenaltyResolutionConfig config) {
                return getPassThrough(transactionId, Boolean.TRUE);
            }

            @Override
            public PenaltyStateCheckResponse visit(ServiceClientClientPenaltyResolutionConfig config) {
                return getPenaltyStateCheckServiceResponse(config, transactionId);
            }
        });

    }

    private PenaltyStateCheckResponse getPassThrough(String transactionId,
                                                     Boolean isPenaltyResolved) {
        return PenaltyStateCheckResponse.builder()
                .isResolved(isPenaltyResolved)
                .message("Pass through probable")
                .transactionId(transactionId)
                .build();
    }

    @SuppressWarnings("java:S100")
    private PenaltyStateCheckResponse getNo_ops(String transactionId) {
        return PenaltyStateCheckResponse.builder()
                .isResolved(Boolean.TRUE)
                .message("No Ops")
                .transactionId(transactionId)
                .build();
    }

    private PenaltyStateCheckResponse getPenaltyStateCheckServiceResponse(ServiceClientClientPenaltyResolutionConfig config,
                                                                          String transactionId) {
        HttpConfiguration httpConfiguration = HttpClientUtils.getConfig(rangerHubConfiguration,
                config.getRangerHubClientId());
        HttpExecutorBuilderFactory httpExecutorBuilderFactory = httpExecutorBuilderFactoryGenerator.generateHttpExecutorBuilderFactory(
                httpConfiguration);
        final var url = String.format(config.getApiPath() + "/%s", transactionId);
        return HttpClientUtils.executeGet(httpExecutorBuilderFactory, "getProbableStatus", url,
                PROBABLE_STATUS_CLIENT_CHECK_RESPONSE, olympusIMClient);
    }
}

