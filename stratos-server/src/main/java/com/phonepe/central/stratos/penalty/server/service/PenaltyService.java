package com.phonepe.central.stratos.penalty.server.service;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.central.stratos.notification.AttachmentData;
import com.phonepe.central.stratos.notification.CommunicationRequest;
import com.phonepe.central.stratos.notification.PenaltyCreatedEmailContext;
import com.phonepe.central.stratos.penalty.Penalty;
import com.phonepe.central.stratos.penalty.PenaltyStatus;
import com.phonepe.central.stratos.penalty.disbursement.PenaltyDisbursement;
import com.phonepe.central.stratos.penalty.meta.PenaltyClass;
import com.phonepe.central.stratos.penalty.probables.PenaltyProbable;
import com.phonepe.central.stratos.penalty.request.DateRangeRequest;
import com.phonepe.central.stratos.penalty.request.penalty.PenaltyBasicSearchRequest;
import com.phonepe.central.stratos.penalty.request.penalty.PenaltyDateRangeSearchRequest;
import com.phonepe.central.stratos.penalty.request.penalty.PenaltySearchRequest;
import com.phonepe.central.stratos.penalty.request.penalty.PenaltySearchRequest.PenaltySearchRequestVisitor;
import com.phonepe.central.stratos.penalty.response.PenaltyResponse;
import com.phonepe.central.stratos.penalty.server.convertor.PenaltyConvertors;
import com.phonepe.central.stratos.penalty.server.generator.PenaltyGrowthGenerator;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.PenaltyEntity;
import com.phonepe.central.stratos.penalty.server.mariadb.repository.PenaltyRepository;
import com.phonepe.central.stratos.penalty.server.util.CommonUtils;
import com.phonepe.central.stratos.penalty.server.util.EmailConstants;
import com.phonepe.central.stratos.penalty.server.util.PenaltyConvertorUtils;
import com.phonepe.central.stratos.penalty.server.util.PenaltyUtil;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.FoxtrotEventUtils;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class PenaltyService {

    private final PenaltyRepository penaltyRepository;
    private final PenaltyGrowthGenerator penaltyGrowthGenerator;
    private final PenaltyClientService penaltyClientService;
    private final PenaltyDisbursementService penaltyDisbursementService;
    private final ClockworkService clockworkService;
    private final PenaltyEvaluationService penaltyEvaluationService;
    private final PenaltyClassService penaltyClassService;
    private final NotificationService notificationService;
    private static final String PENALTIES_FILE_NAME = "penalty.csv";
    private final EventIngester eventIngester;

    public String createNewPenalty(final PenaltyProbable penaltyProbable) {
        log.info("Creating new penalty for probable {}", penaltyProbable);
        Penalty penaltyInstance = this.getPenaltyForProbableId(penaltyProbable.getPenaltyClassId(),
                penaltyProbable.getProbableId());
        if (penaltyInstance != null) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.PENALTY_ALREADY_PRESENT);
        }
        PenaltyEntity entityToStore = PenaltyConvertorUtils.penaltyProbableToPenaltyEntity(penaltyProbable);
        Long finalAmount = penaltyGrowthGenerator.generateGrowthAmount(entityToStore);
        entityToStore.setFinalPenaltyAmount(finalAmount);
        Optional<PenaltyEntity> entityResponse = penaltyRepository.save(entityToStore);
        schedulePenaltyGrowthCallback(entityResponse);
        return entityResponse.map(PenaltyEntity::getPenaltyId)
                .orElse(null);
    }

    public void reconcile(final PenaltyDateRangeSearchRequest dateRangeRequest) {
        log.info("Reconcile penalties entries with due date falling under dateRangeRequest {}", dateRangeRequest);
        List<PenaltyEntity> entities = penaltyRepository.findReconEligiblePenalties(dateRangeRequest);
        entities.forEach(entity -> reconcile(entity.getPenaltyClassId(), entity.getPenaltyId()));
    }

    public void reconcile(final String penaltyClassId,
                          final String penaltyId) {
        log.info("Reconcile penalty entry with penalty ID {}", penaltyId);
        List<Penalty> entities = this.fetchPenalties(PenaltyBasicSearchRequest.builder()
                .penaltyClassId(penaltyClassId)
                .penaltyId(penaltyId)
                .build());
        log.info("Reconciling penalty entry with penalty ID {} with size {}", penaltyId, entities.size());
        entities.stream()
                .filter(penalty -> penalty.getStatus() != PenaltyStatus.DISBURSED
                        && penalty.getStatus() != PenaltyStatus.REJECTED)
                .filter(entity -> !checkAndMarkIfPenaltyDisqualified(entity))
                .filter(entity -> !disburseIfPenaltyResolved(entity))
                .forEach(penaltyInstance -> {
                    this.schedulePenaltyGrowthCallback(Optional.of(PenaltyConvertors.convert(penaltyInstance)));
                    Long finalPenaltyAmount = penaltyGrowthGenerator.generateGrowthAmount(
                            PenaltyConvertors.convert(penaltyInstance));
                    this.updatePenaltyFinalAmount(penaltyInstance.getPenaltyClassId(), penaltyInstance.getPenaltyId(),
                            finalPenaltyAmount);
                });

    }

    private void updatePenaltyFinalAmount(final String penaltyClassId,
                                          final String penaltyId,
                                          Long finalPenaltyAmount) {
        this.penaltyRepository.updatePenaltyFinalAmount(penaltyClassId, penaltyId, finalPenaltyAmount);
    }

    public Penalty getPenaltyForProbableId(String penaltyClassId,
                                           String probableId) {
        return penaltyRepository.getPenaltyForProbable(penaltyClassId, probableId)
                .map(PenaltyConvertors::convert)
                .orElse(null);
    }

    public List<PenaltyResponse> fetchTenantPenalties(PenaltySearchRequest penaltySearchRequest) {
        List<PenaltyResponse> responseList = new ArrayList<>();
        List<Penalty> listOfPenalties = this.fetchPenalties(penaltySearchRequest);
        listOfPenalties.forEach(penalty -> {
            Optional<PenaltyClass> classOptional = penaltyClassService.getClassFor(penalty.getPenaltyClassId());
            classOptional.ifPresent(penaltyClass -> responseList.add(PenaltyResponse.builder()
                    .penalty(penalty)
                    .tenantInfo(penaltyClass.getTenant())
                    .build()));
        });
        return responseList;
    }

    public List<Penalty> fetchPenalties(PenaltySearchRequest penaltySearchRequest) {
        List<PenaltyEntity> entityList = penaltySearchRequest.accept(new PenaltySearchRequestVisitor<>() {
            @Override
            public List<PenaltyEntity> visit(PenaltyBasicSearchRequest searchRequest) {
                return penaltyRepository.findPenalties(searchRequest);
            }

            @Override
            public List<PenaltyEntity> visit(PenaltyDateRangeSearchRequest searchRequest) {
                return penaltyRepository.findPenalties(searchRequest);
            }
        });
        return entityList.stream()
                .map(PenaltyConvertors::convert)
                .collect(Collectors.toList());
    }

    public List<Penalty> fetchCreatedPenalties(PenaltyDateRangeSearchRequest searchRequest) {
        List<PenaltyEntity> entityList = penaltyRepository.findActivePenalties(searchRequest);
        return entityList.stream()
                .map(PenaltyConvertors::convert)
                .collect(Collectors.toList());
    }

    public List<Penalty> fetchCreatedPenaltiesBeforeDate(String penaltyClassId, Date date) {
        List<PenaltyEntity> entityList = penaltyRepository.findActivePenaltiesBeforeDate(penaltyClassId, date);
        return entityList.stream()
                .map(PenaltyConvertors::convert)
                .collect(Collectors.toList());
    }

    private boolean checkAndMarkIfPenaltyDisqualified(Penalty penaltyInstance) {
        if (penaltyEvaluationService.isQualifiedForDisQualifiedCriteria(penaltyInstance)) {
            log.info("Penalty instance is disqualified for penaltyId {}", penaltyInstance.getPenaltyId());
            this.markPenaltyAsDisqualified(penaltyInstance);
            return true;
        }
        return false;
    }

    private boolean disburseIfPenaltyResolved(Penalty penaltyInstance) {
        Boolean isPenaltyResolved = penaltyClientService.isPenaltyResolved(penaltyInstance.getPenaltyClassId(),
                penaltyInstance.getTransactionId());
        if (BooleanUtils.isTrue(isPenaltyResolved)) {
            PenaltyDisbursement penaltyDisbursement = penaltyDisbursementService.initiateDisbursement(penaltyInstance);
            log.info("Penalty disbursement is initiated for penaltyId {} with disbursementId {}",
                    penaltyInstance.getPenaltyId(), penaltyDisbursement.getDisbursementId());
            if (BooleanUtils.isTrue(penaltyDisbursement.isDisbursementSuccessful())) {
                penaltyRepository.updatePenaltyDisbursementStatus(penaltyInstance.getPenaltyClassId(),
                        penaltyInstance.getPenaltyId(),PenaltyStatus.DISBURSED);
            }else{
                penaltyRepository.updatePenaltyDisbursementStatus(penaltyInstance.getPenaltyClassId(),
                        penaltyInstance.getPenaltyId(),PenaltyStatus.FAILED);
            }
            return true;
        }
        return false;
    }

    private void markPenaltyAsDisqualified(Penalty penaltyInstance) {
        log.info("Marking penalty as disqualified for penaltyId {}", penaltyInstance.getPenaltyId());
        List<PenaltyEntity> entities = this.penaltyRepository.findPenalties(PenaltyBasicSearchRequest.builder()
                .penaltyClassId(penaltyInstance.getPenaltyClassId())
                .penaltyId(penaltyInstance.getPenaltyId())
                .build());
        entities.stream()
                .map(penaltyEntity -> {
                    penaltyEntity.setStatus(PenaltyStatus.REJECTED);
                    return penaltyEntity;
                })
                .forEach(penaltyRepository::save);
    }

    private void schedulePenaltyGrowthCallback(Optional<PenaltyEntity> entityResponse) {
        entityResponse.ifPresentOrElse(entity -> {
            if (penaltyEvaluationService.isQualifiedForCriteria(PenaltyConvertors.convert(entity))) {
                Date growthTime = penaltyGrowthGenerator.generatePenaltyScheduleTime(entity);
                clockworkService.schedulePenaltyEntityAtGrowthTime(PenaltyConvertors.convert(entity), growthTime);
            } else {
                log.error("Penalty entity is not eligible for growth callback {}", entity.getPenaltyId());
            }

        }, () -> log.error("Penalty entity is not present to schedule"));
    }

    public PenaltyDisbursement getDisbursement(final String penaltyClassId,
                                               final String penaltyId) {
        return penaltyDisbursementService.getDisbursement(penaltyClassId, penaltyId);
    }

    public boolean isActivePenaltyExistAgainstClass(String penaltyClassId) {
        List<PenaltyEntity> listOfPenalties = penaltyRepository.findPenalties(PenaltyBasicSearchRequest.builder()
                .penaltyId(penaltyClassId)
                .build());
        return !listOfPenalties.isEmpty();
    }

    private PenaltyCreatedEmailContext buildPenaltyCreationEmailContextFor(PenaltyClass penaltyClass,
        List<Penalty>penalties, DateRangeRequest request){

        return PenaltyCreatedEmailContext.builder()
            .tenantName(penaltyClass.getTenant().getName())
            .penaltyClassName(penaltyClass.getName())
            .numberOfPenalties(String.valueOf(penalties.size()))
            .startDate(String.valueOf(request.getFromDate()))
            .endDate(String.valueOf(request.getToDate()))
            .build();
    }

    public String buildPenaltyCreationEmailTemplate(String penaltyClassId, List<Penalty> probables,
        DateRangeRequest dateRangeRequest, String templatePath) {
        PenaltyClass penaltyClass = penaltyClassService.getClassFor(penaltyClassId).orElseThrow(
            () -> DisputeExceptionUtil.error(StratosErrorCodeKey.PENALTY_CLASS_NOT_PRESENT,
                Map.of(Constants.MESSAGE, "penalty class not present")
            ));
        PenaltyCreatedEmailContext emailTemplateContext = buildPenaltyCreationEmailContextFor(
            penaltyClass, probables, dateRangeRequest);
        log.info("Sending penalty notification with email context : {}", emailTemplateContext);
        Map<String, String> emailTemplateContextMap = CommonUtils.convertToMap(emailTemplateContext);
        return CommonUtils.getEmailTemplate(emailTemplateContextMap, templatePath);
    }


    public void sendNotification(List<Penalty> penalties, CommunicationRequest contactRequest
        , String templatePath, String emailSubject, DateRangeRequest conversionDateRange, String fileName) {
        Map<String, List<Penalty>> penaltiesMap = penalties.stream()
            .collect(Collectors.groupingBy(Penalty::getPenaltyClassId));

        penaltiesMap.forEach((penaltyClassId, penaltyList) -> {
            String emailAttachment = PenaltyUtil.convertListOfPenaltiesToString(penaltyList);
            String emailTemplate = buildPenaltyCreationEmailTemplate(penaltyClassId, penaltyList,
                conversionDateRange, templatePath);
            try {
                notificationService.sendNotification(contactRequest,
                    emailSubject, emailTemplate, List.of(
                        AttachmentData.builder()
                            .attachmentFileName(fileName)
                            .attachmentFileType(Constants.CSV_FILE_TYPE)
                            .attachment(emailAttachment)
                            .build()
                    ));
            }catch (DisputeException e)
            {
                log.info("error while sending notification for penaltyClass Id: {} with exception ",
                    penaltyClassId, e);
                eventIngester.generateEvent(FoxtrotEventUtils.getNotificationErrorEvent(
                    e.getErrorCode(),String.format(
                        "error while sending notification for penaltyClass Id: %s with exception %s",
                        penaltyClassId, e.getMessage()
                    )));
            } catch(Exception e) {
                log.info("error while sending notification for penaltyClass Id: {} with exception ",
                    penaltyClassId, e);
                eventIngester.generateEvent(FoxtrotEventUtils.getNotificationErrorEvent(
                    StratosErrorCodeKey.NOTIFICATION_SERVICE_ERROR,String.format(
                        "error while sending notification for penaltyClass Id: %s with exception %s",
                        penaltyClassId, e.getMessage()
                    )
                ));
            }
        });
    }

    public void sendNotificationForPenaltiesCreation(List<Penalty> penalties,
        CommunicationRequest contactRequest, DateRangeRequest conversionDateRange) {
        sendNotification(penalties, contactRequest,
            EmailConstants.PENALTY_CREATED_TEMPLATE_PATH, EmailConstants.PENALTY_CREATED_EMAIL_SUBJECT,
            conversionDateRange, PENALTIES_FILE_NAME);
    }

    public void sendNotificationForPenaltiesGrowing(List<Penalty> penalties,
        CommunicationRequest contactRequest, DateRangeRequest conversionDateRange) {
        sendNotification(penalties, contactRequest,
            EmailConstants.PENALTY_GROWING_EMAIL_TEMPLATE_PATH, EmailConstants.PENALTY_GROWING_EMAIL_SUBJECT,
            conversionDateRange, PENALTIES_FILE_NAME);
    }
}

