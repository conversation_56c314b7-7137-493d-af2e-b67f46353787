package com.phonepe.central.stratos.penalty.server.config.client;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@EqualsAndHashCode
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type")
@JsonSubTypes({
        @JsonSubTypes.Type(name = ClientPenaltyResolutionType.PASS_THROUGH_PROBABLE_TEXT, value = PassThroughProbableClientPenaltyResolutionConfig.class),
        @JsonSubTypes.Type(name = ClientPenaltyResolutionType.NO_OPS_TEXT, value = NoOpsClientPenaltyResolutionConfig.class),
        @JsonSubTypes.Type(name = ClientPenaltyResolutionType.SERVICE_TEXT, value = ServiceClientPenaltyResolutionConfig.class),
        @JsonSubTypes.Type(name = ClientPenaltyResolutionType.SERVICE_CLIENT_TEXT, value = ServiceClientImplClientPenaltyResolutionConfig.class)
})
public abstract class ClientPenaltyResolutionConfig {

    private ClientPenaltyResolutionType type;

    public abstract <T> T accept(ClientPenaltyResolutionVisitor<T> visitor);

    public interface ClientPenaltyResolutionVisitor<T> {

        T visit(final NoOpsClientPenaltyResolutionConfig config);

        T visit(final PassThroughProbableClientPenaltyResolutionConfig config);

        T visit(final ServiceClientPenaltyResolutionConfig config);

        T visit(final ServiceClientImplClientPenaltyResolutionConfig config);

    }

}
