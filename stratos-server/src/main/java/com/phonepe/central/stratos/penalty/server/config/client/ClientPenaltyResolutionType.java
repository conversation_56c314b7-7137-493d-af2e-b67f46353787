package com.phonepe.central.stratos.penalty.server.config.client;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum ClientPenaltyResolutionType {
    NO_OPS(ClientPenaltyResolutionType.NO_OPS_TEXT),
    PASS_THROUGH_PROBABLE(ClientPenaltyResolutionType.PASS_THROUGH_PROBABLE_TEXT),
    SERVICE(ClientPenaltyResolutionType.SERVICE_TEXT),
    SERVICE_CLIENT(ClientPenaltyResolutionType.SERVICE_CLIENT_TEXT);

    public static final String NO_OPS_TEXT = "NO_OPS";
    public static final String PASS_THROUGH_PROBABLE_TEXT = "PASS_THROUGH_PROBABLE";
    public static final String SERVICE_TEXT = "SERVICE";
    public static final String SERVICE_CLIENT_TEXT = "SERVICE_CLIENT";


    @Getter
    private final String value;
}
