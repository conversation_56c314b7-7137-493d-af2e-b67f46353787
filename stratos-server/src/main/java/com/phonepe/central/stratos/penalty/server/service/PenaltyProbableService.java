package com.phonepe.central.stratos.penalty.server.service;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.central.stratos.notification.AttachmentData;
import com.phonepe.central.stratos.notification.CommunicationRequest;
import com.phonepe.central.stratos.notification.PenaltyDueShortlyEmailContext;
import com.phonepe.central.stratos.penalty.Penalty;
import com.phonepe.central.stratos.penalty.meta.PenaltyClass;
import com.phonepe.central.stratos.penalty.probables.PenaltyProbable;
import com.phonepe.central.stratos.penalty.request.DateRangeRequest;
import com.phonepe.central.stratos.penalty.request.PenaltyProbableRequest;
import com.phonepe.central.stratos.penalty.response.PenaltyProbableResponse;
import com.phonepe.central.stratos.penalty.server.aerospike.AeroSpikeRepository;
import com.phonepe.central.stratos.penalty.server.aerospike.PenaltyAerospikeSet;
import com.phonepe.central.stratos.penalty.server.util.CommonUtils;
import com.phonepe.central.stratos.penalty.server.util.EmailConstants;
import com.phonepe.central.stratos.penalty.server.util.PenaltyConvertorUtils;
import com.phonepe.central.stratos.penalty.server.util.PenaltyDBUtils;
import com.phonepe.central.stratos.penalty.server.util.PenaltyUtil;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.FoxtrotEventUtils;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class PenaltyProbableService {

    private final AeroSpikeRepository aeroSpikeRepository;
    private final PenaltyService penaltyService;
    private final ClockworkService clockworkService;
    private final PenaltyClientService penaltyClientService;
    private final PenaltyEvaluationService penaltyEvaluationService;
    private final PenaltyClassService penaltyClassService;
    private final NotificationService notificationService;
    private static final String PENALTY_DUE_SHORTLY_FILE_NAME = "penaltyDueShortly.csv";
    private final EventIngester eventIngester;


    public String registerProbable(final PenaltyProbableRequest penaltyProbableRequest) {
        PenaltyProbable probableEntity = getPenaltyProbable(penaltyProbableRequest);
        if (probableEntity != null) {
            log.info("Probable entry already exists with ID {}", probableEntity.getProbableId());
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.PROBABLE_ALREADY_PRESENT);
        } else {
            probableEntity = PenaltyConvertorUtils.penaltyProbableRequestToPenaltyProbable(penaltyProbableRequest);
            validateIfActivePenaltyClassExist(probableEntity);
            validateIfPenaltyExist(probableEntity);
            if (penaltyEvaluationService.isQualifiedForCriteria(probableEntity)) {
                aeroSpikeRepository.save(PenaltyAerospikeSet.PENALTY_PROBABLE, probableEntity.getProbableId(),
                        probableEntity);
                if(PenaltyUtil.isDueDatePass(probableEntity.getDueDate())){
                    this.reconcile(probableEntity.getProbableId());
                }else{
                    clockworkService.scheduleProbableAtDueTime(probableEntity);
                }
                return probableEntity.getProbableId();
            } else {
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.PENALTY_CLASS_CRITERIA_NOT_MET);
            }
        }
    }

    public PenaltyProbable getPenaltyProbable(final PenaltyProbableRequest penaltyProbableRequest) {
        return aeroSpikeRepository.get(PenaltyDBUtils.generateProbableId(penaltyProbableRequest.getPenaltyClassId(),
                penaltyProbableRequest.getTransactionContext()
                        .getTransactionId()));
    }

    public void reconcile(final DateRangeRequest dateRangeRequest) {
        List<PenaltyProbable> probables = getDuePenaltyProbablesFromRange(dateRangeRequest);
        probables.stream()
                .map(PenaltyProbable::getProbableId)
                .forEach(this::reconcile);
    }

    public List<PenaltyProbable> getDuePenaltyProbablesFromRange(final DateRangeRequest dateRangeRequest) {
        return this.aeroSpikeRepository.getDueProbableEntriesBetween(dateRangeRequest.getFromDate(),
                dateRangeRequest.getToDate());
    }

    public String reconcile(final String probableId) {
        log.info("Reconcile probable entry with due date ID {}", probableId);
        PenaltyProbable probableEntity = aeroSpikeRepository.get(probableId);
        if (probableEntity != null && isProbableEligibleForEntity(probableEntity)) {
            String penaltyId = penaltyService.createNewPenalty(probableEntity);
            log.info("Converting probable with ID {} into Penalty ID {} ", probableId, penaltyId);
            removeProbableFromStorage(probableId);
            return penaltyId;
        } else {
            log.error("Probable entry with ID {} not found or not eligible for conversion", probableId);
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.PROBABLE_RECONCILE_ERROR, Map.of("message",
                    "Probable entity is not eligible for conversion or probable entity not found" + probableId));
        }
    }


    private boolean isProbableEligibleForEntity(PenaltyProbable probableEntity) {
        if (BooleanUtils.isTrue(penaltyClientService.isProbableResolved(probableEntity))) {
            this.removeProbableFromStorage(probableEntity.getProbableId());
            return false;
        } else {
            return PenaltyUtil.isDueDatePass(probableEntity.getDueDate());
        }
    }

    public void removeProbableFromStorage(String penaltyProbableId) {
        log.info("Removing penalty probable since its not eligible or converted into for penalty {}",
                penaltyProbableId);
        aeroSpikeRepository.delete(penaltyProbableId);
    }

    public List<PenaltyProbableResponse> getPaginatedPenaltyProbables(String penaltyClassId,
                                                                      int limit) {
        List<PenaltyProbableResponse> result = new ArrayList<>();
        List<PenaltyProbable> listOfProbables = aeroSpikeRepository.getPaginatedProbableEntries(penaltyClassId, limit);
        listOfProbables.forEach(probable -> {
            Optional<PenaltyClass> classOptional = penaltyClassService.getClassFor(probable.getPenaltyClassId());
            if (classOptional.isPresent()) {
                result.add(PenaltyProbableResponse.builder()
                        .penaltyProbable(probable)
                        .tenantInfo(classOptional.get()
                                .getTenant())
                        .build());
            } else {
                log.error("Penalty class not found for ID {}", probable.getPenaltyClassId());
            }
        });
        return result;
    }

    private void validateIfPenaltyExist(PenaltyProbable probableEntity) {
        Penalty penaltyInstance = penaltyService.getPenaltyForProbableId(probableEntity.getPenaltyClassId(),
                probableEntity.getProbableId());
        if (penaltyInstance != null) {
            log.info("Penalty entry already exists with ID {}", penaltyInstance.getPenaltyId());
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.PENALTY_ALREADY_PRESENT);
        }
    }

    private void validateIfActivePenaltyClassExist(PenaltyProbable probableEntity) {
        Optional<PenaltyClass> penaltyClass = penaltyClassService.getClassFor(probableEntity.getPenaltyClassId());
        if (penaltyClass.isPresent() && penaltyClass.get()
                .isActive()) {
            return;
        }
        log.info("Penalty class not found or active for ID {}", probableEntity.getPenaltyClassId());
        throw DisputeExceptionUtil.error(StratosErrorCodeKey.PENALTY_CLASS_NOT_FOUND,
                Map.of("message", "Penalty class not found or active for ID " + probableEntity.getPenaltyClassId()));
    }

    public boolean isActiveProbableExistAgainstClass(String penaltyClassId) {
        List<PenaltyProbable> probables = aeroSpikeRepository.getPaginatedProbableEntries(penaltyClassId, 1);
        return !probables.isEmpty();
    }
    private PenaltyDueShortlyEmailContext buildPenaltyDueShortlyEmailContext(
        PenaltyClass penaltyClass, List<PenaltyProbable>probables, DateRangeRequest dateRangeRequest){
        return PenaltyDueShortlyEmailContext.builder()
            .tenantName(penaltyClass.getTenant().getName())
            .penaltyClassName(penaltyClass.getName())
            .probableCount(String.valueOf(probables.size()))
            .penaltyClassId(penaltyClass.getId())
            .fromDate(String.valueOf(dateRangeRequest.getFromDate()))
            .toDate(String.valueOf(dateRangeRequest.getToDate()))
            .build();
    }

    public String createDueShortlyEmailTemplate(String penaltyClassId,
        List<PenaltyProbable> probables, DateRangeRequest dateRangeRequest) {

        PenaltyClass penaltyClass = penaltyClassService.getClassFor(penaltyClassId).orElseThrow(
            () -> DisputeExceptionUtil.error(StratosErrorCodeKey.PENALTY_CLASS_NOT_PRESENT,
                Map.of(Constants.MESSAGE, "penalty class not present")
            ));
        PenaltyDueShortlyEmailContext emailTemplateContext = buildPenaltyDueShortlyEmailContext(
            penaltyClass, probables, dateRangeRequest);
        log.info("Sending penalty notification with email context : {}", emailTemplateContext);
        Map<String, String> emailTemplateContextMap = CommonUtils.convertToMap(emailTemplateContext);
        return CommonUtils.getEmailTemplate(emailTemplateContextMap,
            EmailConstants.PENALTY_DUE_SHORTLY_EMAIL_TEMPLATE_PATH);
    }

    public void sendNotificationForPenaltyDueShortly(List<PenaltyProbable> probables,
        CommunicationRequest contactRequest, DateRangeRequest conversionDateRange) {
        Map<String, List<PenaltyProbable>> probablesMap = probables.stream()
            .collect(Collectors.groupingBy(PenaltyProbable::getPenaltyClassId));

        probablesMap.forEach((penaltyClassId, probablesList) -> {
            String emailAttachment = PenaltyUtil.convertListOfProbablesToString(probablesList);
            String emailTemplate = createDueShortlyEmailTemplate(penaltyClassId, probablesList, conversionDateRange);
            try {
                notificationService.sendNotification(contactRequest,
                    EmailConstants.PENALTY_DUE_SHORTLY_EMAIL_SUBJECT, emailTemplate,
                    List.of(
                        AttachmentData.builder()
                            .attachmentFileName(PENALTY_DUE_SHORTLY_FILE_NAME)
                            .attachmentFileType(Constants.CSV_FILE_TYPE)
                            .attachment(emailAttachment)
                            .build()
                    ));
            }catch (DisputeException e)
            {
                log.info("error while sending notification for penaltyClass Id: {} with exception ",
                    penaltyClassId, e);
                eventIngester.generateEvent(FoxtrotEventUtils.getNotificationErrorEvent(
                    e.getErrorCode(),String.format(
                        "error while sending notification for penaltyClass Id: %s with exception %s",
                        penaltyClassId, e.getMessage()
                    )));
            } catch(Exception e) {
                log.info("error while sending notification for penaltyClass Id: {} with exception ",
                    penaltyClassId, e);
                eventIngester.generateEvent(FoxtrotEventUtils.getNotificationErrorEvent(
                    StratosErrorCodeKey.NOTIFICATION_SERVICE_ERROR, String.format(
                        "error while sending notification for penaltyClass Id: %s with exception %s",
                        penaltyClassId, e.getMessage()
                    )));
            }
        });
    }
}
