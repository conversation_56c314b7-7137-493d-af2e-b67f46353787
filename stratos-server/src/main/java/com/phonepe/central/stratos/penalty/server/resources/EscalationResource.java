package com.phonepe.central.stratos.penalty.server.resources;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.google.inject.Inject;
import com.phonepe.central.stratos.escalation.request.EscalateEntityRequest;
import com.phonepe.central.stratos.penalty.server.service.EscalationService;
import com.phonepe.merchant.platform.stratos.server.core.models.ApiState;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.olympus.im.client.annotations.AccessAllowed;
import com.phonepe.olympus.im.models.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import io.dropwizard.auth.Auth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import static com.phonepe.olympus.im.models.core.CoreConstants.BEARER;

@Slf4j
@Path("/v1/escalation")
@Tag(name = "Escalation Related APIs")
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
@SecurityScheme(name = BEARER, type = SecuritySchemeType.APIKEY, in = SecuritySchemeIn.HEADER, paramName = "Authorization")
@SecurityRequirement(name = BEARER)
public class EscalationResource {

    private final EscalationService escalationService;

    @POST
    @ExceptionMetered
    @AccessAllowed
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/escalateEntities")
    @Operation(summary = "Escalate and send notifications for eligible entities")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response escalate(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                             @Valid final EscalateEntityRequest escalateEntityRequest) {
        try {
            log.info("Received request: {}", escalateEntityRequest);
            escalationService.escalateEntities(escalateEntityRequest);
            return Response.status(Response.Status.NO_CONTENT)
                    .build();
        }
        catch (DisputeException exception){
            throw DisputeExceptionUtil.propagate(exception);
        }
    }
}