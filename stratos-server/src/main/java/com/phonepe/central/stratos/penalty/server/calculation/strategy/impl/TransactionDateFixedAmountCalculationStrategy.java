package com.phonepe.central.stratos.penalty.server.calculation.strategy.impl;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.central.stratos.penalty.growth.calculation.impl.TransactionDateFixedAmountCalculation;
import com.phonepe.central.stratos.penalty.meta.PenaltyClassDetail;
import com.phonepe.central.stratos.penalty.server.calculation.strategy.PenaltyCalculationStrategy;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.PenaltyEntity;
import com.phonepe.central.stratos.penalty.server.util.PenaltyEvaluatorUtil;
import com.phonepe.central.stratos.penalty.server.util.PenaltyUtil;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.clients.PaymentsTxnlClient;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.models.payments.pay.TransactionDetail;
import com.phonepe.models.response.GenericResponse;
import java.time.LocalDateTime;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class TransactionDateFixedAmountCalculationStrategy implements
        PenaltyCalculationStrategy<TransactionDateFixedAmountCalculation> {

    private final PaymentsTxnlClient paymentsTxnlClient;

    @Override
    public Long calculate(PenaltyClassDetail penaltyClassDetail,
                          PenaltyEntity penaltyEntity,
                          TransactionDateFixedAmountCalculation calculation) {
        GenericResponse<TransactionDetail> transactionDetailsResponse = paymentsTxnlClient.getTransactionDetails(
                penaltyEntity.getTransactionId());
        if (transactionDetailsResponse.isSuccess()) {
            TransactionDetail transactionDetails = transactionDetailsResponse.getData();
            var transactionDate = PenaltyUtil.getLocalDateTime(transactionDetails.getSentPayment()
                    .getSentAt());
            LocalDateTime penaltyAppliedTime = transactionDate.plus(penaltyClassDetail.getCriteria()
                    .getLeeway());
            Long noOfUnits = PenaltyEvaluatorUtil.getNumberOfUnits(penaltyClassDetail.getGrowthRate()
                    .getUnit(), penaltyAppliedTime, penaltyEntity.getPenaltyId());
            return penaltyEntity.getInitialPenaltyAmount() + (noOfUnits * calculation.getAmount()
                    .longValue());
        }
        log.error("NonSuccess response : Failed to fetch transaction details for penalty calculation. Transaction ID: "
                + penaltyEntity.getTransactionId());
        throw DisputeExceptionUtil.error(StratosErrorCodeKey.PENALTY_RECONCILE_ERROR,
                "NonSuccess response : Failed to fetch transaction details for penalty calculation. Transaction ID: "
                        + penaltyEntity.getTransactionId());

    }
}
