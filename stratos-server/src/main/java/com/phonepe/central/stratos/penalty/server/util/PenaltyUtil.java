package com.phonepe.central.stratos.penalty.server.util;

import com.phonepe.central.stratos.penalty.Penalty;
import com.phonepe.central.stratos.penalty.growth.GrowthUnit;
import com.phonepe.central.stratos.penalty.probables.PenaltyProbable;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.models.payments.pay.TransactionDetail;
import com.phonepe.models.payments.pay.destination.Destination;
import com.phonepe.models.payments.pay.destination.DestinationVisitor;
import com.phonepe.models.payments.pay.destination.GiftCardDestinationType;
import com.phonepe.models.payments.pay.destination.impl.AccountDestination;
import com.phonepe.models.payments.pay.destination.impl.GiftCardDestination;
import com.phonepe.models.payments.pay.destination.impl.IntentVpaDestination;
import com.phonepe.models.payments.pay.destination.impl.MerchantDestination;
import com.phonepe.models.payments.pay.destination.impl.MerchantUserDestination;
import com.phonepe.models.payments.pay.destination.impl.PhoneDestination;
import com.phonepe.models.payments.pay.destination.impl.UserDestination;
import com.phonepe.models.payments.pay.destination.impl.VpaDestination;
import com.phonepe.models.payments.pay.instrument.AccountPaymentInstrument;
import com.phonepe.models.payments.pay.instrument.PaymentInstrumentType;
import com.phonepe.models.payments.pay.instrument.PaymentInstrumentType.PaymentInstrumentTypeVisitor;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@UtilityClass
public class PenaltyUtil {

    public Date getEndDate(final GrowthUnit growthUnit) {
        LocalDate localDate = growthUnit.accept(new GrowthUnit.Visitor<>() {
            @Override
            public LocalDate visitDay() {
                return LocalDate.now()
                        .plus(1, ChronoUnit.DAYS);
            }

            @Override
            public LocalDate visitWeek() {
                return LocalDate.now()
                        .plus(1, ChronoUnit.WEEKS);
            }

            @Override
            public LocalDate visitMonth() {
                return LocalDate.now()
                        .plus(1, ChronoUnit.MONTHS);
            }

            @Override
            public LocalDate visitYear() {
                return LocalDate.now()
                        .plus(1, ChronoUnit.YEARS);
            }
        });
        return Date.from(localDate.atStartOfDay(ZoneId.systemDefault())
                .toInstant());
    }

    public Boolean isGreaterThanOrEqual(final LocalDateTime startDate,
                                        final LocalDateTime endDate) {
        return startDate.isAfter(endDate) || startDate.isEqual(endDate);
    }

    public LocalDateTime getLocalDateTime(Date date) {
        return date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
    }

    public boolean isDueDatePass(Date dueDate){
        return dueDate != null && dueDate.before(new Date());
    }

    public Destination getCustomDestination(Destination destination,TransactionDetail transactionDetail)  {

        try{
            return destination.accept(new DestinationVisitor<>() {
                @Override
                public Destination visit(AccountDestination accountDestination) throws Exception {
                    accountDestination.setUserId(transactionDetail.getSentPayment()
                            .getUserId());
                    return accountDestination;
                }

                @Override
                public Destination visit(IntentVpaDestination intentVpaDestination) throws Exception {
                    throw new UnsupportedOperationException(
                            "Intent VPA payment instrument is not supported for penalty disbursement");
                }

                @Override
                public Destination visit(MerchantDestination merchantDestination) throws Exception {
                    throw new UnsupportedOperationException(
                            "Merchant payment instrument is not supported for penalty disbursement");
                }

                @Override
                public Destination visit(MerchantUserDestination merchantUserDestination) throws Exception {
                    throw new UnsupportedOperationException(
                            "Merchant User payment instrument is not supported for penalty disbursement");
                }

                @Override
                public Destination visit(PhoneDestination phoneDestination) throws Exception {
                    throw new UnsupportedOperationException(
                            "Phone payment instrument is not supported for penalty disbursement");
                }

                @Override
                public Destination visit(UserDestination userDestination) throws Exception {
                    userDestination.setUserId(transactionDetail.getSentPayment()
                            .getUserId());
                    return userDestination;
                }

                @Override
                public Destination visit(VpaDestination vpaDestination) throws Exception {
                    throw new UnsupportedOperationException(
                            "VPA payment instrument is not supported for penalty disbursement");
                }

                @Override
                public Destination visit(GiftCardDestination giftCardDestination) throws Exception {
                    giftCardDestination.setUserId(transactionDetail.getSentPayment()
                            .getUserId());
                    return giftCardDestination;
                }
            });
        }catch (Exception exception){
            log.error("Exception in getting custom destination for penalty disbursement: {}", exception.getMessage());
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.PENALTY_DISBURSEMENT_ERROR,
                    "Unable to get custom destination for penalty disbursement: " + exception.getMessage());
        }

    }

    public String convertListOfProbablesToString(List<PenaltyProbable> probables)
    {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String[] headersArray = {
            "Penalty Probable Id",
            "Penalty Class Id",
            "Beneficiary Type",
            "Beneficiary Id",
            "Transaction Id",
            "Transaction Amount",
            "Initial Penalty Amount",
            "TriggeredAt",
            "Due Date",
            "Created",
            "Updated"
        };
        String headers = String.join(", ", headersArray);
        return (headers + probables.stream()
            .map(probable -> "\n" + String.join(",",
                probable.getProbableId(),
                probable.getPenaltyClassId(),
                probable.getBeneficiary().getType().toString(),
                probable.getBeneficiary().getId(),
                probable.getTransactionId(),
                String.valueOf(probable.getTransactionAmount()),
                String.valueOf(probable.getInitialPenaltyAmount()),
                CommonUtils.formatDate(probable.getTriggeredAt(), dateFormat),
                CommonUtils.formatDate(probable.getDueDate(), dateFormat),
                CommonUtils.formatDate(probable.getCreated(), dateFormat),
                CommonUtils.formatDate(probable.getUpdated(), dateFormat)
            ))
            .collect(Collectors.joining("")));
    }

    public String convertListOfPenaltiesToString(List<Penalty> penalties) {

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String[] headersArray = {
            "Penalty Class Id",
            "Penalty Id",
            "Penalty Probable Id",
            "Beneficiary Type",
            "Beneficiary Id",
            "Transaction Id",
            "Status",
            "Initial Penalty Amount",
            "Final Penalty Amount",
            "Transaction Amount",
            "CreatedAt",
            "UpdatedAt"
        };

        String headers = String.join(", ", headersArray);
        return (headers + penalties.stream()
            .map(penalty ->  "\n" + String.join(",",
                    penalty.getPenaltyClassId(),
                    penalty.getPenaltyId(),
                    penalty.getPenaltyProbableId(),
                    penalty.getBeneficiary().getType().toString(),
                    penalty.getBeneficiary().getId(),
                    penalty.getTransactionId(),
                    penalty.getStatus().toString(),
                    String.valueOf(penalty.getInitialPenaltyAmount()),
                    String.valueOf(penalty.getFinalPenaltyAmount()),
                    String.valueOf(penalty.getTransactionAmount()),
                    CommonUtils.formatDate(penalty.getCreatedAt(), dateFormat),
                    CommonUtils.formatDate(penalty.getUpdatedAt(), dateFormat)
                ))
            .collect(Collectors.joining("")));
    }

    public Destination getDestination(PaymentInstrumentType destinationType,
                                       Penalty penaltyInstance,
                                       TransactionDetail transactionDetail) {
        return destinationType.visit(new PaymentInstrumentTypeVisitor<>() {
            @Override
            public Destination visitWallet() {
                throw new UnsupportedOperationException(
                        "Wallet payment instrument is not supported for penalty disbursement");
            }

            @Override
            public Destination visitAccount() {
                AccountPaymentInstrument accountPaymentInstrument = transactionDetail.getSentPayment()
                        .getPaidFrom()
                        .stream()
                        .filter(paymentInstrument -> paymentInstrument.getType()
                                .equals(PaymentInstrumentType.ACCOUNT))
                        .map(AccountPaymentInstrument.class::cast)
                        .findFirst()
                        .orElseThrow(() -> new RuntimeException(
                                "Account payment instrument not found in transaction detail"));
                return AccountDestination.builder()
                        .accountId(accountPaymentInstrument.getAccountId())
                        .amount(penaltyInstance.getFinalPenaltyAmount())
                        .userId(transactionDetail.getSentPayment()
                                .getUserId())
                        .build();
            }

            @Override
            public Destination visitEgv() {
                return GiftCardDestination.builder()
                        .amount(penaltyInstance.getFinalPenaltyAmount())
                        .userId(transactionDetail.getSentPayment()
                                .getUserId())
                        .programId("TOA_VO")
                        .giftCardDestinationType(GiftCardDestinationType.EGV_USER_VAULT)
                        .build();
            }

            @Override
            public Destination visitCreditCard() {
                throw new UnsupportedOperationException(
                        "Credit Card payment instrument is not supported for penalty disbursement");
            }

            @Override
            public Destination visitDebitCard() {
                throw new UnsupportedOperationException(
                        "Debit Card payment instrument is not supported for penalty disbursement");
            }

            @Override
            public Destination visitNetBanking() {
                throw new UnsupportedOperationException(
                        "Net Banking payment instrument is not supported for penalty disbursement");
            }

            @Override
            public Destination visitExternalVpa() {
                throw new UnsupportedOperationException(
                        "External VPA payment instrument is not supported for penalty disbursement");
            }

            @Override
            public Destination visitExternalWallet() {
                throw new UnsupportedOperationException(
                        "External Wallet payment instrument is not supported for penalty disbursement");
            }

            @Override
            public Destination visitBnpl() {
                throw new UnsupportedOperationException(
                        "BNPL payment instrument is not supported for penalty disbursement");
            }

            @Override
            public Destination visitCreditLine() {
                throw new UnsupportedOperationException(
                        "Credit Line payment instrument is not supported for penalty disbursement");
            }
        });

    }

    public Date addDaystoDate(Date date, int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, days);
        return calendar.getTime();
    }

    public Date addOneDayToDate(Date date) {
        return addDaystoDate(date, 1);
    }

    public Date addTwoDaysToDate(Date date) {
        return addDaystoDate(date, 2);
    }

    public Date subOneDayFromDate(Date date) {
        return addDaystoDate(date, -1);
    }
}
