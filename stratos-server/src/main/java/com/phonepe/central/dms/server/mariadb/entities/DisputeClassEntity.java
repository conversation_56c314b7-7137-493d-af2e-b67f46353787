package com.phonepe.central.dms.server.mariadb.entities;

import com.phonepe.central.dispute.DisputeClassState;
import com.phonepe.central.dispute.DisputeLevel;
import com.phonepe.services.ppo.core.storage.mariadb.Sharded;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "dms_dispute_class", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"tenant_id", "dispute_level", "name", "version"})}, indexes = {
        @Index(name = "idx_dispute_class", columnList = "tenant_id,dispute_class_id")})
@FieldNameConstants
public class DisputeClassEntity implements Sharded {

    private static final long serialVersionUID = -6548024317040241952L;

    @Id
    @Column(name = "id", unique = true, insertable = false, updatable = false, nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    @NotBlank
    @Column(name = "dispute_class_id", nullable = false)
    private String disputeClassId;


    @NotBlank
    @Column(name = "name", nullable = false)
    private String name;

    @NotBlank
    @Column(name = "description")
    private String description;

    @NotBlank
    @Column(name = "workflow_class_id", nullable = false)
    private String workflowClassId;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "dispute_level", nullable = false)
    private DisputeLevel disputeLevel;

    @NotBlank
    @Column(name = "tenant_id", nullable = false)
    private String tenantId;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "state", nullable = false)
    private DisputeClassState state;

    @NotNull
    @Column(name = "version", updatable = false, nullable = false)
    private Integer version = 1;


    @Column(name = "approved_by", nullable = false)
    private String approvedBy;

    @NotBlank
    @Column(name = "updated_by", nullable = false)
    private String updatedBy;

    @NotBlank
    @Column(name = "created_by", updatable = false, nullable = false)
    private String createdBy;


    @CreationTimestamp
    @Column(name = "created_at", columnDefinition = "datetime(3) DEFAULT current_timestamp", updatable = false, nullable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", columnDefinition = "datetime(3) DEFAULT current_timestamp ON UPDATE current_timestamp", nullable = false)
    private LocalDateTime updatedAt;

    @Override
    public String getKey() {
        return tenantId;
    }

}
