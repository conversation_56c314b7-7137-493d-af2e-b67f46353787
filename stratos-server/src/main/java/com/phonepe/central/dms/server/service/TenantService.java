package com.phonepe.central.dms.server.service;


import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.central.dispute.request.tenant.TenantCreateRequest;
import com.phonepe.central.dispute.request.tenant.TenantInfo;
import com.phonepe.central.dispute.request.tenant.TenantSearchRequest;
import com.phonepe.central.dispute.request.tenant.TenantUpdateRequest;
import com.phonepe.central.dispute.tenant.Tenant;
import com.phonepe.central.dispute.tenant.TenantState;
import com.phonepe.central.dms.server.mariadb.entities.TenantEntity;
import com.phonepe.central.dms.server.mariadb.repository.TenantRepository;
import com.phonepe.central.dms.server.utils.TenantConvertorUtils;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class TenantService {

    private final TenantRepository tenantRepository;

    public Tenant createNewTenant(final TenantCreateRequest tenantCreateRequest) {
        Optional<TenantEntity> existingTenant = tenantRepository.findByNameAndSubCategory(tenantCreateRequest.getName(),
                tenantCreateRequest.getSubCategory());

        if (existingTenant.isPresent()) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.TENANT_ALREADY_EXISTS,
                    "Tenant already exists with name " + tenantCreateRequest.getName() + "and sub category"
                            + tenantCreateRequest.getSubCategory());
        }

        TenantEntity tenantEntity = TenantConvertorUtils.tenantCreateRequestToTenantEntity(tenantCreateRequest);
        Optional<TenantEntity> storedEntity = tenantRepository.save(tenantEntity);
        return storedEntity.map(TenantConvertorUtils::tenantEntityToTenant)
                .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.UNABLE_TO_CREATE_TENANT));
    }

    public List<Tenant> searchForTenants(final TenantSearchRequest tenantSearchRequest) {
        List<TenantEntity> tenantEntities = getTenantEntities(tenantSearchRequest);
        return tenantEntities.stream()
                .map(TenantConvertorUtils::tenantEntityToTenant)
                .toList();
    }

    public Tenant updateTenant(final String tenantId,
                               final TenantUpdateRequest tenantUpdateRequest) {
        Optional<TenantEntity> entityOptional = getTenantEntities(TenantSearchRequest.builder()
                .tenantId(tenantId)
                .build()).stream()
                .findFirst();

        if (entityOptional.isPresent() && TenantState.DELETED.equals(entityOptional.get()
                .getState())) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.TENANT_NOT_FOUND,
                    "Cannot update tenant with tenant id " + tenantId + " as it is already deleted");
        }

        return entityOptional.map(entity -> {
                    if (CollectionUtils.isNotEmpty(tenantUpdateRequest.getEmailIds())) {
                        entity.setEmailIds(StringUtils.join(tenantUpdateRequest.getEmailIds(), ','));
                    }
                    if (StringUtils.isNotBlank(tenantUpdateRequest.getDescription())) {
                        entity.setDescription(tenantUpdateRequest.getDescription());
                    }

                    Optional<TenantEntity> updatedEntity = tenantRepository.save(entity);
                    return updatedEntity.map(TenantConvertorUtils::tenantEntityToTenant)
                            .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.UNABLE_TO_UPDATE_TENANT));
                })
                .orElse(null);
    }


    public void deleteTenant(final String tenantId) {
        TenantEntity entity = getTenantEntities(TenantSearchRequest.builder()
                .tenantId(tenantId)
                .build()).stream()
                .findFirst()
                .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.TENANT_NOT_FOUND,
                        "Tenant with tenant id " + tenantId + " not found"));

        if (TenantState.DELETED.equals(entity.getState())) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.TENANT_NOT_FOUND,
                    "Tenant with tenant id " + tenantId + " is already deleted");
        }

        entity.setState(TenantState.DELETED);
        tenantRepository.save(entity);
    }


    public Tenant getTenantByTenantId(final String tenantId) {
        TenantSearchRequest tenantSearchRequest = TenantSearchRequest.builder()
                .tenantId(tenantId)
                .build();
        return getTenantEntities(tenantSearchRequest).stream()
                .findFirst()
                .map(TenantConvertorUtils::tenantEntityToTenant)
                .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.TENANT_NOT_FOUND,
                        String.format("Tenant not found with tenant id : %s ", tenantId)));
    }

    public List<Tenant> getTenant(final TenantInfo tenantInfo) {
        List<TenantEntity> tenantEntities = this.getTenantEntities(TenantSearchRequest.builder()
                .name(tenantInfo.getName())
                .subCategory(tenantInfo.getSubCategory())
                .build());
        return tenantEntities.stream()
                .map(TenantConvertorUtils::tenantEntityToTenant)
                .toList();
    }

    public String getTenantId(final String tenantName,
                              final String tenantSubCategory) {
        Optional<TenantEntity> entity = tenantRepository.findByNameAndSubCategory(tenantName, tenantSubCategory);
        return entity.map(TenantEntity::getTenantId)
                .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.TENANT_NOT_FOUND,
                        "Tenant not found with name " + tenantName + " and sub category " + tenantSubCategory));
    }

    private List<TenantEntity> getTenantEntities(final TenantSearchRequest tenantSearchRequest) {
        return tenantRepository.getTenantList(tenantSearchRequest);
    }

}
