package com.phonepe.central.dms.server.mariadb.repository;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.central.dispute.request.tenant.TenantSearchRequest;
import com.phonepe.central.dms.server.mariadb.entities.TenantEntity;
import com.phonepe.central.dms.server.mariadb.entities.TenantEntity.Fields;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.RelationalDaoCrudRepository;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import io.appform.dropwizard.sharding.dao.RelationalDao;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;

@Slf4j
@Singleton
public class TenantRepository extends RelationalDaoCrudRepository<TenantEntity> {

    private static final Integer MAX_PAGE_SIZE = 2000;

    @Inject
    public TenantRepository(RelationalDao<TenantEntity> relationalDao) {
        super(relationalDao);
    }

    public List<TenantEntity> getTenantList(TenantSearchRequest tenantSearchRequest) {
        try {
            final var detachedCriteria = DetachedCriteria.forClass(TenantEntity.class);
            if (StringUtils.isNotBlank(tenantSearchRequest.getName())) {
                detachedCriteria.add(Restrictions.eq(TenantEntity.Fields.name, tenantSearchRequest.getName()));
            }
            if (StringUtils.isNotBlank(tenantSearchRequest.getSubCategory())) {
                detachedCriteria.add(
                        Restrictions.eq(TenantEntity.Fields.subCategory, tenantSearchRequest.getSubCategory()));
            }
            if (tenantSearchRequest.getState() != null) {
                detachedCriteria.add(Restrictions.eq(Fields.state, tenantSearchRequest.getState()));
            }
            if (StringUtils.isNotBlank(tenantSearchRequest.getTenantId())) {
                detachedCriteria.add(Restrictions.eq(Fields.tenantId, tenantSearchRequest.getTenantId()));
                return this.relationalDao.select(tenantSearchRequest.getTenantId(), detachedCriteria, 0, MAX_PAGE_SIZE);
            } else {
                return this.relationalDao.scatterGather(detachedCriteria, 0, MAX_PAGE_SIZE);
            }
        } catch (Exception exception) {
            log.error("Exception in getting tenants list for search request {} : ", tenantSearchRequest, exception);
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.DB_ERROR, exception);
        }
    }

    public Optional<TenantEntity> findByNameAndSubCategory(String name,
                                                           String subCategory) {

        try {
            DetachedCriteria criteria = DetachedCriteria.forClass(TenantEntity.class)
                    .add(Restrictions.eq("name", name))
                    .add(Restrictions.eq("subCategory", subCategory));
            return this.relationalDao.scatterGather(criteria, 0, MAX_PAGE_SIZE)
                    .stream()
                    .findFirst();
        } catch (Exception exception) {
            log.error("Exception in getting tenant with tenant name {} and tenant sub category {} : ", name,
                    subCategory, exception);
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.DB_ERROR, exception);
        }
    }


}