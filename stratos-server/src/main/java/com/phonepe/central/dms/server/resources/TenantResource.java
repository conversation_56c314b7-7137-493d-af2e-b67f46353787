package com.phonepe.central.dms.server.resources;

import static com.phonepe.olympus.im.models.core.CoreConstants.BEARER;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.google.inject.Inject;
import com.phonepe.central.dispute.request.tenant.TenantCreateRequest;
import com.phonepe.central.dispute.request.tenant.TenantSearchRequest;
import com.phonepe.central.dispute.request.tenant.TenantUpdateRequest;
import com.phonepe.central.dispute.tenant.Tenant;
import com.phonepe.central.dms.server.service.TenantService;
import com.phonepe.merchant.platform.stratos.server.core.models.ApiState.ApiStateString;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import io.dropwizard.auth.Auth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Path("/v1/dms/tenants")
@Tag(name = "Dispute Management Service Tenant related API's")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
@SecurityScheme(name = BEARER, type = SecuritySchemeType.APIKEY, in = SecuritySchemeIn.HEADER, paramName = "Authorization")
@SecurityRequirement(name = BEARER)
public class TenantResource {

    private final TenantService tenantService;

    @POST
    @ExceptionMetered
    @Path("/")
    @Operation(summary = "Create Tenant for Dispute management service")
    @ApiKillerMeta(tags = {ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response createTenant(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                 @NotNull @Valid TenantCreateRequest tenantCreateRequest) {
        try {
            log.info("Input requests for creation of a dms tenant {}", tenantCreateRequest);
            Tenant response = tenantService.createNewTenant(tenantCreateRequest);
            return Response.ok(response)
                    .build();
        } catch (Exception exception) {
            log.error("Exception in creating a tenant {} : ", tenantCreateRequest, exception);
            throw DisputeExceptionUtil.propagate(exception);
        }
    }

    @POST
    @ExceptionMetered
    @Path("/search")
    @Operation(summary = "Search tenants for Dispute management service")
    @ApiKillerMeta(tags = {ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response searchTenants(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                  @NotNull @Valid TenantSearchRequest tenantSearchRequest) {
        try {
            log.info("Received request to search tenants {}", tenantSearchRequest);
            List<Tenant> response = tenantService.searchForTenants(tenantSearchRequest);
            return Response.ok(response)
                    .build();
        } catch (Exception exception) {
            log.error("Exception while searching for tenants with search request {} : ", tenantSearchRequest,
                    exception);
            throw DisputeExceptionUtil.propagate(exception);
        }
    }

    @DELETE
    @ExceptionMetered
    @Path("/{tenantId}")
    @Operation(summary = "Delete a tenant for Dispute management service")
    @ApiKillerMeta(tags = {ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response removeTenant(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                 @PathParam("tenantId") @NotBlank String tenantId) {
        try {
            log.info("Received request to delete tenant with tenantId: {}", tenantId);
            tenantService.deleteTenant(tenantId);
            return Response.ok(String.format("Tenant with tenantId %s deleted successfully", tenantId))
                    .build();
        } catch (Exception exception) {
            log.error("Exception while deleting tenant with tenant id {} : ", tenantId, exception);
            throw DisputeExceptionUtil.propagate(exception);
        }

    }

    @PUT
    @ExceptionMetered
    @Path("/{tenantId}")
    @Operation(summary = "update tenant details for Dispute management service")
    @ApiKillerMeta(tags = {ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response updateTenantDetails(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                        @PathParam("tenantId") @NotBlank String tenantId,
                                        @NotNull @Valid TenantUpdateRequest updateRequest) {
        try {
            log.info("Input requests for updating tenant : {} ", updateRequest);
            Tenant response = tenantService.updateTenant(tenantId, updateRequest);
            return Response.ok(response)
                    .build();
        } catch (Exception exception) {
            log.error("Exception in updating tenant {} with emails {} : ", tenantId, updateRequest, exception);
            throw DisputeExceptionUtil.propagate(exception);
        }
    }

}
