package com.phonepe.central.dms.server.resources;

import static com.phonepe.olympus.im.models.core.CoreConstants.BEARER;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.google.inject.Inject;
import com.phonepe.central.dispute.evidence.DisputeEvidenceStatus;
import com.phonepe.central.dispute.request.disputeinstance.DisputeRegisterRequest;
import com.phonepe.central.dispute.request.disputeinstance.DisputeTransitionRequest;
import com.phonepe.central.dispute.request.disputeinstance.filter.DisputeInstanceFilter;
import com.phonepe.central.dispute.request.evidence.DisputeEvidenceUploadRequest;
import com.phonepe.central.dispute.response.disputeinstance.DisputeInstanceResponse;
import com.phonepe.central.dispute.response.disputeinstance.DisputeTransitionResponse;
import com.phonepe.central.dispute.response.evidence.DisputeEvidenceResponse;
import com.phonepe.merchant.platform.stratos.server.core.models.ApiState.ApiStateString;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import io.dropwizard.auth.Auth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.io.InputStream;
import java.util.List;
import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.glassfish.jersey.media.multipart.FormDataParam;

/**
 * <AUTHOR>
 */
@Slf4j
@Path("/v1/dms/dispute-instance")
@Tag(name = "Dispute Management Service Dispute Class Instance related APIs")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
@SecurityScheme(name = BEARER, type = SecuritySchemeType.APIKEY, in = SecuritySchemeIn.HEADER, paramName = "Authorization")
@SecurityRequirement(name = BEARER)
public class DisputeClassInstanceResource {

    @POST
    @ExceptionMetered
    @RolesAllowed("dms_class_instance/create")
    @Path("/")
    @Operation(summary = "Register Dispute for Dispute class")
    @ApiKillerMeta(tags = {ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response registerDispute(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                    @NotNull DisputeRegisterRequest disputeRegisterRequest) {
        log.info("Received request to register dispute for dispute class with: {}", disputeRegisterRequest);
        return Response.status(Response.Status.NOT_IMPLEMENTED)
                .entity(DisputeInstanceResponse.builder()
                        .build())
                .build();
    }


    @POST
    @ExceptionMetered
    @RolesAllowed("dms_class_instance/process_transition")
    @Path("/transition")
    @Operation(summary = "Process Transition for Dispute class instance")
    @ApiKillerMeta(tags = {ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response processTransition(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                      @NotNull @Valid DisputeTransitionRequest disputeTransitionRequest) {
        log.info("Received request to process transition for dispute class instance with: {}",
                disputeTransitionRequest);
        return Response.status(Response.Status.NOT_IMPLEMENTED)
                .entity(DisputeTransitionResponse.builder()
                        .build())
                .build();
    }

    //TODO : Transition should provide the list of Transition Object :
    // Each object should specify that, Next stage Name, Mandatory input require , This is related to ActionInput require to process
    @GET
    @ExceptionMetered
    @RolesAllowed("dms_class_instance/fetch_transition")
    @Path("/{disputeClassId}/{disputeClassInstanceId}/transition")
    @Operation(summary = "Fetch available transitions for Dispute class instance")
    @ApiKillerMeta(tags = {ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response fetchAvailableTransitions(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                              @PathParam("disputeClassId") String disputeClassId,
                                              @PathParam("disputeClassInstanceId") String disputeClassInstanceId) {
        log.info("Received request to fetch available transitions for dispute class instance with: {}",
                "details not provided in this example");
        return Response.status(Response.Status.NOT_IMPLEMENTED)
                .entity(DisputeTransitionResponse.builder()
                        .build())
                .build();
    }

    @GET
    @ExceptionMetered
    @RolesAllowed("dms_class_instance/fetch_timeline")
    @Path("/{disputeClassId}/{transactionRefId}/timeline")
    @Operation(summary = "Fetch dispute timeline for dispute class Id and transaction Reference for Dispute class instance")
    @ApiKillerMeta(tags = {ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response fetchDisputeTimeline(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                         @PathParam("disputeClassId") String disputeClassId,
                                         @PathParam("transactionRefId") String transactionRefId) {
        log.info("Received request to fetch dispute timeline for dispute class instance with: {}",
                "details not provided in this example");
        //This should be order by created time
        return Response.status(Response.Status.NOT_IMPLEMENTED)
                .entity(List.of(DisputeInstanceResponse.builder()
                        .build()))
                .build();
    }

    @GET
    @ExceptionMetered
    @RolesAllowed("dms_class_instance/dispute_details")
    @Path("/{disputeClassId}/{disputeClassInstanceId}")
    @Operation(summary = "Fetch dispute timeline for dispute class Id and transaction Reference for Dispute class instance")
    @ApiKillerMeta(tags = {ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response fetchDisputeDetails(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                        @PathParam("disputeClassId") String disputeClassId,
                                        @PathParam("disputeClassInstanceId") String disputeClassInstanceId) {
        log.info("Received request to fetch dispute details for dispute class instance with: {}",
                "details not provided in this example");
        return Response.status(Response.Status.NOT_IMPLEMENTED)
                .entity(DisputeInstanceResponse.builder()
                        .build())
                .build();
    }


    @POST
    @ExceptionMetered
    @RolesAllowed("dms_class_instance/filter")
    @Path("/{disputeClassId}/filter")
    @Operation(summary = "Fetch dispute instances with filters for dispute class Id")
    @ApiKillerMeta(tags = {ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response fetchFilterDisputeInstances(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                                @PathParam("disputeClassId") String disputeClassId,
                                                @NotNull @Valid DisputeInstanceFilter disputeInstanceFilter) {
        log.info("Received request to fetch dispute instances with filters for dispute class Id: {} with filter: {}",
                disputeClassId, disputeInstanceFilter);
        return Response.status(Response.Status.NOT_IMPLEMENTED)
                .entity(List.of(DisputeInstanceResponse.builder()
                        .build()))
                .build();
    }

    @POST
    @ExceptionMetered
    @RolesAllowed("dms_class_instance/upload_evidence")
    @Path("/{disputeClassId}/{disputeClassInstanceId}/evidence/upload")
    @Operation(summary = "Upload evidence for dispute class instance")
    @ApiKillerMeta(tags = {ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Produces(MediaType.APPLICATION_JSON)
    public Response uploadEvidenceDisputeInstances(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                                   @HeaderParam(Constants.AUTHORIZATION) @Parameter(required = true, hidden = true) String authToken,
                                                   @PathParam("disputeClassId") String disputeClassId,
                                                   @PathParam("disputeClassInstanceId") String disputeClassInstanceId,
                                                   @NotNull @Valid DisputeEvidenceUploadRequest evidenceUploadRequest,
                                                   @NotNull @FormDataParam("file") final InputStream inputStream) {
        log.info(
                "Received request to upload evidence for disputeClassId {}, disputeClassInstanceId {} with evidenceUploadRequest: {}",
                disputeClassId, disputeClassInstanceId, evidenceUploadRequest);
        return Response.status(Response.Status.NOT_IMPLEMENTED)
                .entity(List.of(DisputeEvidenceResponse.builder()
                        .build()))
                .build();
    }

    @GET
    @ExceptionMetered
    @RolesAllowed("dms_class_instance/fetch_evidence")
    @Path("/{disputeClassId}/{disputeClassInstanceId}/evidence")
    @Operation(summary = "Fetch evidences for dispute class instance")
    @ApiKillerMeta(tags = {ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response fetchEvidenceDisputeInstances(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                                  @HeaderParam(Constants.AUTHORIZATION) @Parameter(required = true, hidden = true) String authToken,
                                                  @PathParam("disputeClassId") String disputeClassId,
                                                  @PathParam("disputeClassInstanceId") String disputeClassInstanceId,
                                                  @QueryParam("status") DisputeEvidenceStatus status) {
        log.info("Received request to fetch evidences for disputeClassId {}, disputeClassInstanceId {}", disputeClassId,
                disputeClassInstanceId);
        return Response.status(Response.Status.NOT_IMPLEMENTED)
                .entity(List.of(DisputeEvidenceResponse.builder()
                        .build()))
                .build();
    }


}
