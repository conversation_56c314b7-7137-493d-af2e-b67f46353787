package com.phonepe.central.dms.server.mariadb.entities;

import com.phonepe.central.dispute.DisputeState;
import com.phonepe.services.ppo.core.storage.mariadb.Sharded;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "dms_dispute", uniqueConstraints = {@UniqueConstraint(columnNames = {"dispute_id"}),
        @UniqueConstraint(columnNames = {"tenant_id", "name", "transaction_id"}),}, indexes = {
        @Index(name = "idx_dispute_class", columnList = "tenant_id,transaction_id")})
@FieldNameConstants
public class DisputeEntity implements Sharded {

    private static final long serialVersionUID = -6548024317040241952L;

    @Id
    @Column(name = "id", unique = true, insertable = false, updatable = false, nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    @NotBlank
    @Column(name = "dispute_id", nullable = false)
    private String disputeId;


    @NotBlank
    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "description")
    private String description;

    @NotBlank
    @Column(name = "transaction_id", nullable = false)
    private String transactionId;


    @NotNull
    @Column(name = "transaction_amount", nullable = false)
    private Long transactionAmount;

    @NotBlank
    @Column(name = "tenant_id", nullable = false)
    private String tenantId;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "state", nullable = false)
    private DisputeState state;

    @Column(name = "partition_id", nullable = false, updatable = false)
    private int partitionId;


    @CreationTimestamp
    @Column(name = "raised_at", columnDefinition = "datetime(3) DEFAULT current_timestamp ON UPDATE current_timestamp", nullable = false)
    private LocalDateTime raisedAt;

    @CreationTimestamp
    @Column(name = "closed_at", columnDefinition = "datetime(3)", nullable = false)
    private LocalDateTime closedAt;

    @CreationTimestamp
    @Column(name = "created_at", columnDefinition = "datetime(3) DEFAULT current_timestamp", updatable = false, nullable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", columnDefinition = "datetime(3) DEFAULT current_timestamp ON UPDATE current_timestamp", nullable = false)
    private LocalDateTime updatedAt;


    @Override
    public String getKey() {
        return tenantId;
    }


}
