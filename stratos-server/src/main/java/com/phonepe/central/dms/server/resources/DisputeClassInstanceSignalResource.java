package com.phonepe.central.dms.server.resources;

import static com.phonepe.olympus.im.models.core.CoreConstants.BEARER;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.google.inject.Inject;
import com.phonepe.central.dispute.request.disputeinstance.signal.credit.CreditSignalRequest;
import com.phonepe.central.dispute.request.disputeinstance.signal.debit.DebitSignalRequest;
import com.phonepe.central.dispute.response.signal.CreditSignalResponse;
import com.phonepe.central.dispute.response.signal.DebitSignalResponse;
import com.phonepe.merchant.platform.stratos.server.core.models.ApiState.ApiStateString;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import io.dropwizard.auth.Auth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Path("/v1/dms/dispute-instance/signal")
@Tag(name = "Dispute Class Instance signal related APIs")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
@SecurityScheme(name = BEARER, type = SecuritySchemeType.APIKEY, in = SecuritySchemeIn.HEADER, paramName = "Authorization")
@SecurityRequirement(name = BEARER)
public class DisputeClassInstanceSignalResource {

    @POST
    @ExceptionMetered
    @RolesAllowed("dms_class_instance/credit_signal")
    @Path("/credit")
    @Operation(summary = "Process Credit Signal for Dispute class instance")
    @ApiKillerMeta(tags = {ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response processCreditSignal(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                        @NotNull @Valid CreditSignalRequest creditSignalRequest) {
        log.info("Received request to process credit signal for dispute class instance with: {}", creditSignalRequest);
        return Response.status(Response.Status.NOT_IMPLEMENTED)
                .entity(CreditSignalResponse.builder()
                        .build())
                .build();
    }

    @POST
    @ExceptionMetered
    @RolesAllowed("dms_class_instance/debit_signal")
    @Path("/debit")
    @Operation(summary = "Process Debit Signal for Dispute class instance")
    @ApiKillerMeta(tags = {ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response processDebitSignal(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                       @NotNull DebitSignalRequest debitSignalRequest) {
        log.info("Received request to process debit signal for dispute class instance with: {}", debitSignalRequest);
        return Response.status(Response.Status.NOT_IMPLEMENTED)
                .entity(DebitSignalResponse.builder()
                        .build())
                .build();
    }

}
