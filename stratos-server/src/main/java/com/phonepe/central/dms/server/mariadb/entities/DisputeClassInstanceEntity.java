package com.phonepe.central.dms.server.mariadb.entities;

import com.phonepe.central.dispute.DisputeState;
import com.phonepe.central.dispute.user.UserType;
import com.phonepe.services.ppo.core.storage.mariadb.Sharded;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "dms_dispute_class_instance", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"dispute_class_id", "dispute_id", "dispute_class_instance_id"})}, indexes = {
        @Index(name = "idx_dispute_class", columnList = "dispute_class_id,dispute_class_instance_id")})
@FieldNameConstants
public class DisputeClassInstanceEntity implements Sharded {

    private static final long serialVersionUID = -6548024317040241952L;

    @Id
    @Column(name = "id", unique = true, insertable = false, updatable = false, nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    @NotBlank
    @Column(name = "dispute_id", nullable = false)
    private String disputeId;

    @NotBlank
    @Column(name = "dispute_class_instance_id", nullable = false)
    private String disputeClassInstanceId;

    @NotBlank
    @Column(name = "dispute_class_id", updatable = false, nullable = false)
    private String disputeClassId;

    @NotBlank
    @Column(name = "workflow_class_instance_id", updatable = false, nullable = false)
    private String workflowClassInstanceId;

    @NotNull
    @Min(1L)
    @Column(name = "disputed_amount", updatable = false, nullable = false)
    private Long disputedAmount;

    @NotNull
    @Min(0L)
    @Column(name = "penalty_amount", nullable = false)
    private Long penaltyAmount;

    @NotNull
    @Min(0L)
    @Column(name = "total_charges", nullable = false)
    private Long totalCharges;

    @Column(name = "accepted_amount", nullable = false)
    private Long acceptedAmount;

    @NotBlank
    @Column(name = "raised_by_user_id", updatable = false, nullable = false)
    private String raisedByUserId;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "raised_by_user_type", nullable = false)
    private UserType raisedByUserType;

    @NotBlank
    @Column(name = "raised_against_by_user_id", updatable = false, nullable = false)
    private String raisedAgainstByUserId;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "raised_against_by_user_type", nullable = false)
    private UserType raisedAgainstByUserType;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "state", nullable = false)
    private DisputeState state;

    @Column(name = "partition_id", nullable = false, updatable = false)
    private int partitionId;

    @CreationTimestamp
    @Column(name = "raised_at", columnDefinition = "datetime(3) DEFAULT current_timestamp ON UPDATE current_timestamp", nullable = false)
    private LocalDateTime raisedAt;

    @CreationTimestamp
    @Column(name = "closed_at", columnDefinition = "datetime(3)", nullable = false)
    private LocalDateTime closedAt;

    @CreationTimestamp
    @Column(name = "created_at", columnDefinition = "datetime(3) DEFAULT current_timestamp", updatable = false, nullable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", columnDefinition = "datetime(3) DEFAULT current_timestamp ON UPDATE current_timestamp", nullable = false)
    private LocalDateTime updatedAt;


    @Override
    public String getKey() {
        return disputeClassId;
    }


}
