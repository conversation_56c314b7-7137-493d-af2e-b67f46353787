package com.phonepe.central.dms.server.utils;

import com.phonepe.central.dispute.request.tenant.TenantCreateRequest;
import com.phonepe.central.dispute.tenant.Tenant;
import com.phonepe.central.dispute.tenant.TenantState;
import com.phonepe.central.dms.server.mariadb.entities.TenantEntity;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang.StringUtils;

@UtilityClass
public class TenantConvertorUtils {

    private static final String TENANT_ID_PREFIX = "TNT";

    public Tenant tenantEntityToTenant(TenantEntity tenantEntity) {
        if (tenantEntity != null) {
            return Tenant.builder()
                    .tenantId(tenantEntity.getTenantId())
                    .name(tenantEntity.getName())
                    .subCategory(tenantEntity.getSubCategory())
                    .description(tenantEntity.getDescription())
                    .emailIds(tenantEntity.getEmailsList())
                    .state(tenantEntity.getState())
                    .createdAt(tenantEntity.getCreatedAt())
                    .updatedAt(tenantEntity.getUpdatedAt())
                    .build();

        }
        return null;
    }

    public TenantEntity tenantCreateRequestToTenantEntity(TenantCreateRequest tenantCreateRequest) {
        if (tenantCreateRequest != null) {
            return TenantEntity.builder()
                    .tenantId(IdGenerator.generate(TENANT_ID_PREFIX)
                            .getId())
                    .name(tenantCreateRequest.getName())
                    .subCategory(tenantCreateRequest.getSubCategory())
                    .description(tenantCreateRequest.getDescription())
                    .state(TenantState.ACTIVE)
                    .emailIds(StringUtils.join(tenantCreateRequest.getEmailIds(), ','))
                    .build();
        }
        return null;
    }

}