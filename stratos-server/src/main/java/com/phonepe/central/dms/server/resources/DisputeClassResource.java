package com.phonepe.central.dms.server.resources;

import static com.phonepe.olympus.im.models.core.CoreConstants.BEARER;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.google.inject.Inject;
import com.phonepe.central.dispute.request.disputeclass.DisputeClassCreateRequest;
import com.phonepe.central.dispute.request.disputeclass.DisputeClassSearchRequest;
import com.phonepe.central.dispute.request.disputeclass.DisputeClassUpdateRequest;
import com.phonepe.central.dispute.response.disputeclass.DisputeClassResponse;
import com.phonepe.merchant.platform.stratos.server.core.models.ApiState.ApiStateString;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import io.dropwizard.auth.Auth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Path("/v1/dms/dispute-class")
@Tag(name = "Dispute Management Service Dispute Class related APIs")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
@SecurityScheme(name = BEARER, type = SecuritySchemeType.APIKEY, in = SecuritySchemeIn.HEADER, paramName = "Authorization")
@SecurityRequirement(name = BEARER)
public class DisputeClassResource {

    @POST
    @ExceptionMetered
    @RolesAllowed("dms_class/create")
    @Path("/")
    @Operation(summary = "Create Dispute class for Dispute management service")
    @ApiKillerMeta(tags = {ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response createDisputeClass(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                       @NotNull @Valid final DisputeClassCreateRequest disputeClassCreateRequest) {
        log.info("Received request to create dispute class with details: {}", disputeClassCreateRequest);
        return Response.status(Response.Status.NOT_IMPLEMENTED)
                .entity(DisputeClassResponse.builder()
                        .build())
                .build();
    }

    @PUT
    @ExceptionMetered
    @RolesAllowed("dms_class/update")
    @Path("/{disputeClassId}")
    @Operation(summary = "Update Dispute class for Dispute management service")
    @ApiKillerMeta(tags = {ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response updateDisputeClass(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                       @PathParam("disputeClassId") @Parameter(required = true) String disputeClassId,
                                       @NotNull @Valid final DisputeClassUpdateRequest disputeClassUpdateRequest) {
        log.info("Received request to update dispute class with ID: {} and details: {}", disputeClassId,
                disputeClassUpdateRequest);
        return Response.status(Response.Status.NOT_IMPLEMENTED)
                .entity(DisputeClassResponse.builder()
                        .build())
                .build();
    }

    @POST
    @ExceptionMetered
    @RolesAllowed("dms_class/search")
    @Path("/search")
    @Operation(summary = "Search dispute classes for Dispute management service")
    @ApiKillerMeta(tags = {ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response searchDisputeClass(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                       @NotNull @Valid final DisputeClassSearchRequest disputeClassSearchRequest) {
        log.info("Received request to search dispute classes with criteria: {}", disputeClassSearchRequest);
        return Response.status(Response.Status.NOT_IMPLEMENTED)
                .entity(List.of(DisputeClassResponse.builder()
                        .build()))
                .build();
    }

    @DELETE
    @ExceptionMetered
    @RolesAllowed("dms_class/delete")
    @Path("/{disputeClassId}")
    @Operation(summary = "Delete Dispute class for Dispute management service")
    @ApiKillerMeta(tags = {ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response deleteDisputeClass(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                       @PathParam("disputeClassId") @Parameter(required = true) String disputeClassId) {
        log.info("Received request to delete dispute class with ID: {} ", disputeClassId);
        return Response.status(Response.Status.NOT_IMPLEMENTED)
                .entity("Dispute class deletion is not implemented yet.")
                .build();
    }
}
