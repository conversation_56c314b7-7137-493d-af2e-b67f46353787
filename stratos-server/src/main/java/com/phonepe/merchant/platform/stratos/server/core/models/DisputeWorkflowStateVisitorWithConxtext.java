package com.phonepe.merchant.platform.stratos.server.core.models;

public interface DisputeWorkflowStateVisitorWithConxtext<T,R> {

    T visitReceived(R context) ;

    T visitRgcsAcceptanceRequired(R context) ;

    T visitRgcsAcceptanceCompleted(R context) ;

    T visitRepresentmentRequired(R context) ;

    T visitNPCIRepresentmentCompleted(R context) ;

    T visitCreditReceived(R context) ;

    T visitRefundBlocked(R context) ;

    T visitFulfilmentDocumentsReceived(R context) ;

    T visitPartialFulfilmentDocumentsReceived(R context) ;

    T visitNPCIPartialRepresentmentCompleted(R context) ;

    T visitPartialCreditReceived(R context) ;

    T visitMerchantNotRespondedWithinTTL(R context) ;

    T visitMerchantAcceptedChargeback(R context) ;

    T visitNPCIAcceptanceCompleted(R context) ;

    T visitAbsorbChargebackRequested(R context) ;

    T visitAbsorbChargebackRejected(R context) ;

    T visitAbsorbChargebackApproved(R context) ;

    T visitChargebackAbsorbed(R context) ;

    T visitChargebackAbsorbedReversed(R context) ;

    T visitRecoverChargebackRequested(R context) ;

    T visitRecoverChargebackRejected(R context) ;

    T visitRecoverChargebackApproved(R context) ;

    T visitRecoverChargebackEventRaised(R context) ;

    T visitRecoverChargebackEventAccepted(R context) ;

    T visitReversalOfRecoveredChargebackRequested(R context) ;

    T visitReversalOfRecoveredChargebackApproved(R context) ;

    T visitReversalOfRecoveredChargebackEventRaised(R context) ;

    T visitReversalOfRecoveredChargebackEventAccepted(R context) ;

    T visitDebitReceived(R context) ;

    T visitPartialDebitReceived(R context) ;

    T visitPgRepresentmentCompleted(R context) ;

    T visitPgAcceptanceCompleted(R context) ;

    T visitPgPartialRepresentmentCompleted(R context) ;

    T visitUdirComplaintRejected(R context) ;

    T visitFailure(R context) ;
    T visitUdirComplaintAccepted(R context) ;
    T visitUdirResponseReceived(R context) ;

    T visitUdirResponseNotReceived(R context) ;

    T visitInternalMerchantRepresentmentRequired(R context) ;

    T visitChargebackCancelled(R context) ;

    T visitToaBlockedDueToKs(R context) ;
    T visitP2pmToaInitiated(R context) ;
    T visitP2pmToaInitiationFailed(R context) ;
    T visitP2pmToaCompleted(R context) ;
    T visitP2pmToaFailed(R context) ;
    T visitP2pmToaPending(R context) ;
    T visitP2pmToaCompletedExternally(R context) ;
    T visitP2pmToaFailedAfterMaxAutoRetry(R context) ;
    T visitRepresentedCompleted(R context) ;
    T visitAcceptanceCompleted(R context) ;
    T visitPartialRepresentmentCompleted(R context) ;
    T visitFraudRejected(R context);
    T visitSuspectedFraud(R context);
    T visitCBRefundCreated(R context);
    T visitCBRefundInitated(R context);
    T visitCBRefundAccepted(R context);
    T visitCBRefundFailed(R context);
    T visitToaOpened(R context);
    T visitToaClosed(R context);
    T visitToaInitiated(R context);
    T visitToaInitiationFailed(R context);
    T visitToaCompleted(R context);
    T visitToaFailed(R context);
    T visitToaPending(R context);
    T visitToaFailedAfterMaxAutoRetry(R context);
    T visitFraudRepresentmentCompleted(R context);
    T visitNpciAckChargeback(R context);
    T visitNpciRejectedChargeback(R context);
    T visitPartialRejectedChargeback(R context);
    T visitPartialAcceptedChargeback(R context);
    T visitFullyAcceptedChargeback(R context);
    T visitAcceptedChargeback(R context);
    T visitRejectedChargeback(R context);
    T visitCbRefundInitiationCompleted(R context);
    T visitCbRefundProcessedExternally(R context);
    T visitToaUnableToProcess(R context);
}
