package com.phonepe.merchant.platform.stratos.server.spokes.toa.resources;

import static com.phonepe.olympus.im.models.core.CoreConstants.BEARER;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.google.inject.Inject;
import com.phonepe.merchant.platform.stratos.models.commons.TransitionContext;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.CommentContext;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilter;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.DisputeWorkflowKey;
import com.phonepe.merchant.platform.stratos.models.disputes.toa.responses.ToaSummary;
import com.phonepe.merchant.platform.stratos.server.core.models.ApiState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.common.services.ToaService;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.p2pm.services.P2pmToaService;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.olympus.im.models.security.ServiceUserPrincipal;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import io.dropwizard.auth.Auth;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.Set;
import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Path("/v1/toa/{disputeType}")
@Tag(name = "TOA Related apis")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
@SecurityScheme(name = BEARER, type = SecuritySchemeType.APIKEY, in = SecuritySchemeIn.HEADER, paramName = "Authorization")
@SecurityRequirement(name = BEARER)
public class ToaResource {

    private final P2pmToaService p2pmToaService;
    private final ToaService toaService;

    @GET
    @Path("/states")
    @ExceptionMetered
    @RolesAllowed("p2pmtoa/summary") //change permission name
    @Operation(summary = "Fetch All States of TOA Workflow")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Set<DisputeWorkflowState> getAllStates(@PathParam("disputeType") final DisputeType disputeType) {

        return disputeType.equals(DisputeType.P2PM_TOA) ?
                p2pmToaService.getAllStates() : toaService.getAllStates();
    }

    @POST
    @Path("/filter")
    @RolesAllowed("p2pmtoa/summary")
    @Operation(summary = "Fetch TOA Summary based on Filter Parameters")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public List<ToaSummary> getToaSummary(
        @Valid final DisputeFilter filter, @PathParam("disputeType") final DisputeType disputeType
    ) {
        return disputeType.equals(DisputeType.P2PM_TOA) ?
                p2pmToaService.filter(filter) : toaService.filter(filter);
    }

    @Hidden
//    @POST
//    @Path("/reconcile")
    @RolesAllowed("p2pmtoa/reconcile")
    @Operation(summary = "Reconcile a Pending TOA")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.DEPRECATED_API}, statusCode = 451)
    public GenericResponse<ToaSummary> reconcileToa(
        @Parameter(hidden = true) @Auth ServiceUserPrincipal serviceUserPrincipal,
        @Valid @NotNull final DisputeWorkflowKey request, @PathParam("disputeType") final DisputeType disputeType
    ) {
        final UserAuthDetails userAuthDetails = serviceUserPrincipal.getUserAuthDetails();
        return disputeType.equals(DisputeType.P2PM_TOA) ?
                p2pmToaService.reconcile(request.getTransactionReferenceId(), request.getDisputeWorkflowId(), userAuthDetails)
                : toaService.reconcile(request.getTransactionReferenceId(), request.getDisputeWorkflowId(), userAuthDetails);
    }

    @POST
    @Path("/retry")
    @RolesAllowed("p2pmtoa/retry")
    @Operation(summary = "Retry a Failed TOA")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public GenericResponse<ToaSummary> retryToa(
        @Parameter(hidden = true) @Auth ServiceUserPrincipal serviceUserPrincipal,
        @Valid @NotNull final DisputeWorkflowKey request, @PathParam("disputeType") final DisputeType disputeType
    ) {
        final UserAuthDetails userAuthDetails = serviceUserPrincipal.getUserAuthDetails();
        return disputeType.equals(DisputeType.P2PM_TOA) ?
                p2pmToaService.retry(request.getTransactionReferenceId(), request.getDisputeWorkflowId(), userAuthDetails)
                : toaService.retry(request.getTransactionReferenceId(), request.getDisputeWorkflowId(), userAuthDetails);
    }

    @POST
    @Path("/processToaExternally/{transactionReferenceId}/{disputeWorkflowId}")
    @RolesAllowed("p2pmtoa/processToaExternally")
    @Operation(summary = "Mark a TOA as processed externally/unable to process")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public GenericResponse<ToaSummary> processToaExternally(
        @Parameter(hidden = true) @Auth ServiceUserPrincipal serviceUserPrincipal,
        @PathParam("transactionReferenceId") final String transactionReferenceId,
        @PathParam("disputeWorkflowId") final String disputeWorkflowId,
        @Valid @NotNull final TransitionContext transitionContext,
        @PathParam("disputeType") final DisputeType disputeType
    ) {
        final UserAuthDetails userAuthDetails = serviceUserPrincipal.getUserAuthDetails();
        return disputeType.equals(DisputeType.P2PM_TOA)?
                p2pmToaService.processToaExternally(transactionReferenceId, disputeWorkflowId, transitionContext, userAuthDetails)
                : toaService.processToaExternally(transactionReferenceId, disputeWorkflowId, transitionContext, userAuthDetails);
    }

    @GET
    @Path("/killSwitch/check")
    @RolesAllowed("p2pmtoa/summary")
    @Operation(summary = "Check if TOA kill switch is enabled")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public GenericResponse<Boolean> isToaKillSwitchEnabled(@PathParam("disputeType") final DisputeType disputeType){
        GenericResponse<Boolean> response = GenericResponse.<Boolean>builder()
                .success(true)
                .build();
        if (disputeType.equals(DisputeType.P2PM_TOA)) {
            response.setData(p2pmToaService.isP2pmToaKillSwitchEnabled());
        } else {
            response.setData(toaService.isKillSwitchEnabled(disputeType));
        }
        return response;
    }

    @POST
    @Path("/killSwitch/engage")
    @RolesAllowed("p2pmtoa/ks_toggle")
    @Operation(summary = "Engage TOA killSwitch")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public GenericResponse<Boolean> engageKillSwitch(
        @Parameter(hidden = true) @Auth ServiceUserPrincipal serviceUserPrincipal,
        @Valid @NotNull final CommentContext commentContext, @PathParam("disputeType") final DisputeType disputeType){
        final UserAuthDetails userAuthDetails = serviceUserPrincipal.getUserAuthDetails();

        GenericResponse<Boolean> response = GenericResponse.<Boolean>builder()
                .success(true)
                .build();
        if (disputeType.equals(DisputeType.P2PM_TOA)) {
            response.setData(p2pmToaService.engageP2pmToaKillSwitch(commentContext.getComment(), userAuthDetails));
        } else {
            response.setData(toaService.engageKillSwitch(disputeType, commentContext.getComment(), userAuthDetails));
        }
        return response;
    }

    @POST
    @Path("/killSwitch/deactivate")
    @RolesAllowed("p2pmtoa/ks_toggle")
    @Operation(summary = "Deactivate TOA killSwitch")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public GenericResponse<Boolean> deactivateKillSwitch(@PathParam("disputeType") final DisputeType disputeType){

        GenericResponse<Boolean> response = GenericResponse.<Boolean>builder()
                .success(true)
                .build();
        if (disputeType.equals(DisputeType.P2PM_TOA)) {
            response.setData(p2pmToaService.deactivateP2pmKillSwitch());
        } else {
            response.setData(toaService.deactivateKillSwitch(disputeType));
        }
        return response;
    }

    @GET
    @RolesAllowed("p2pmtoa/summary")
    @Operation(summary = "Toa Status based on DisputeReferenceId")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public ToaSummary getToaStatusBasedOnDisputeReferenceId(@PathParam("disputeType") final DisputeType disputeType,
                                                            @NotNull @QueryParam("disputeReferenceId") final String disputeReferenceId) {
        return toaService.getToaStatusBasedOnDisputeReferenceId(disputeType, disputeReferenceId);
    }
}
