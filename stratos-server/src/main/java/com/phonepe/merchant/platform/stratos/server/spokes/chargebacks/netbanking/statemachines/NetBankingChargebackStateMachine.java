package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.statemachines;

import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.TransitionLockCommand;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.registries.keys.DisputeStateMachineRegistryKey;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.DisputeStateMachine;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.comments.impls.MandatoryCommentUpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.comments.impls.OptionalCommentUpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.AcceptPartialDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.FraudCheckDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.ResetChargebackAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.UpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.interceptors.impls.DisputeErrorHandlingInterceptor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.ApproveRecoverChargebackAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.ApproveRecoveryReversalChargebackAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.BlockRefundAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.RaiseChargebackRecoveryAccountingEventAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.RaiseChargebackRecoveryReversalAccountingEventAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.UnblockRefundAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.statemachines.actions.FullRefundCreationAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.statemachines.actions.NetBankingChargebackCreateEntryAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.statemachines.actions.RefundCreationAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.statemachines.actions.RefundInitiationAction;
import java.util.EnumSet;
import lombok.SneakyThrows;
import org.springframework.statemachine.config.builders.StateMachineConfigurationConfigurer;
import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;

public abstract class NetBankingChargebackStateMachine extends DisputeStateMachine {

    private final NetBankingChargebackCreateEntryAction netbankingChargebackCreateEntryAction;
    private final BlockRefundAction blockRefundAction;
    private final UpdateDisputeStateAction updateDisputeStateAction;
    private final UnblockRefundAction unblockRefundAction;
    private final MandatoryCommentUpdateDisputeStateAction mandatoryCommentUpdateDisputeStateAction;
    private final OptionalCommentUpdateDisputeStateAction optionalCommentUpdateDisputeStateAction;
    private final ApproveRecoverChargebackAction approveRecoverChargebackAction;
    private final RaiseChargebackRecoveryAccountingEventAction raiseChargebackRecoveryAccountingEventAction;
    private final ApproveRecoveryReversalChargebackAction approveRecoveryReversalChargebackAction;
    private final RaiseChargebackRecoveryReversalAccountingEventAction raiseChargebackRecoveryReversalAccountingEventAction;
    private final ResetChargebackAction resetChargebackAction;
    private final FullRefundCreationAction fullRefundCreationAction;
    private final AcceptPartialDisputeAction acceptPartialDisputeAction;
    private final RefundInitiationAction refundInitiationAction;
    private final RefundCreationAction refundCreationAction;
    private final FraudCheckDisputeAction fraudCheckUpdateDisputeAction;

    @SuppressWarnings("java:S107")
    public NetBankingChargebackStateMachine(
        final TransitionLockCommand transitionLockCommand,
        final DisputeErrorHandlingInterceptor disputeErrorHandlingInterceptor,
        final NetBankingChargebackCreateEntryAction netbankingChargebackCreateEntryAction,
        BlockRefundAction blockRefundAction, UpdateDisputeStateAction updateDisputeStateAction,
        UnblockRefundAction unblockRefundAction,
        MandatoryCommentUpdateDisputeStateAction mandatoryCommentUpdateDisputeStateAction,
        OptionalCommentUpdateDisputeStateAction optionalCommentUpdateDisputeStateAction,
        ApproveRecoverChargebackAction approveRecoverChargebackAction,
        RaiseChargebackRecoveryAccountingEventAction raiseChargebackRecoveryAccountingEventAction,
        ApproveRecoveryReversalChargebackAction approveRecoveryReversalChargebackAction,
        RaiseChargebackRecoveryReversalAccountingEventAction raiseChargebackRecoveryReversalAccountingEventAction,
        ResetChargebackAction resetChargebackAction,
        AcceptPartialDisputeAction acceptPartialDisputeAction,
        FullRefundCreationAction fullRefundCreationAction,
        RefundInitiationAction refundInitiationAction, RefundCreationAction refundCreationAction,
        FraudCheckDisputeAction fraudCheckUpdateDisputeAction) {
        super(transitionLockCommand, disputeErrorHandlingInterceptor);
        this.netbankingChargebackCreateEntryAction = netbankingChargebackCreateEntryAction;
        this.blockRefundAction = blockRefundAction;
        this.updateDisputeStateAction = updateDisputeStateAction;
        this.unblockRefundAction = unblockRefundAction;
        this.mandatoryCommentUpdateDisputeStateAction = mandatoryCommentUpdateDisputeStateAction;
        this.optionalCommentUpdateDisputeStateAction = optionalCommentUpdateDisputeStateAction;
        this.approveRecoverChargebackAction = approveRecoverChargebackAction;
        this.raiseChargebackRecoveryAccountingEventAction = raiseChargebackRecoveryAccountingEventAction;
        this.approveRecoveryReversalChargebackAction = approveRecoveryReversalChargebackAction;
        this.raiseChargebackRecoveryReversalAccountingEventAction = raiseChargebackRecoveryReversalAccountingEventAction;
        this.resetChargebackAction = resetChargebackAction;
        this.fullRefundCreationAction = fullRefundCreationAction;
        this.acceptPartialDisputeAction = acceptPartialDisputeAction;
        this.refundInitiationAction = refundInitiationAction;
        this.refundCreationAction = refundCreationAction;
        this.fraudCheckUpdateDisputeAction = fraudCheckUpdateDisputeAction;
    }

    @Override
    @SneakyThrows
    protected void configure(
        final StateMachineConfigurationConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> config) {
        config
            .withConfiguration()
            .autoStartup(false);

    }

    @Override
    @SneakyThrows
    protected void configure(
        final StateMachineStateConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> states) {
        states
            .withStates()
            .initial(DisputeWorkflowState.RECEIVED)
            .states(EnumSet.allOf(DisputeWorkflowState.class))
            .end(DisputeWorkflowState.CHARGEBACK_ABSORB_REVERSED)
            .end(DisputeWorkflowState.CHARGEBACK_CANCELLED)
            .end(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT_ACCEPTED);
    }

    @Override
    @SneakyThrows
    protected void configure(
        final StateMachineTransitionConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> transitions) {

        transitions
            .withExternal()
            .source(DisputeWorkflowState.RECEIVED)
            .target(DisputeWorkflowState.RECEIVED)
            .event(DisputeWorkflowEvent.CREATE_ENTRY)
            .action(netbankingChargebackCreateEntryAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECEIVED)
            .target(DisputeWorkflowState.REPRESENTMENT_REQUIRED)
            .event(DisputeWorkflowEvent.REQUEST_REPRESENTMENT)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REPRESENTMENT_REQUIRED)
            .target(DisputeWorkflowState.REPRESENTMENT_COMPLETED)
            .event(DisputeWorkflowEvent.COMPLETE_REPRESENTMENT)
            .action(unblockRefundAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REPRESENTMENT_COMPLETED)
            .target(DisputeWorkflowState.CHARGEBACK_CANCELLED)
            .event(DisputeWorkflowEvent.RESET_CHARGEBACK)
            .action(resetChargebackAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECEIVED)
            .target(DisputeWorkflowState.REFUND_BLOCKED)
            .event(DisputeWorkflowEvent.BLOCK_REFUND)
            .action(blockRefundAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REFUND_BLOCKED)
            .target(DisputeWorkflowState.FULFILMENT_DOCUMENTS_RECEIVED)
            .event(DisputeWorkflowEvent.RECEIVE_FULFILMENT_DOCUMENTS)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.FULFILMENT_DOCUMENTS_RECEIVED)
            .target(DisputeWorkflowState.REPRESENTMENT_COMPLETED)
            .event(DisputeWorkflowEvent.COMPLETE_REPRESENTMENT)
            .action(unblockRefundAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REFUND_BLOCKED)
            .target(DisputeWorkflowState.FRAUD_REJECTED)
            .event(DisputeWorkflowEvent.FRAUD_REJECT)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.FRAUD_REJECTED)
            .target(DisputeWorkflowState.REPRESENTMENT_COMPLETED)
            .event(DisputeWorkflowEvent.FRAUD_REJECTED_TO_REPRESENTMENT) // Manual action
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REFUND_BLOCKED)
            .target(DisputeWorkflowState.MERCHANT_NOT_RESPONDED_WITHIN_TTL)
            .event(DisputeWorkflowEvent.NO_RESPONSE_FROM_MERCHANT_WITHIN_TTL)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_NOT_RESPONDED_WITHIN_TTL)
            .target(DisputeWorkflowState.REPRESENTMENT_COMPLETED)
            .event(DisputeWorkflowEvent.COMPLETE_REPRESENTMENT)
            .action(unblockRefundAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_NOT_RESPONDED_WITHIN_TTL)
            .target(DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED)
            .event(DisputeWorkflowEvent.RECEIVE_PARTIAL_FULFILMENT_DOCUMENTS)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_NOT_RESPONDED_WITHIN_TTL)
            .target(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
            .event(DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK)
            .action(fraudCheckUpdateDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REFUND_BLOCKED)
            .target(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
            .event(DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK)
            .action(fraudCheckUpdateDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
            .target(DisputeWorkflowState.CB_REFUND_CREATED)
            .event(DisputeWorkflowEvent.CREATE_CHARGEBACK_REFUND)
            .action(fullRefundCreationAction)//unblock refund & set accepted amount

            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
            .target(DisputeWorkflowState.SUSPECTED_FRAUD)
            .event(DisputeWorkflowEvent.SUSPECTED_FRAUD)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.SUSPECTED_FRAUD)
            .target(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
            .event(DisputeWorkflowEvent.SUSPECTED_FRAUD_TO_ACCEPTANCE)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.SUSPECTED_FRAUD)
            .target(DisputeWorkflowState.FRAUD_REJECTED)
            .event(DisputeWorkflowEvent.FRAUD_REJECT)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
            .target(DisputeWorkflowState.FRAUD_REJECTED)
            .event(DisputeWorkflowEvent.FRAUD_REJECT)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.CB_REFUND_CREATED)
            .target(DisputeWorkflowState.CB_REFUND_INITIATED)
            .event(DisputeWorkflowEvent.INITIATE_CHARGEBACK_REFUND)
            .action(refundInitiationAction) // make call to RO & state change based on inline success

            .and()
            .withExternal()
            .source(DisputeWorkflowState.CB_REFUND_INITIATED)
            .target(DisputeWorkflowState.CB_REFUND_ACCEPTED)
            .event(DisputeWorkflowEvent.CHARGEBACK_REFUND_ACCEPTED)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.CB_REFUND_INITIATED)
            .target(DisputeWorkflowState.CB_REFUND_FAILED)
            .event(DisputeWorkflowEvent.INITIATED_TO_FAILED)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REFUND_BLOCKED)
            .target(DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED)
            .event(DisputeWorkflowEvent.RECEIVE_PARTIAL_FULFILMENT_DOCUMENTS)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED)
            .target(DisputeWorkflowState.SUSPECTED_FRAUD)
            .event(DisputeWorkflowEvent.SUSPECTED_FRAUD)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED)
            .target(DisputeWorkflowState.FRAUD_REJECTED)
            .event(DisputeWorkflowEvent.FRAUD_REJECT)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED)
            .target(DisputeWorkflowState.PARTIAL_REPRESENTMENT_COMPLETED)
            .event(DisputeWorkflowEvent.COMPLETE_PARTIAL_REPRESENTMENT)
            .action(acceptPartialDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.PARTIAL_REPRESENTMENT_COMPLETED)
            .target(DisputeWorkflowState.CB_REFUND_CREATED)
            .event(DisputeWorkflowEvent.CREATE_CHARGEBACK_REFUND)
            .action(refundCreationAction) // ops to enter refund amount manually

            .and()
            .withExternal()
            .source(DisputeWorkflowState.CB_REFUND_ACCEPTED)
            .target(DisputeWorkflowState.ABSORB_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_ABSORB_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.ABSORB_CHARGEBACK_REQUESTED)
            .target(DisputeWorkflowState.ABSORB_CHARGEBACK_APPROVED)
            .event(DisputeWorkflowEvent.APPROVE_ABSORB_CHARGEBACK)
            .action(optionalCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.ABSORB_CHARGEBACK_REQUESTED)
            .target(DisputeWorkflowState.ABSORB_CHARGEBACK_REJECTED)
            .event(DisputeWorkflowEvent.REJECT_ABSORB_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.ABSORB_CHARGEBACK_REJECTED)
            .target(DisputeWorkflowState.ABSORB_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_ABSORB_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECOVER_CHARGEBACK_REJECTED)
            .target(DisputeWorkflowState.ABSORB_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_ABSORB_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.ABSORB_CHARGEBACK_APPROVED)
            .target(DisputeWorkflowState.CHARGEBACK_ABSORBED)
            .event(DisputeWorkflowEvent.ABSORB_CHARGEBACK)
            .action(optionalCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.CHARGEBACK_ABSORBED)
            .target(DisputeWorkflowState.CHARGEBACK_ABSORB_REVERSED)
            .event(DisputeWorkflowEvent.REVERSE_ABSORB_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.CB_REFUND_ACCEPTED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_RECOVER_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECOVER_CHARGEBACK_REQUESTED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_APPROVED)
            .event(DisputeWorkflowEvent.APPROVE_RECOVER_CHARGEBACK)
            .action(approveRecoverChargebackAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECOVER_CHARGEBACK_REJECTED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_RECOVER_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.ABSORB_CHARGEBACK_REJECTED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_RECOVER_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECOVER_CHARGEBACK_APPROVED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_RAISED)
            .event(DisputeWorkflowEvent.RAISE_RECOVER_CHARGEBACK_EVENT)
            .action(raiseChargebackRecoveryAccountingEventAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_RAISED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_ACCEPTED)
            .event(DisputeWorkflowEvent.ACCEPT_RECOVER_CHARGEBACK_EVENT)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECOVER_CHARGEBACK_REQUESTED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_REJECTED)
            .event(DisputeWorkflowEvent.REJECT_RECOVER_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_ACCEPTED)
            .target(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_REVERSAL_OF_RECOVERED_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_REQUESTED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_ACCEPTED)
            .event(DisputeWorkflowEvent.REJECT_REVERSAL_OF_RECOVERED_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_REQUESTED)
            .target(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_APPROVED)
            .event(DisputeWorkflowEvent.APPROVE_REVERSAL_OF_RECOVERED_CHARGEBACK)
            .action(approveRecoveryReversalChargebackAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_APPROVED)
            .target(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT_RAISED)
            .event(DisputeWorkflowEvent.RAISE_REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT)
            .action(raiseChargebackRecoveryReversalAccountingEventAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT_RAISED)
            .target(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT_ACCEPTED)
            .event(DisputeWorkflowEvent.ACCEPT_REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT)
            .action(updateDisputeStateAction);

    }

    @Override
    public abstract DisputeStateMachineRegistryKey getRegistryKey();
}
