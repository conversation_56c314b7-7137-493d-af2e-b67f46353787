package com.phonepe.merchant.platform.stratos.server.core.services.impls;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.WalletTransactionDetails;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.clients.PaymentsTxnlClient;
import com.phonepe.merchant.platform.stratos.server.core.clients.WalletTxnlClient;
import com.phonepe.merchant.platform.stratos.server.core.services.TransactionService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.TransformationUtils;
import com.phonepe.models.payments.pay.view.ProcessingRail;
import java.util.Date;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class WalletTransactionService implements TransactionService<WalletTransactionDetails> {

    private final PaymentsTxnlClient paymentsTxnlClient;
    private final WalletTxnlClient walletTxnlClient;

    @Override
    public WalletTransactionDetails getTransactionDetails(String transactionId) {

        final var transactionDetail = paymentsTxnlClient.getTransactionDetails(transactionId);

        if (!transactionDetail.isSuccess()) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_PAYMENTS_ID,
                Map.of(Constants.MESSAGE, "Invalid payments Id " + transactionId,
                    Constants.SERVICE_NAME, "PaymentsService"));
        }

        log.info("Transaction detail from payment is {}", transactionDetail.getData());
        return TransformationUtils.toWalletTransactionDetails(transactionDetail.getData(), ProcessingRail.UPI);
    }

    public WalletTransactionDetails getTransactionDetailFromUpiId(final String upiTransactionId,
        final String utr,
        final Date txnDate){

        final var response = walletTxnlClient.getPaymentsTransactionDetailFromUpiTxnId(upiTransactionId, utr, txnDate);

        if (!response.isSuccess()) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_UPI_ID, Map.of(Constants.MESSAGE,
                "Invalid upi txn Id " + upiTransactionId));
        }

        log.info("Transaction detail from wallet is {}", response.getData());
        return TransformationUtils.toWalletTransactionDetails(response.getData(), ProcessingRail.PPI_WALLET);
    }

}
