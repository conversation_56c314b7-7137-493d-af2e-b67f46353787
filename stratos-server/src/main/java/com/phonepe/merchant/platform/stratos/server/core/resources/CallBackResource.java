package com.phonepe.merchant.platform.stratos.server.core.resources;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.google.inject.Inject;
import com.phonepe.merchant.platform.stratos.server.core.models.CallbackType;
import com.phonepe.merchant.platform.stratos.server.core.utils.AuthorizationUtils;
import com.phonepe.merchant.platform.stratos.server.core.visitors.CallbackTypeVisitorImpl;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.olympus.im.models.security.ServiceUserPrincipal;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import io.dropwizard.auth.Auth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.io.InputStream;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Path("/v1/callback")
@Tag(name = "CallBack related API's")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class CallBackResource {

    private final CallbackTypeVisitorImpl callbackTypeVisitor;
    private final OlympusIMClient olympusIMClient;

    @POST
    @ExceptionMetered
    @Path("/{callbackType}")
    @Operation(summary = "Update the status of Refund")
    public Response processCallback(
            @Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
            @NotNull InputStream requestBody,
            @NotNull @PathParam("callbackType") String callbackTypeString) {
        final UserAuthDetails userAuthDetails = serviceUserPrincipal.getUserAuthDetails();
        AuthorizationUtils.authorizeCallback(olympusIMClient, userAuthDetails,callbackTypeString);
        CallbackType callbackType = CallbackType.getCallbackType(callbackTypeString);
        //sanity check
        if(callbackType==null)
            return Response.status(Response.Status.BAD_REQUEST)
                .entity("Unsupported type: " + callbackTypeString)
                .build();
        return callbackType.accept(callbackTypeVisitor, requestBody);
    }
}
