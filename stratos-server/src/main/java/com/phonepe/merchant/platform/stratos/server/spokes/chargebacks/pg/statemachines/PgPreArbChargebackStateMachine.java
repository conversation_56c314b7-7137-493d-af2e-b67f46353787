package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.pg.statemachines;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.TransitionLockCommand;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.registries.StateMachineListenerRegistry;
import com.phonepe.merchant.platform.stratos.server.core.registries.keys.DisputeStateMachineRegistryKey;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.comments.impls.MandatoryCommentUpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.comments.impls.OptionalCommentUpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.AcceptDebitAndDebitDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.AcceptDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.AcceptPartialDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.DebitDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.FraudCheckDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.ResetChargebackAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.UpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.interceptors.impls.DisputeErrorHandlingInterceptor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.ApproveRecoverChargebackAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.ApproveRecoveryReversalChargebackAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.BlockRefundAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.RaiseChargebackRecoveryAccountingEventAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.RaiseChargebackRecoveryReversalAccountingEventAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.UnblockRefundAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.pg.statemachines.actions.PgChargebackCreateEntryAction;

@Singleton
public class PgPreArbChargebackStateMachine extends PgChargebackStateMachine {

    @Inject
    public PgPreArbChargebackStateMachine(final TransitionLockCommand transitionLockCommand,
        final DisputeErrorHandlingInterceptor disputeErrorHandlingInterceptor,
        final PgChargebackCreateEntryAction createEntryAction,
        final BlockRefundAction blockRefundAction,
        final UpdateDisputeStateAction updateDisputeStateAction,
        final AcceptDisputeAction acceptDisputeAction,
        final AcceptPartialDisputeAction acceptPartialDisputeAction,
        final DebitDisputeAction debitDisputeAction,
        final MandatoryCommentUpdateDisputeStateAction mandatoryCommentUpdateDisputeStateAction,
        final OptionalCommentUpdateDisputeStateAction optionalCommentUpdateDisputeStateAction,
        final ApproveRecoverChargebackAction approveRecoverChargebackAction,
        final RaiseChargebackRecoveryAccountingEventAction raiseChargebackRecoveryAccountingEventAction,
        final ApproveRecoveryReversalChargebackAction approveRecoveryReversalChargebackAction,
        final RaiseChargebackRecoveryReversalAccountingEventAction raiseChargebackRecoveryReversalAccountingEventAction,
        final UnblockRefundAction unblockRefundAction,
        final ResetChargebackAction resetChargebackAction,
        final FraudCheckDisputeAction fraudCheckUpdateDisputeAction,
        final AcceptDebitAndDebitDisputeAction acceptDebitAndDebitDisputeAction,
        final StateMachineListenerRegistry<DisputeWorkflowState, DisputeWorkflowEvent> stateMachineListenerRegistry) {

        super(transitionLockCommand, disputeErrorHandlingInterceptor, createEntryAction,
            blockRefundAction, updateDisputeStateAction,
            acceptDisputeAction,
            acceptPartialDisputeAction, debitDisputeAction,
            mandatoryCommentUpdateDisputeStateAction,
            optionalCommentUpdateDisputeStateAction, approveRecoverChargebackAction,
            raiseChargebackRecoveryAccountingEventAction,
            approveRecoveryReversalChargebackAction,
            raiseChargebackRecoveryReversalAccountingEventAction,
            unblockRefundAction,
            resetChargebackAction,
            fraudCheckUpdateDisputeAction,
            acceptDebitAndDebitDisputeAction,
            stateMachineListenerRegistry
        );
    }

    @Override
    public DisputeStateMachineRegistryKey getRegistryKey() {
        return DisputeStateMachineRegistryKey.builder()
            .disputeType(DisputeType.PG_CHARGEBACK)
            .disputeStage(DisputeStage.PRE_ARBITRATION)
            .disputeWorkflowVersion(DisputeWorkflowVersion.V1)
            .build();
    }
}
