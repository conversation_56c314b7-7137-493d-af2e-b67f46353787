package com.phonepe.merchant.platform.stratos.server.core.resources.housekeeping;

import static com.phonepe.olympus.im.models.core.CoreConstants.BEARER;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.google.inject.Inject;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.responses.metadata.DisputeMetadataResponse;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.FileRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.*;
import com.phonepe.merchant.platform.stratos.server.core.resources.housekeeping.requests.CorrectDisputeStateRequest;
import com.phonepe.merchant.platform.stratos.server.core.resources.housekeeping.requests.CorrectRowStateRequest;
import com.phonepe.merchant.platform.stratos.server.core.resources.housekeeping.requests.DisputeWorkflowUpdateRequest;
import com.phonepe.merchant.platform.stratos.server.core.resources.housekeeping.response.RowResponse;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.EdcService;
import com.phonepe.merchant.platform.stratos.server.core.services.PaymentsService;
import com.phonepe.merchant.platform.stratos.server.core.services.TstoreService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.FeedUtils;
import com.phonepe.merchant.platform.stratos.server.core.visitors.disputeworkflow.BlockRefundDisputeWorkflowVisitor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.FraUtils;
import com.phonepe.olympus.im.models.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import io.dropwizard.auth.Auth;
import com.phonepe.merchant.platform.stratos.server.core.services.impls.RowServiceImpl;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Path("/v1/housekeeping")
@Tag(name = "Housekeeping Related APIs")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
@SecurityScheme(name = BEARER, type = SecuritySchemeType.APIKEY, in = SecuritySchemeIn.HEADER, paramName = "Authorization")
@SecurityRequirement(name = BEARER)
public class HousekeepingResource {

    private final PaymentsService paymentsService;
    private final DisputeService disputeService;
    private final DisputeWorkflowRepository disputeWorkflowRepository;
    private final RowServiceImpl rowServiceImp;
    private final FileRepository fileRepository;
    private final EdcService edcService;
    private final TstoreService tstoreService;


    @PUT
    @RolesAllowed("unblock-reversal")
    @ExceptionMetered
    @Path("/unblock/reversal")
    @Operation(summary = "Unblock refunds for the given transaction Ids")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public void unblockReversalsForTransactionIds(
        @Valid @NotEmpty final List<String> transactionIds) {
        transactionIds.forEach(transactionId -> {
            paymentsService.unblockReversals(transactionId);
            log.info("Refund Unblocked for {}", transactionId);
        });
    }

    @PUT
    @RolesAllowed("admin-apis")
    @ExceptionMetered
    @Path("/correct/dispute/state")
    @Operation(summary = "Correct dispute state for the given transaction Id. "
        + "[WARNING] Use judiciously as state transition may have some action "
        + "associated which may be missed")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public void correctDisputeState(
        @Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
        @Valid @NotNull final CorrectDisputeStateRequest correctDisputeStateRequest) {
        disputeWorkflowRepository.updateState(
            correctDisputeStateRequest.getTransactionId(),
            correctDisputeStateRequest.getDisputeWorkflowId(),
            correctDisputeStateRequest.getToState(),
            correctDisputeStateRequest.getToEvent(),
            serviceUserPrincipal.getUserAuthDetails().getUserDetails().getUserId(), UserType.USER);
    }

    @PUT
    @RolesAllowed("admin-apis")
    @ExceptionMetered
    @Path("/correct/row/state")
    @Operation(summary = "Correct Row state for the given sourceId and rowId. "
        + "[WARNING] Use judiciously as state transition may have some action "
        + "associated which may be missed")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public void correctRowState(
        @Valid @NotNull final CorrectRowStateRequest correctRowStateRequest) {
        rowServiceImp.updateState(correctRowStateRequest);
    }

    @PUT
    @RolesAllowed("admin-apis")
    @ExceptionMetered
    @Path("/file/processed/{fileId}")
    @Operation(summary = "Marks File with Given FileId as Processed")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public void markFileAsProcessed(
        @Valid @NotNull @PathParam("fileId") final String fileId) {

        fileRepository.updateFile(fileId, fileTemp -> {
            fileTemp.setFileState(FileState.PROCESSED);
            return fileTemp;
        });
    }

    @GET
    @ExceptionMetered
    @Path("details/row/{sourceId}")
    @RolesAllowed("admin-apis")
    @Operation(summary = "Get Row details")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public RowResponse getRowDetails(
        @Valid @NotNull @PathParam("sourceId") final String sourceId
        ) {
        return rowServiceImp.getRow(sourceId);
    }

    @PUT
    @ExceptionMetered
    @Path("/block/reversals/{disputeType}/{disputeStage}")
    @RolesAllowed("admin-apis")
    @Operation(summary = "Block reversals")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response blockReversals(@Valid @NotEmpty final List<String> transactionIds,@PathParam("disputeType") final DisputeType disputeType,
        @PathParam("disputeStage") final DisputeStage disputeStage) {

        return disputeType.accept(BlockRefundDisputeWorkflowVisitor.builder()
            .disputeStage(disputeStage)
            .disputeType(disputeType)
            .edcService(edcService)
            .paymentsService(paymentsService)
            .transactionIds(transactionIds)
            .disputeWorkflowRepository(disputeWorkflowRepository)
            .build());
    }
    @PUT
    @ExceptionMetered
    @RolesAllowed("admin-apis")
    @Path("/disputeWorkflowId/updateRaisedAt")
    @Operation(summary = "API to update raised at attribute of disputeWorkflows")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response updateDisputeRaisedAt(@Valid DisputeWorkflowUpdateRequest updateRequest) {
        return disputeService.updateDisputeWorkflows(updateRequest);
    }

    @GET
    @ExceptionMetered
    @RolesAllowed("admin-apis")
    @Path("/{disputeWorkflowId}/raiseTstoreEntity")
    @Operation(summary = "Raise Tstore Entity for disptueWorkflowId")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response raiseTstoreEntity(@PathParam("disputeWorkflowId") final String disputeWorkflowId) {
        DisputeWorkflow disputeWorkflow = disputeService.getDisputeWorkflow(disputeWorkflowId);
        String globalPaymentId = paymentsService.getGlobalPaymentId(disputeWorkflow.getTransactionReferenceId());

        tstoreService.createMerchantFeed(
                disputeWorkflowId, FeedUtils.toDisputeEntityFeed(disputeWorkflow),
                disputeWorkflow.getDispute().getMerchantId(),
                globalPaymentId,
                FraUtils.toEpochSecond(disputeWorkflow.getCreatedAt()),
                FraUtils.toEpochSecond(disputeWorkflow.getUpdatedAt()));
        return Response.ok().build();
    }

}
