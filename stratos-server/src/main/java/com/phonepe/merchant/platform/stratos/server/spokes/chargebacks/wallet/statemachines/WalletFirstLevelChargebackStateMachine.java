package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.statemachines;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.TransitionLockCommand;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.registries.StateMachineListenerRegistry;
import com.phonepe.merchant.platform.stratos.server.core.registries.keys.DisputeStateMachineRegistryKey;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.RejectedDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.UpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.interceptors.impls.DisputeErrorHandlingInterceptor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.actions.UpdateAcceptedStateAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.actions.UpdateWalletMetadataOnExternalCompletionAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.actions.WalletMerchantAcceptedChargebackAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.actions.WalletRefundCreationAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.actions.WalletRefundInitiationAction;
import java.util.Set;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;

@Slf4j
@Singleton
public class WalletFirstLevelChargebackStateMachine extends WalletChargebackStateMachine {

    @Inject
    protected WalletFirstLevelChargebackStateMachine(
        TransitionLockCommand transitionLockCommand,
        DisputeErrorHandlingInterceptor disputeErrorHandlingInterceptor,
        final UpdateDisputeStateAction updateDisputeStateAction,
        final WalletRefundInitiationAction refundInitiationAction,
        final WalletRefundCreationAction fullRefundCreationAction,
        final WalletMerchantAcceptedChargebackAction walletMerchantAcceptedChargebackAction,
        final RejectedDisputeAction rejectedDisputeAction,
        final UpdateAcceptedStateAction updateAcceptedStateAction,
        final UpdateWalletMetadataOnExternalCompletionAction updateWalletMetadataOnExternalCompletionAction,
        final StateMachineListenerRegistry<DisputeWorkflowState, DisputeWorkflowEvent> stateMachineListenerRegistry
        ) {
        super(transitionLockCommand, disputeErrorHandlingInterceptor,
            updateDisputeStateAction, refundInitiationAction, fullRefundCreationAction,
            walletMerchantAcceptedChargebackAction, rejectedDisputeAction, updateAcceptedStateAction,
            updateWalletMetadataOnExternalCompletionAction, stateMachineListenerRegistry);
    }

    @Override
    protected Set<DisputeWorkflowState> endStates() {
        return Set.of();
    }

    @Override
    public DisputeStateMachineRegistryKey getRegistryKey() {
        return DisputeStateMachineRegistryKey.builder()
            .disputeType(DisputeType.WALLET_CHARGEBACK)
            .disputeStage(DisputeStage.FIRST_LEVEL)
            .disputeWorkflowVersion(DisputeWorkflowVersion.V1)
            .build();
    }

    @Override
    @SneakyThrows
    protected void configure(
        final StateMachineTransitionConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> transitions) {
        baseTransitions(transitions);
    }
}
