package com.phonepe.merchant.platform.stratos.server.core.filter;

import com.phonepe.merchant.platform.stratos.server.core.configs.ApiFilterConfig;
import com.phonepe.merchant.platform.stratos.server.core.models.ApiState;
import com.phonepe.platform.filters.FilterEvaluator;
import java.util.Arrays;
import java.util.Map;
import java.util.Set;

public class ApiFilterEvaluator implements FilterEvaluator {

    private final ApiFilterConfig apiFilterConfig;

    public ApiFilterEvaluator(ApiFilterConfig apiFilterConfig) {
        this.apiFilterConfig = apiFilterConfig;
    }

    @Override
    public boolean shouldFilterUriTemplate(String s, String s1, Map<String, String> map) {
        return false;
    }

    @Override
    public boolean shouldFilterUrlPath(String s, String s1, Map<String, String> map) {
        return false;
    }

    @Override
    public boolean shouldFilterApiTags(final String requestMethod, final Set<String> tags,
                                       final Map<String, String> clientDetails) {
        return Arrays.stream(ApiState.values())
                .anyMatch(apiState -> tags.contains(apiState.name()) &&
                        apiState.ordinal() > apiFilterConfig.getApiState().ordinal());
    }

    @Override
    public boolean shouldFilterUsers(String s, String s1, Map<String, String> map) {
        return false;
    }

    @Override
    public boolean shouldFilterPriority(String s) {
        return false;
    }
}
