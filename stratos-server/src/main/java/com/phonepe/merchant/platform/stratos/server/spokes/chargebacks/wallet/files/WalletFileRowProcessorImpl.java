package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.files;

import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_NPCI_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK;

import com.google.inject.Inject;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.EmptyTransitionContext;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.PartialAcceptanceTransitionContext;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.configs.FileConfig;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.events.type.DisputeSignalEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.DisputeSignalEvent.SignalType;
import com.phonepe.merchant.platform.stratos.server.core.handlebars.HandleBarsService;
import com.phonepe.merchant.platform.stratos.server.core.helpers.id.IdHelper;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowStateNonMandatoryVisitor;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.models.SourceType;
import com.phonepe.merchant.platform.stratos.server.core.models.UserType;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.MerchantMandateService;
import com.phonepe.merchant.platform.stratos.server.core.services.PaymentsService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeWorkflowUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.StorageUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.StringUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.files.BaseFileRowProcessor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.files.models.FileRowMeta;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.FoxtrotEventUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.ValidationUtils;
import com.phonepe.models.payments.pay.TransactionDetail;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class WalletFileRowProcessorImpl extends BaseFileRowProcessor<TransactionDetail> {

    private static final Logger log = LoggerFactory.getLogger(WalletFileRowProcessorImpl.class);
    private final PaymentsService paymentsService;
    private final DisputeService disputeService;
    private final IdHelper idHelper;
    private final EventIngester eventIngester;

    @Inject
    public WalletFileRowProcessorImpl(HandleBarsService handleBarsService,
        DisputeService disputeService, MerchantMandateService merchantMandateService,
        PaymentsService paymentsService, final IdHelper idHelper,
        EventIngester eventIngester,
        Map<DisputeType, DisputeWorkflowVersion> disputeWorkflowVersionMap) {
        super(handleBarsService, disputeService, merchantMandateService, disputeWorkflowVersionMap);
        this.paymentsService = paymentsService;
        this.disputeService = disputeService;
        this.idHelper = idHelper;
        this.eventIngester = eventIngester;
    }

    private DisputeWorkflowState getDisputeWorkflowState(
        final FinancialDisputeWorkflow disputeWorkflow, DisputeType disputeType,
        String transactionId) {
        final var storedDisputeWorkflow = disputeService
            .getDisputeWorkflow(transactionId,
                disputeType,
                disputeWorkflow.getDisputeStage());

        if ((disputeWorkflow.getDisputedAmount() > 0
            && (disputeWorkflow.getDisputedAmount()
            < storedDisputeWorkflow.getDisputedAmount())) && disputeWorkflow.getCurrentState()
            .equals(DisputeWorkflowState.NPCI_REPRESENTMENT_COMPLETED)) {
            return DisputeWorkflowState.PARTIAL_REJECTED_CHARGEBACK;
        }

        return disputeWorkflow.getCurrentState();
    }

    @Override
    public DisputeWorkflow enrichAndGetDisputeWorkflow(final TransactionDetail transactionDetails,
        final FinancialDisputeWorkflow disputeWorkflow, final String fileId,
        final DisputeType disputeType,
        final FileConfig fileConfig, final String disputeId) {

        final var sentPayment = transactionDetails.getSentPayment();

        final var ttlInDays = fileConfig.getTTLForDisputeStage(disputeWorkflow.getDisputeStage());

        final DisputeWorkflowState disputeWorkflowState = getDisputeWorkflowState(disputeWorkflow, disputeType,
            sentPayment.getTransactionId());

        return FinancialDisputeWorkflow.builder()
            .key(StorageUtils.primaryKey())
            .disputeType(disputeType)
            .disputeSourceId(fileId)
            .disputeSourceType(SourceType.FILE)
            .disputeWorkflowId(idHelper.disputeWorkflowId(sentPayment.getTransactionId()))
            .disputeWorkflowVersion(DisputeWorkflowVersion.V1)
            .transactionReferenceId(sentPayment.getTransactionId())
            .respondBy(disputeWorkflow.getRaisedAt().plusDays(ttlInDays))
            .userType(UserType.SYSTEM)
            .gandalfUserId(Constants.STRATOS_SYSTEM_USER_OLYMPUS.getUserDetails().getUserId())
            .disputeStage(disputeWorkflow.getDisputeStage())
            .currentState(disputeWorkflowState)
            .currentEvent(disputeWorkflow.getCurrentEvent())
            .raisedAt(disputeWorkflow.getRaisedAt())
            .disputedAmount(disputeWorkflow.getDisputedAmount())
            .penaltyAmount(disputeWorkflow.getPenaltyAmount())
            .acceptedAmount(disputeWorkflow.getDisputedAmount())
            .disputeId(disputeId).build();

    }

    @SneakyThrows
    private Dispute enrichDisputeDetails(final TransactionDetail transactionDetails,
        final Dispute disputeFilePojo, final DisputeType disputeType) {

        final var sentPayment = transactionDetails.getSentPayment();

        final String merchantId = transactionDetails.getSentPayment().getTo().get(0).getVpa();

        return Dispute.builder().key(StorageUtils.primaryKey())
            .disputeType(disputeType)
            .disputeId(idHelper.disputeId(sentPayment.getTransactionId()))
            .transactionReferenceId(sentPayment.getTransactionId())
            .disputeReferenceId(StringUtils.join(disputeFilePojo.getRrn(),
                disputeFilePojo.getInstrumentTransactionId()))
            .merchantId(merchantId)
            .currentDisputeStage(disputeFilePojo.getCurrentDisputeStage())
            .instrumentTransactionId(disputeFilePojo.getInstrumentTransactionId())
            .transactionAmount(disputeFilePojo.getTransactionAmount()).rrn(disputeFilePojo.getRrn())
            .disputeIssuer(disputeFilePojo.getDisputeIssuer())
            .disputeCategory(disputeFilePojo.getDisputeCategory()).build();
    }

    private String getUtr(final WalletFileRowMeta walletFileRowMeta) {
        return walletFileRowMeta.getUtr().replace("'", "");
    }

    private LocalDateTime getTxnDate(final WalletFileRowMeta walletFileRowMeta) {
        return Optional.ofNullable(walletFileRowMeta.getTransactionDate())
            .orElse(LocalDateTime.now());
    }

    @Override
    public TransactionDetail getTransactionDetails(String instrumentTransactionId,
        FileRowMeta fileRowMeta) {
        return paymentsService.transactionDetailFromUpiId(instrumentTransactionId,
            getUtr((WalletFileRowMeta) fileRowMeta), getTxnDate((WalletFileRowMeta) fileRowMeta));
    }

    @Override
    public Dispute getDispute(DisputeWorkflow disputeWorkflow, TransactionDetail transactionDetail,
        Dispute disputeFilePojo, DisputeType disputeType, String instrumentTransactionId) {
        return enrichDisputeDetails(transactionDetail, disputeFilePojo, disputeType);
    }

    @Override
    public Boolean triggerEvent(final DisputeWorkflow disputeWorkflow, final Dispute dispute,
        final TransactionDetail transactionDetail) {

        final var storedDisputeWorkflow = disputeService.getDisputeWorkflow(
            dispute.getTransactionReferenceId(), dispute.getDisputeType(),
            dispute.getCurrentDisputeStage());

        return disputeWorkflow.getCurrentState()
            .accept(new DisputeWorkflowStateNonMandatoryVisitor<>() {
                @Override
                public Boolean visitNpciAckChargeback() {
                    eventIngester.generateEvent(FoxtrotEventUtils.getSignalEvent(storedDisputeWorkflow,
                        DisputeWorkflowEvent.NPCI_ACK_CHARGEBACK,
                        SignalType.CREDIT));
                    if (!ValidationUtils.validateNpciDisputeAmount(disputeWorkflow, storedDisputeWorkflow)) {
                        eventIngester.generateEvent(FoxtrotEventUtils.toDisputeActionFailedEvent(disputeWorkflow, disputeWorkflow.getCurrentState(),
                                "expected disputeAmount " + storedDisputeWorkflow.getDisputedAmount() + " Npci disputed amount is "
                                    + disputeWorkflow.getDisputedAmount() + "for transactionId "
                                    + disputeWorkflow.getTransactionReferenceId(), ""));
                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_DISPUTE_AMOUNT, Map.of(Constants.MESSAGE, "expected disputeAmount "
                                    + storedDisputeWorkflow.getDisputedAmount() + " Npci disputed amount is "
                                    + disputeWorkflow.getDisputedAmount() + "for transactionId "
                                    + disputeWorkflow.getTransactionReferenceId()));
                    }
                    disputeService.triggerNpciAck(dispute,
                        storedDisputeWorkflow);
                    return true;
                }

                @Override
                public Boolean visitMerchantAcceptedChargeback() {
                    eventIngester.generateEvent(FoxtrotEventUtils.getSignalEvent(storedDisputeWorkflow,
                        DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK,
                        SignalType.CREDIT));
                    triggerMerchantAcceptedEvent(disputeWorkflow, dispute, storedDisputeWorkflow);
                    return true;
                }

                @Override
                public Boolean visitNPCIRepresentmentCompleted() {
                    eventIngester.generateEvent(FoxtrotEventUtils.getSignalEvent(storedDisputeWorkflow,
                        DisputeWorkflowEvent.COMPLETE_NPCI_REPRESENTMENT,
                        DisputeSignalEvent.SignalType.DEBIT));
                    triggerNpciRepresentmentEvent(dispute, storedDisputeWorkflow);
                    return true;
                }

                @Override
                public Boolean visitPartialRejectedChargeback() {

                    FinancialDisputeWorkflow financialDisputeWorkflow = DisputeWorkflowUtils.getFinancialDisputeWorkflow(
                        disputeWorkflow);
                    log.info(
                        "Partial chargeback for disputeID: {}, transactionID: {}, rejected amount: {}, disputed amount{}",
                        storedDisputeWorkflow.getDisputeWorkflowId(),
                        storedDisputeWorkflow.getTransactionReferenceId(), financialDisputeWorkflow
                            .getAcceptedAmount(), storedDisputeWorkflow.getDisputedAmount());

                    eventIngester.generateEvent(
                        FoxtrotEventUtils.toSignalEvent(disputeWorkflow.getDisputedAmount(),
                            SignalType.PARTIAL_DEBIT, storedDisputeWorkflow.getDisputeId(),
                            storedDisputeWorkflow.getDisputeStage(),
                            storedDisputeWorkflow.getDisputeType(),
                            storedDisputeWorkflow.getDisputeWorkflowId(),
                            storedDisputeWorkflow.getTransactionReferenceId(),
                            financialDisputeWorkflow.getCurrentState(),
                            financialDisputeWorkflow.getCurrentEvent()));

                    return true;
                }
            });
    }

    private void triggerMerchantAcceptedEvent(final DisputeWorkflow disputeWorkflow,
        final Dispute dispute,
        final DisputeWorkflow storedDisputeWorkflow) {
        log.info("DisputeWorkflow {} is going to Accepted state", storedDisputeWorkflow);
        disputeService.triggerEvent(Constants.STRATOS_SYSTEM_USER_OLYMPUS,
            dispute.getTransactionReferenceId(), storedDisputeWorkflow.getDisputeWorkflowId(),
            MERCHANT_ACCEPT_CHARGEBACK, PartialAcceptanceTransitionContext.builder()
                .acceptedAmount(disputeWorkflow.getDisputedAmount()).build());
    }

    private void triggerNpciRepresentmentEvent(final Dispute dispute,
        final DisputeWorkflow storedDisputeWorkflow) {
        log.info("DisputeWorkflow {} is going to Representment state", storedDisputeWorkflow);
        disputeService.triggerEvent(Constants.STRATOS_SYSTEM_USER_OLYMPUS,
            dispute.getTransactionReferenceId(), storedDisputeWorkflow.getDisputeWorkflowId(),
            COMPLETE_NPCI_REPRESENTMENT, EmptyTransitionContext.builder().build());
    }
}
