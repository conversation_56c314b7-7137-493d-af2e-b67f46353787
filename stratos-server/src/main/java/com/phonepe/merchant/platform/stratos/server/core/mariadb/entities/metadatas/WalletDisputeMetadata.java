package com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas;

import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.PrimaryKey;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeMetadataType;
import lombok.*;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.envers.Audited;

import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@Audited
@ToString
@DynamicInsert
@DynamicUpdate
@NoArgsConstructor
@FieldNameConstants
@DiscriminatorValue(DisputeMetadataType.Ordinals.WALLET_DISPUTE_METADATA)
public class WalletDisputeMetadata extends DisbursementDisputeMetadata {

    private static final long serialVersionUID = -457764526925051254L;

    @NotEmpty
    @Column(name = "reason_code", columnDefinition = "varchar(255)")
    private String reasonCode;

    @Builder
    @SuppressWarnings("java:S107")
    public WalletDisputeMetadata(
            final PrimaryKey key,
            final String disputeMetadataId,
            final String disputeWorkflowId,
            final String transactionReferenceId,
            final LocalDateTime createdAt,
            final LocalDateTime updatedAt,
            final String disbursementTransactionId,final String reasonCode) {
        super(key, disputeMetadataId,
                disputeWorkflowId, transactionReferenceId,createdAt,updatedAt,
                DisputeMetadataType.WALLET_DISPUTE_METADATA, disbursementTransactionId);
        this.reasonCode=reasonCode;
    }

}
