package com.phonepe.merchant.platform.stratos.server.core.resources;


import static com.phonepe.olympus.im.models.core.CoreConstants.BEARER;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.google.inject.Inject;
import com.phonepe.merchant.platform.stratos.models.commons.DateRange;
import com.phonepe.merchant.platform.stratos.models.udir.requests.UdirRaiseComplaintRequest;
import com.phonepe.merchant.platform.stratos.models.udir.response.UdirRaiseComplaintResponse;
import com.phonepe.merchant.platform.stratos.server.core.models.ApiState;
import com.phonepe.merchant.platform.stratos.server.core.services.UdirService;
import com.phonepe.merchant.platform.stratos.server.core.utils.AuthorizationUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.DtoUtils;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.olympus.im.client.annotations.AccessAllowed;
import com.phonepe.olympus.im.models.security.ServiceUserPrincipal;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import com.phonepe.payments.upiclientmodel.complaint.UPIClientOutgoingComplaintResponse;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import io.dropwizard.auth.Auth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Path("/v1/udir/complaint")
@Tag(name = "UDIR Related APIs")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
@SecurityScheme(name = BEARER, type = SecuritySchemeType.APIKEY, in = SecuritySchemeIn.HEADER, paramName = "Authorization")
@SecurityRequirement(name = BEARER)
public class UdirResource {

    private final UdirService udirService;
    private final OlympusIMClient olympusIMClient;

    @POST
    @ExceptionMetered
    @AccessAllowed
    @Path("/raise")
    @Operation(summary = "Raise a Udir Complaint")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public UdirRaiseComplaintResponse raiseComplaint(
        @Valid @NotNull UdirRaiseComplaintRequest udirOutgoingComplaintRequest,
        @Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal) {

        final UserAuthDetails userAuthDetails = serviceUserPrincipal.getUserAuthDetails();
        AuthorizationUtils.authorizeUdirRaiseComplaint(olympusIMClient, userAuthDetails,
            DtoUtils.udirDisputeDtoToType(udirOutgoingComplaintRequest.getDisputeType()));
        return udirService.raiseComplaint(udirOutgoingComplaintRequest);
    }

    @RolesAllowed("udir/callback")
    @POST
    @ExceptionMetered
    @Path("/callback")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public void complaintCallback(
        UPIClientOutgoingComplaintResponse upiClientOutgoingComplaintResponse) {
        udirService.processCallback(upiClientOutgoingComplaintResponse);
    }

    @PUT
    @ExceptionMetered
    @RolesAllowed("udir/ttl-breach")
    @Path("/ttl-breach")
    @Operation(summary = "Update state of Udir Complaints which are breaching TTL on the same day as of API invocation")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public void updateChargebackStateBreachingTtl(
        @Valid @NotNull final DateRange dateRange) {

        udirService.updateUdirComplaintStateBreachingTtl(dateRange);
    }
}
