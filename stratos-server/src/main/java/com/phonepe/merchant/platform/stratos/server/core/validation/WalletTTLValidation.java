package com.phonepe.merchant.platform.stratos.server.core.validation;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.pipeline.core.executor.types.Validator;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeCategoryDto;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.WalletTransactionDetails;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.CreateDisputeRequest;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.configs.DisputeCategoryTtlConfig;
import com.phonepe.merchant.platform.stratos.server.core.configs.TtlConfig;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeCategory;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.services.impls.WalletTransactionService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.DtoUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class WalletTTLValidation extends Validator<CreateDisputeRequest> {

    private static final Logger log = LoggerFactory.getLogger(WalletTTLValidation.class);
    private final Map<DisputeType, TtlConfig> ttlConfigMap;
    private final Map<DisputeType, DisputeCategoryTtlConfig> fraudChargebackTtl;
    private final WalletTransactionService walletTransactionService;

    @Inject
    public WalletTTLValidation(String name, Map<DisputeType, TtlConfig> ttlConfigMap, Map<DisputeType, DisputeCategoryTtlConfig> fraudChargebackTtl,
        WalletTransactionService walletTransactionService) {
        super("ttlValidation");
        this.ttlConfigMap = ttlConfigMap;
        this.fraudChargebackTtl = fraudChargebackTtl;
        this.walletTransactionService = walletTransactionService;
    }

    @Override
    public void validate(CreateDisputeRequest createDisputeRequest){

        WalletTransactionDetails transactionDetail = walletTransactionService.getTransactionDetails(
            createDisputeRequest.getTransactionId());
        DisputeCategoryDto disputeCategory = DtoUtils.requestDisputeCategoryToDisputeCategoryDto(
            createDisputeRequest.getDisputeData().getDisputeCategory(),
            createDisputeRequest.getDisputeData().getDisputeType());
        DisputeType disputeType = DtoUtils.transactionTypeToDisputeType(
            createDisputeRequest.getTransactionType(),
            createDisputeRequest.getDisputeData().getDisputeType());
        long ttlDays = DisputeCategoryDto.FRAUD_CHARGEBACK.equals(disputeCategory)
                ? fraudChargebackTtl.get(DisputeType.WALLET_CHARGEBACK).getTtlForStage(DisputeCategory.FRAUD_CHARGEBACK)
                : ttlConfigMap.get(disputeType)
                    .getTtlForStage(DtoUtils.disputeDataStageToDisputeStage(
                        createDisputeRequest.getDisputeData().getDisputeStage()));

        LocalDateTime expiryDate = LocalDateTime.ofInstant(
                transactionDetail.getSentTime().toInstant(), ZoneId.systemDefault())
            .plusDays(ttlDays);

        if (LocalDateTime.now().isAfter(expiryDate)) {
            log.error("Wallet transaction ttl expire for creation request {} transaction detail is: {} ",
                createDisputeRequest, transactionDetail);
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.TTL_BREACHED,
                Map.of(
                    Constants.MESSAGE, StratosErrorCodeKey.TTL_BREACHED.getKey(),
                    "Ttl expire on ", expiryDate));
        }
    }
}
