package com.phonepe.merchant.platform.stratos.server.core.clients;

import static com.phonepe.merchant.platform.stratos.server.core.utils.TypeReferences.EDC_TRANSACTION_DETAILS_RESPONSE;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.edc.response.EdcTransactionDetailsResponse;
import com.phonepe.edc.response.TransactionGenericResponse;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs.EdcClientConfig;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.HttpClientUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.TypeReferences;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import com.phonepe.platform.http.v2.executor.factory.HttpExecutorBuilderFactory;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class EdcServiceClient {

    private final HttpExecutorBuilderFactory httpExecutorBuilderFactory;
    private final OlympusIMClient olympusIMClient;

    @Inject
    public EdcServiceClient(
        @EdcClientConfig final HttpConfiguration httpConfiguration,
        final ServiceEndpointProviderFactory serviceEndpointProviderFactory,
        final ObjectMapper mapper,
        final MetricRegistry metricRegistry,
        final OlympusIMClient olympusIMClient) {
        this.olympusIMClient = olympusIMClient;
        this.httpExecutorBuilderFactory = HttpClientUtils.getHttpExecutorBuilderFactory(
            EdcServiceClient.class, httpConfiguration, serviceEndpointProviderFactory, mapper, metricRegistry);
    }

    public TransactionGenericResponse edcUnblockReversals(
        final String merchantTransactionId) {

        final var url = String
            .format("/v2/chargeback/reject/%s", merchantTransactionId);

        return HttpClientUtils.executePost(
            httpExecutorBuilderFactory, "edcUnblockReversals", url, Constants.EMPTY_HTTP_DATA,
            TypeReferences.EDC_TRANSACTION_RESPONSE,
            olympusIMClient);
    }

    public TransactionGenericResponse edcBlockReversals(
        final String merchantTransactionId) {
        final var url = String
            .format("/v2/chargeback/initiate/%s", merchantTransactionId);

        return HttpClientUtils.executePost(
            httpExecutorBuilderFactory, "edcBlockReversals", url, Constants.EMPTY_HTTP_DATA,
            TypeReferences.EDC_TRANSACTION_RESPONSE,
            olympusIMClient);
    }
    public EdcTransactionDetailsResponse getEdcTransactionDetails(
        final String tenant, final String merchantId, final String terminalId, final String rrn) {
        final var url = String.format("/v1/chargeback/edcTransactions/%s/%s/%s/%s", tenant, merchantId, terminalId, rrn);
        return HttpClientUtils.executeGet(
            httpExecutorBuilderFactory,"getEdcTransactionDetails", url,
            EDC_TRANSACTION_DETAILS_RESPONSE, olympusIMClient);
    }

    public EdcTransactionDetailsResponse  getEdcTransactionDetails(final String merchantTransactionId){
        final var url = String.format("/v2/chargeback/edcTransactions/%s", merchantTransactionId);
        return HttpClientUtils.executeGet(
            httpExecutorBuilderFactory,"getEdcTransactionDetailsFromMerchantTransactionId", url,
            EDC_TRANSACTION_DETAILS_RESPONSE, olympusIMClient);
    }
}
