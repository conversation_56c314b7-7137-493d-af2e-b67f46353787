package com.phonepe.merchant.platform.stratos.server.core.creator.dispute;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.WalletTransactionDetails;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.WalletDisputeRequest;
import com.phonepe.merchant.platform.stratos.server.core.configs.TtlConfig;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.helpers.id.IdHelper;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.WalletDisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.WalletDisputeMetadataRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.impls.WalletTransactionService;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeMetadataHelper;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class WalletDisputeCreator extends DisputeCreator<WalletDisputeRequest> {

    private final WalletTransactionService transactionService;
    private final WalletDisputeMetadataRepository walletDisputeMetadataRepository;
    private final DisputeMetadataHelper disputeMetadataHelper;


    @Inject
    @SuppressWarnings("java:S107")
    protected WalletDisputeCreator(
        DisputeService disputeService,
        IdHelper idHelper, Map<DisputeType, TtlConfig> ttlConfigMap,
        WalletTransactionService transactionService,
        WalletDisputeMetadataRepository walletDisputeMetadataRepository,
        DisputeMetadataHelper disputeMetadataHelper, EventIngester eventIngester) {
        super(disputeService, idHelper, ttlConfigMap, eventIngester);
        this.transactionService = transactionService;
        this.walletDisputeMetadataRepository = walletDisputeMetadataRepository;
        this.disputeMetadataHelper = disputeMetadataHelper;
    }

    @Override
    public WalletTransactionDetails getTransactionDetails(String transactionId) {
        return transactionService.getTransactionDetails(transactionId);
    }

    @Override
    protected void createAndPersistDisputeMeta(DisputeWorkflow disputeWorkflow,
        WalletDisputeRequest createDisputeRequest) {

        WalletDisputeMetadata walletDisputeMetadata = disputeMetadataHelper.toWalletDisputeMetadata(
            disputeWorkflow, createDisputeRequest.getReasonCode());

        log.info("Wallet dispute metadata for transactionId {}, dispute workflow id {} is {}",
            createDisputeRequest.getTransactionId(), disputeWorkflow.getDisputeWorkflowId(), walletDisputeMetadata);
        walletDisputeMetadataRepository.save(walletDisputeMetadata);
    }
}
