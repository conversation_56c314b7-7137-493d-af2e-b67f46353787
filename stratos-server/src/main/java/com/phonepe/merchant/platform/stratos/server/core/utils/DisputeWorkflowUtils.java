package com.phonepe.merchant.platform.stratos.server.core.utils;

import com.google.inject.Provider;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.StratosConfiguration;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.NonFinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVisitor;
import com.phonepe.merchant.platform.stratos.server.core.queue.ActionType;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.DisputeWorkflowMessage;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.publishfeeds.ChargebackRecoveryFeedPublishActor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.raiseevents.RaiseChargebackRecoveryAccountingEventActor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.statechange.StateChangeHandlerActor;
import io.appform.dropwizard.actors.actor.Actor;
import lombok.SneakyThrows;
import io.appform.dropwizard.sharding.DBShardingBundle;
import io.appform.dropwizard.sharding.dao.RelationalDao;
import io.appform.dropwizard.sharding.utils.ShardCalculator;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.List;
import lombok.experimental.UtilityClass;

import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.IntStream;
import lombok.experimental.UtilityClass;
import org.hibernate.criterion.DetachedCriteria;

@UtilityClass
public class DisputeWorkflowUtils {

    public FinancialDisputeWorkflow getFinancialDisputeWorkflow(DisputeWorkflow disputeWorkflow) {
        return disputeWorkflow.accept(
            new DisputeWorkflowVisitor<>() {

                @Override
                public FinancialDisputeWorkflow visit(
                    FinancialDisputeWorkflow financialDisputeWorkflow1) {
                    return financialDisputeWorkflow1;
                }

                @Override
                public FinancialDisputeWorkflow visit(
                    NonFinancialDisputeWorkflow nonFinancialDisputeWorkflow) {
                    throw DisputeExceptionUtil.error(StratosErrorCodeKey.OPERATION_NOT_ALLOWED, Map.of(
                        Constants.MESSAGE, "Financial DisputeWorkflow Required"));
                }
            });
    }
    @SneakyThrows
    public void closeWorkflow(
            final DisputeWorkflow workflow,
            final StateChangeHandlerActor stateChangeHandlerActor) {
        final DisputeWorkflowMessage msg = DisputeWorkflowMessage.builder()
                .disputeWorkflowId(workflow.getDisputeWorkflowId())
                .disputeWorkflowEvent(DisputeWorkflowEvent.END_WORKFLOW)
                .transactionReferenceId(workflow.getTransactionReferenceId())
                .build();
        stateChangeHandlerActor.publish(msg);
    }

    @SneakyThrows
    public void merchantRecovery(
            final DisputeWorkflow currentStagedisputeWorkflow,
            final Actor<ActionType, DisputeWorkflowMessage> raiseAccountingEventActor,
            final ChargebackRecoveryFeedPublishActor chargebackRecoveryFeedPublishActor) {
        final DisputeWorkflowMessage disputeWorkflowMessage = DisputeWorkflowMessage.builder()
                .transactionReferenceId(currentStagedisputeWorkflow.getTransactionReferenceId())
                .disputeWorkflowId(currentStagedisputeWorkflow.getDisputeWorkflowId())
                .build();
        chargebackRecoveryFeedPublishActor.publish(disputeWorkflowMessage);
        raiseAccountingEventActor.publish(disputeWorkflowMessage);
    }


    public static <T> List<T> executeScatterGatherInParallel(RelationalDao<T> relationalDao,
                                                             DBShardingBundle<StratosConfiguration> dbShardingBundle,final DetachedCriteria detachedCriteria) {
        try {
            List<T> results = new ArrayList<>();
            ExecutorService executorService = Executors.newFixedThreadPool(dbShardingBundle.getNumShards());
            List<Callable<Boolean>> tasks = new ArrayList<>();
            DisputeWorkflowUtils.getShardKeys(relationalDao.getShardCalculator(), dbShardingBundle.getNumShards())
                    .forEach(shardKey -> tasks.add(() -> {
                        results.addAll(relationalDao.select(String.valueOf(shardKey), detachedCriteria, 0, 100_000));
                        return true;
                    }));
            List<Future<Boolean>> futures = executorService.invokeAll(tasks, 5, TimeUnit.MINUTES);
            futures.forEach(booleanFuture -> {
                try {
                    booleanFuture.get();
                } catch (InterruptedException | ExecutionException e) {
                    Thread.currentThread().interrupt();
                    throw DisputeExceptionUtil.error(StratosErrorCodeKey.INTERNAL_SERVER_ERROR,
                            Map.of(Constants.MESSAGE, "Error executing scatter-gather operation"));
                }
            });
            return results;
        } catch (InterruptedException exception) {
            Thread.currentThread().interrupt();
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INTERNAL_SERVER_ERROR,
                    Map.of(Constants.MESSAGE, "Error executing scatter-gather operation"));
        }
    }
    public static Set<String> getShardKeys(ShardCalculator<String> shardCalculator,int numShards) {
        Map<Integer, String> shardKeys = new HashMap<>();
        IntStream.range(0, numShards)
                .forEach(i -> shardKeys.put(i, ""));
        while (numShards > 0) {
            String shardKey = UUID.randomUUID()
                    .toString();
            int shardId = shardCalculator.shardId(shardKey);
            if (shardKeys.containsKey(shardId) && shardKeys.get(shardId)
                    .isEmpty()) {
                shardKeys.put(shardId, shardKey);
                numShards--;
            }
        }
        return new HashSet<>(shardKeys.values());
    }
    public boolean ignoreSignal(
            Map<DisputeType,Map<DisputeWorkflowVersion, List<DisputeWorkflowState>>> ignoreSignalStateConfig,
            DisputeWorkflow disputeWorkflow){
        if( ignoreSignalStateConfig == null ||
            !ignoreSignalStateConfig.containsKey(disputeWorkflow.getDisputeType()) ||
            !ignoreSignalStateConfig.get(disputeWorkflow.getDisputeType()).containsKey(disputeWorkflow.getDisputeWorkflowVersion()))
            return false;
        return ignoreSignalStateConfig.get(disputeWorkflow.getDisputeType())
                .get(disputeWorkflow.getDisputeWorkflowVersion())
                .contains(disputeWorkflow.getCurrentState());

    }
}
