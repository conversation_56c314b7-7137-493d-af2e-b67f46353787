package com.phonepe.merchant.platform.stratos.server.core.clients.utils;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.clients.CallbackClient;
import com.phonepe.merchant.platform.stratos.server.core.utils.HttpClientUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import com.phonepe.platform.http.v2.executor.factory.HttpExecutorBuilderFactory;
import io.dropwizard.setup.Environment;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

@Slf4j
@Singleton
public class ClientContainer {

    private final Map<String, HttpConfiguration> httpClientConfigMap;
    private final ServiceEndpointProviderFactory serviceEndpointProviderFactory;
    private final MetricRegistry metricRegistry;
    private final Environment environment;
    private final ObjectMapper mapper;


    private final Map<String, HttpExecutorBuilderFactory> httpClientMap;

    @Inject
    public ClientContainer(
            final Map<String, HttpConfiguration> httpClientConfigMap,
            MetricRegistry metricRegistry,
            ServiceEndpointProviderFactory serviceEndpointProviderFactory,
            Environment environment, ObjectMapper mapper) {
        this.httpClientConfigMap = httpClientConfigMap;
        this.metricRegistry = metricRegistry;
        this.serviceEndpointProviderFactory = serviceEndpointProviderFactory;
        this.environment = environment;
        this.mapper = mapper;
        this.httpClientMap = new HashMap<>();

        this.initialise();
    }

    @SuppressWarnings("java:S1874")
    private void initialise() {
        if (!httpClientConfigMap.isEmpty()) {
            try {
                for (Entry<String, HttpConfiguration> clientConf : httpClientConfigMap.entrySet()) {
                    HttpExecutorBuilderFactory executorBuilderFactory = HttpClientUtils.getHttpExecutorBuilderFactory(
                            CallbackClient.class, clientConf.getValue(),
                            serviceEndpointProviderFactory, environment, mapper, metricRegistry);
                    httpClientMap.put(
                        clientConf.getKey(),
                        executorBuilderFactory
                    );
                }
            } catch (Exception e) {
                log.error("Error while creating app", e);
                throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.INTERNAL_SERVER_ERROR, e);
            }
        }
    }
    public HttpExecutorBuilderFactory getHttpExecutorBuilderFactory(String serviceName) {
        return httpClientMap.get(serviceName);
    }

}
