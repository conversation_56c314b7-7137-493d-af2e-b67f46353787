package com.phonepe.merchant.platform.stratos.server.core.events.type;

import com.phonepe.merchant.platform.stratos.server.core.events.BaseEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.EventType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeCategory;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeIssuer;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class DisputeActionSuccessEvent extends BaseEvent {

    private final String disputeWorkflowId;

    private final String disputeId;

    private final String transactionReferenceId;

    private final DisputeWorkflowEvent disputeWorkflowEvent;

    private final DisputeWorkflowState fromState;

    private final DisputeWorkflowState toState;

    private final DisputeType disputeType;

    private final DisputeStage disputeStage;

    private final DisputeWorkflowVersion disputeWorkflowVersion;

    private final String disputeSourceId;

    private final String instrumentTransactionId;

    private final String disputeReferenceId;
    private final String merchantId;

    private final DisputeCategory disputeCategory;

    private final DisputeIssuer disputeIssuer;

    private final long transactionAmount;

    private final long disputeAmount;

    private final long acceptedAmount;

    private final String gandalfUserId;

    @Builder
    @SuppressWarnings("java:S107")
    public DisputeActionSuccessEvent(final String disputeWorkflowId, final String disputeId,
        final String transactionReferenceId,
        final DisputeWorkflowEvent disputeWorkflowEvent,
        final DisputeWorkflowState fromState,
        final DisputeWorkflowState toState,
        final DisputeType disputeType,
        final DisputeStage disputeStage,
        final DisputeWorkflowVersion disputeWorkflowVersion, final String disputeSourceId,
        final String instrumentTransactionId, final String disputeReferenceId,
        final DisputeCategory disputeCategory,
        final DisputeIssuer disputeIssuer, final long transactionAmount, final long disputeAmount,
        final long acceptedAmount,
        final String gandalfUserId,
        final String merchantId) {
        super(EventType.DISPUTE_ACTION_SUCCESS_EVENT);
        this.disputeWorkflowId = disputeWorkflowId;
        this.disputeId = disputeId;
        this.transactionReferenceId = transactionReferenceId;
        this.disputeWorkflowEvent = disputeWorkflowEvent;
        this.fromState = fromState;
        this.toState = toState;
        this.disputeType = disputeType;
        this.disputeStage = disputeStage;
        this.disputeWorkflowVersion = disputeWorkflowVersion;
        this.disputeSourceId = disputeSourceId;
        this.instrumentTransactionId = instrumentTransactionId;
        this.disputeReferenceId = disputeReferenceId;
        this.disputeCategory = disputeCategory;
        this.disputeIssuer = disputeIssuer;
        this.transactionAmount = transactionAmount;
        this.disputeAmount = disputeAmount;
        this.acceptedAmount = acceptedAmount;
        this.gandalfUserId = gandalfUserId;
        this.merchantId = merchantId;
    }

    @Override
    protected String getGroupingKey() {
        return transactionReferenceId;
    }
}
