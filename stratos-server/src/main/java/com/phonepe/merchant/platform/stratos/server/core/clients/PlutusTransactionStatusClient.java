package com.phonepe.merchant.platform.stratos.server.core.clients;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.accountingevents.AccountingEventType;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs.PlutusTransactionStatusConfiguration;
import com.phonepe.merchant.platform.stratos.server.core.utils.HttpClientUtils;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import com.phonepe.platform.http.v2.executor.factory.HttpExecutorBuilderFactory;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class PlutusTransactionStatusClient {

    private final HttpExecutorBuilderFactory httpExecutorBuilderFactory;
    private final OlympusIMClient olympusIMClient;

    @Inject
    public PlutusTransactionStatusClient(
        @PlutusTransactionStatusConfiguration final HttpConfiguration httpConfiguration,
        final ServiceEndpointProviderFactory serviceEndpointProviderFactory,
        final ObjectMapper mapper,
        final MetricRegistry metricRegistry,
        final OlympusIMClient olympusIMClient) {

        this.httpExecutorBuilderFactory = HttpClientUtils.getHttpExecutorBuilderFactory(
            PlutusTransactionStatusClient.class, httpConfiguration,
            serviceEndpointProviderFactory, mapper, metricRegistry);
        this.olympusIMClient = olympusIMClient;
    }

    public boolean checkAccountingEventStatus(final String eventId,
        final AccountingEventType eventType) {

        final var url = String.format("/v1/transaction/exists/%s/%s", eventId, eventType.name());

        HttpClientUtils.executeGet(
            httpExecutorBuilderFactory, "checkAccountingEventStatus", url, String.class,
            olympusIMClient);

        return true;
    }
}
