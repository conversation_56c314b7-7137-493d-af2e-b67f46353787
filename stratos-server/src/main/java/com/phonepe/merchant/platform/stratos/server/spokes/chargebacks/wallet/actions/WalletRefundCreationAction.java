package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.actions;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.queue.actors.CallbackActor;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.DisputeWorkflowMessage;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.UpdateDisputeStateBaseAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.statechange.StateChangeHandlerActor;
import lombok.SneakyThrows;

public class WalletRefundCreationAction extends UpdateDisputeStateBaseAction {

    private final Provider<StateChangeHandlerActor> stateChangeHandlerProvider;

    @Inject
    public WalletRefundCreationAction(
        DisputeService disputeService,
        DisputeWorkflowRepository disputeWorkflowRepository,
        EventIngester eventIngester,
        Provider<StateChangeHandlerActor> stateChangeHandlerProvider,
        CallbackActor callbackActorProvider) {
        super(disputeService, disputeWorkflowRepository, eventIngester, callbackActorProvider);
        this.stateChangeHandlerProvider = stateChangeHandlerProvider;
    }

    @Override
    @SneakyThrows
    protected void postTransition(final DisputeWorkflow disputeWorkflow) {

        final var disputeWorkflowMessage = DisputeWorkflowMessage.builder()
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
            .disputeWorkflowEvent(DisputeWorkflowEvent.INITIATE_CHARGEBACK_REFUND)
            .build();

        stateChangeHandlerProvider.get().publish(disputeWorkflowMessage);

    }

}
