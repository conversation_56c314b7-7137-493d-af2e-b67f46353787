package com.phonepe.merchant.platform.stratos.server.core.models;

public interface DisputeWorkflowStateVisitor<T> {

     T visitReceived() ;

     T visitRgcsAcceptanceRequired() ;

     T visitRgcsAcceptanceCompleted() ;

     T visitRepresentmentRequired() ;

     T visitNPCIRepresentmentCompleted() ;

     T visitCreditReceived() ;

     T visitRefundBlocked() ;

     T visitFulfilmentDocumentsReceived() ;

     T visitPartialFulfilmentDocumentsReceived() ;

     T visitNPCIPartialRepresentmentCompleted() ;

     T visitPartialCreditReceived() ;

     T visitMerchantNotRespondedWithinTTL() ;

     T visitMerchantAcceptedChargeback() ;

     T visitNPCIAcceptanceCompleted() ;

     T visitAbsorbChargebackRequested() ;

     T visitAbsorbChargebackRejected() ;

     T visitAbsorbChargebackApproved() ;

     T visitChargebackAbsorbed() ;

     T visitChargebackAbsorbedReversed() ;

     T visitRecoverChargebackRequested() ;

     T visitRecoverChargebackRejected() ;

     T visitRecoverChargebackApproved() ;

     T visitRecoverChargebackEventRaised() ;

     T visitRecoverChargebackEventAccepted() ;

     T visitReversalOfRecoveredChargebackRequested() ;

     T visitReversalOfRecoveredChargebackApproved() ;

     T visitReversalOfRecoveredChargebackEventRaised() ;

     T visitReversalOfRecoveredChargebackEventAccepted() ;

     T visitDebitReceived() ;

     T visitPartialDebitReceived() ;

     T visitPgRepresentmentCompleted() ;

     T visitPgAcceptanceCompleted() ;

     T visitPgPartialRepresentmentCompleted() ;

     T visitUdirComplaintRejected() ;

     T visitFailure() ;

     T visitUdirComplaintAccepted() ;

     T visitUdirResponseReceived() ;

     T visitUdirResponseNotReceived() ;

     T visitInternalMerchantRepresentmentRequired() ;

     T visitChargebackCancelled() ;

     T visitToaBlockedDueToKs() ;

     T visitP2pmToaInitiated() ;

     T visitP2pmToaInitiationFailed() ;

     T visitP2pmToaCompleted() ;

     T visitP2pmToaFailed() ;

     T visitP2pmToaPending() ;

     T visitP2pmToaCompletedExternally() ;

     T visitP2pmToaFailedAfterMaxAutoRetry() ;

     T visitRepresentedCompleted() ;

     T visitAcceptanceCompleted() ;

     T visitPartialRepresentmentCompleted() ;

     T visitFraudRejected();
     T visitSuspectedFraud();

     T visitCBRefundCreated();

     T visitCBRefundInitated();

     T visitCBRefundAccepted();
     T visitCBRefundFailed();
     T visitToaOpened();
     T visitToaClosed();
     T visitToaInitiated();
     T visitToaInitiationFailed();
     T visitToaCompleted();
     T visitToaFailed();
     T visitToaPending();
     T visitToaFailedAfterMaxAutoRetry();

     T visitFraudRepresentmentCompleted();

     T visitNpciAckChargeback();
     T visitNpciRejectedChargeback();
     T visitPartialRejectedChargeback();
     T visitPartialAcceptedChargeback();
     T visitFullyAcceptedChargeback();
     T visitAcceptedChargeback();
     T visitRejectedChargeback();
     T visitCbRefundInitiationCompleted();
     T visitToaUnableToProcess();
     T visitCbRefundProcessedExternally();
}