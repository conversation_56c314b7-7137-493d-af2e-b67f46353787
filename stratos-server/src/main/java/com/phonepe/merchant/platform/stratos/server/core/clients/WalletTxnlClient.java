package com.phonepe.merchant.platform.stratos.server.core.clients;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs.WalletTxnlClientConfig;
import com.phonepe.merchant.platform.stratos.server.core.utils.HttpClientUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.TypeReferences;
import com.phonepe.models.payments.pay.TransactionDetail;
import com.phonepe.models.payments.upi.UpiTransactionDetailRequest;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import com.phonepe.platform.http.v2.executor.factory.HttpExecutorBuilderFactory;
import com.phonepe.platform.http.v2.executor.httpdata.SerializableHttpData;
import io.dropwizard.setup.Environment;
import java.util.Date;
import javax.ws.rs.core.MediaType;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class WalletTxnlClient {

    private final HttpExecutorBuilderFactory httpExecutorBuilderFactory;
    private final OlympusIMClient olympusIMClient;

    @Inject
    public WalletTxnlClient(
        @WalletTxnlClientConfig final HttpConfiguration httpConfiguration,
        final ServiceEndpointProviderFactory serviceEndpointProviderFactory,
        final Environment environment,
        final ObjectMapper mapper,
        final MetricRegistry metricRegistry,
        final OlympusIMClient olympusIMClient) {

        this.httpExecutorBuilderFactory = HttpClientUtils.getHttpExecutorBuilderFactory(
            WalletTxnlClient.class, httpConfiguration,
            serviceEndpointProviderFactory, environment, mapper, metricRegistry);
        this.olympusIMClient = olympusIMClient;
    }

    public GenericResponse<TransactionDetail> getPaymentsTransactionDetailFromUpiTxnId(
        final String upiTxnId, final String utr, final Date txnDate) {
        
        return HttpClientUtils.executePost(
            httpExecutorBuilderFactory, "WalletTransactionIdFromUpiId",
            "/upi/wallet/transaction/phonepe/v1/upiTransaction/detail",
            SerializableHttpData.builder()
                .data(UpiTransactionDetailRequest.builder()
                    .requestDate(txnDate)
                    .upiTransactionId(upiTxnId)
                    .utr(utr)
                    .build())
                .mediaType(MediaType.APPLICATION_JSON)
                .build(),
            TypeReferences.TRANSACTION_DETAILS,
            olympusIMClient);
    }
}
