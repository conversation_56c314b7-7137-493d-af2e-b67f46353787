package com.phonepe.merchant.platform.stratos.server.core.clients;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.ErrorUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.HttpClientUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.TypeReferences;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.olympus.im.models.user.UserDetails;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import com.phonepe.platform.http.v2.executor.HeaderPair;
import com.phonepe.platform.http.v2.executor.factory.HttpExecutorBuilderFactory;
import com.phonepe.platform.http.v2.executor.httpdata.SerializableHttpData;
import com.phonepe.services.warden.core.models.checkers.StaticChecker;
import com.phonepe.services.warden.core.models.data.WardenWorkflowConfigData;
import com.phonepe.services.warden.core.models.data.WardenWorkflowInstanceData;
import com.phonepe.services.warden.core.models.data.context.JsonNodeWorkflowContext;
import com.phonepe.services.warden.core.models.requests.config.WardenWorkflowConfigCreationRequest;
import com.phonepe.services.warden.core.models.requests.instance.WardenWorkflowInstanceCreationRequest;
import com.phonepe.services.warden.core.models.responses.config.WardenWorkflowConfig;
import com.phonepe.services.warden.core.models.responses.instance.WardenWorkflowInstance;
import com.phonepe.services.warden.models.callback.HttpTenantCallback;
import io.dropwizard.setup.Environment;
import java.util.List;
import javax.ws.rs.core.MediaType;

@Singleton
public class WardenClient {
    private final HttpExecutorBuilderFactory httpExecutorBuilderFactory;
    private static final String GET_WORKFLOW_DETAILS = "GET_WORKFLOW_DETAILS";

    private static final String TENANT_ID = "STRATOS";

    private static final String WARDEN_CREATE_WORKFLOW_ENDPOINT = "/v4/workflow/%s";

    private static final String WARDEN_V4_WORKFLOW_ENDPOINT = "/v4/workflow-config";
    private static final String WARDEN_V4_WORKFLOW_CONFIG_ENDPOINT = "/v4/workflow-config/%s";

    private static final String WARDEN_GET_WORKFLOW_DETAILS_ENDPOINT = "/v4/workflow/%s/%s";
    private static final String WARDEN_RAISE_REQUEST = "wardenRaiseRequest";

    private final OlympusIMClient olympusIMClient;

    @Inject
    public WardenClient(
            final ServiceEndpointProviderFactory serviceEndpointProviderFactory,
            @Configs.WardenClientConfig final HttpConfiguration httpConfiguration,
            final Environment environment,
            final ObjectMapper mapper,
            final MetricRegistry metricRegistry,
            final OlympusIMClient olympusIMClient) {

        this.httpExecutorBuilderFactory = HttpClientUtils.getHttpExecutorBuilderFactory(
                WardenClient.class, httpConfiguration,
                serviceEndpointProviderFactory, environment, mapper, metricRegistry);

        this.olympusIMClient = olympusIMClient;
    }
    public WardenWorkflowInstance createWorkflow(
            String workflowType,
            ObjectNode reviewContext,
            String userAuthToken) {
        String url = String.format(WARDEN_CREATE_WORKFLOW_ENDPOINT, TENANT_ID);
        WardenWorkflowInstanceCreationRequest wardenWorkflowInstanceCreationRequest =
            WardenWorkflowInstanceCreationRequest.builder()
                .tenantId(TENANT_ID)
                .workflowType(workflowType)
                .data(WardenWorkflowInstanceData.builder()
                    .reviewContext(
                        JsonNodeWorkflowContext.builder()
                                .data(reviewContext).build())
                    .build())
                .build();

        return HttpClientUtils.executePost(
            httpExecutorBuilderFactory,
            WARDEN_RAISE_REQUEST,
            url,
            new SerializableHttpData(MediaType.APPLICATION_JSON,
                    wardenWorkflowInstanceCreationRequest),
            TypeReferences.WARDEN_WORKFLOW_INSTANCE,
            List.of(
                HeaderPair.builder()
                    .name(Constants.AUTHORIZATION)
                    .value(olympusIMClient.getSystemAuthHeader())
                    .build(),
                HeaderPair.builder()
                    .name(Constants.X_END_USER_AUTHORIZATION)
                    .value(userAuthToken)
                    .build()
            ),
            ErrorUtils.exceptionConsumerWithHystrixTimeoutHandler()
        );
    }

    public WardenWorkflowConfig createNewWorkFlowType(
            String workflowType,
            String userAuthToken, List<UserDetails> userDetails) {
        String url = String.format(WARDEN_V4_WORKFLOW_CONFIG_ENDPOINT, TENANT_ID);

        JsonNode callbackConfig = MapperUtils.convertToJsonNode(HttpTenantCallback.builder()
                .httpClientId("stratos")
                .path("/v1/callback/warden-callback")
                .build());
        WardenWorkflowConfigCreationRequest configCreationRequest = WardenWorkflowConfigCreationRequest.builder()
                .tenantId(TENANT_ID)
                .workflowType(workflowType)
                .data(WardenWorkflowConfigData.builder()
                        .workflowContext(JsonNodeWorkflowContext.builder()
                                .data(callbackConfig)
                                .build())
                        .build())
                .workflowName("Penalty class creation checker with workflow type " + workflowType)
                .checkers(List.of(StaticChecker.builder()
                        .name("PenaltyClassApprover")
                        .users(userDetails)
                        .build()))
                .build();

        return HttpClientUtils.executePost(
                httpExecutorBuilderFactory,
                WARDEN_RAISE_REQUEST,
                url,
                new SerializableHttpData(MediaType.APPLICATION_JSON,
                        configCreationRequest),
                TypeReferences.WARDEN_WORKFLOW_CONFIG_CREATE,
                List.of(
                        HeaderPair.builder()
                                .name(Constants.AUTHORIZATION)
                                .value(userAuthToken)
                                .build(),
                        HeaderPair.builder()
                                .name(Constants.X_END_USER_AUTHORIZATION)
                                .value(userAuthToken)
                                .build()
                ),
                ErrorUtils.exceptionConsumerWithHystrixTimeoutHandler()
        );
    }

    public WardenWorkflowInstance getWorkflowDetailsFromWorkflowId(String workflowId){
        String url = String.format(WARDEN_GET_WORKFLOW_DETAILS_ENDPOINT, TENANT_ID,workflowId);
        return HttpClientUtils.executeGet(
            httpExecutorBuilderFactory,
            GET_WORKFLOW_DETAILS,
            url,
            TypeReferences.WARDEN_WORKFLOW_INSTANCE,
            olympusIMClient
        );
    }


}
