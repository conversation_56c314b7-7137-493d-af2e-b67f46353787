package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.udir.statemachines;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.TransitionLockCommand;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.registries.StateMachineListenerRegistry;
import com.phonepe.merchant.platform.stratos.server.core.registries.keys.DisputeStateMachineRegistryKey;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.DisputeStateMachine;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.UpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.interceptors.impls.DisputeErrorHandlingInterceptor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.udir.statemachines.actions.UdirComplaintCreateEntryAction;
import java.util.EnumSet;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.config.builders.StateMachineConfigurationConfigurer;
import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;

@Slf4j
@Singleton
public class UdirComplaintStateMachine extends DisputeStateMachine {

    private final UdirComplaintCreateEntryAction createEntryAction;
    private final UpdateDisputeStateAction updateDisputeStateAction;

    @Inject
    public UdirComplaintStateMachine(
        final TransitionLockCommand transitionLockCommand,
        final UdirComplaintCreateEntryAction createEntryAction,
        final UpdateDisputeStateAction updateDisputeStateAction,
        final DisputeErrorHandlingInterceptor disputeErrorHandlingInterceptor,
        final StateMachineListenerRegistry<DisputeWorkflowState, DisputeWorkflowEvent> stateMachineListenerRegistry) {
        super(transitionLockCommand, disputeErrorHandlingInterceptor, stateMachineListenerRegistry);
        this.createEntryAction = createEntryAction;
        this.updateDisputeStateAction = updateDisputeStateAction;
    }

    @Override
    @SneakyThrows
    protected void configure(
        StateMachineConfigurationConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> config) {
        config
            .withConfiguration()
            .autoStartup(false);
    }

    @Override
    @SneakyThrows
    protected void configure(
        StateMachineStateConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> states) {
        states.withStates()
            .initial(DisputeWorkflowState.RECEIVED)
            .states(EnumSet.allOf(DisputeWorkflowState.class))
            .end(DisputeWorkflowState.UDIR_RESPONSE_RECEIVED);
    }

    @Override
    @SneakyThrows
    protected void configure(
        StateMachineTransitionConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> transitions) {
        transitions.withExternal()
            .source(DisputeWorkflowState.RECEIVED)
            .target(DisputeWorkflowState.RECEIVED)
            .event(DisputeWorkflowEvent.CREATE_ENTRY)
            .action(createEntryAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECEIVED)
            .target(DisputeWorkflowState.UDIR_COMPLAINT_REJECTED)
            .event(DisputeWorkflowEvent.NPCI_REJECTED_COMPLAINT)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECEIVED)
            .target(DisputeWorkflowState.FAILURE)
            .event(DisputeWorkflowEvent.PAYMENT_CALL_FAILED)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECEIVED)
            .target(DisputeWorkflowState.UDIR_COMPLAINT_ACCEPTED)
            .event(DisputeWorkflowEvent.NPCI_ACCEPTED_COMPLAINT)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.UDIR_COMPLAINT_ACCEPTED)
            .target(DisputeWorkflowState.UDIR_RESPONSE_RECEIVED)
            .event(DisputeWorkflowEvent.UDIR_NPCI_RESPONSE_RECEIVED)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.UDIR_COMPLAINT_ACCEPTED)
            .target(DisputeWorkflowState.UDIR_RESPONSE_NOT_RECEIVED_WITHIN_TTL)
            .event(DisputeWorkflowEvent.UDIR_NPCI_RESPONSE_NOT_RECEIVED)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECEIVED)
            .target(DisputeWorkflowState.UDIR_RESPONSE_RECEIVED)
            .event(DisputeWorkflowEvent.UDIR_NPCI_RESPONSE_RECEIVED)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECEIVED)
            .target(DisputeWorkflowState.UDIR_RESPONSE_NOT_RECEIVED_WITHIN_TTL)
            .event(DisputeWorkflowEvent.UDIR_NPCI_RESPONSE_NOT_RECEIVED)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.UDIR_RESPONSE_NOT_RECEIVED_WITHIN_TTL)
            .target(DisputeWorkflowState.UDIR_RESPONSE_RECEIVED)
            .event(DisputeWorkflowEvent.UDIR_NPCI_RESPONSE_RECEIVED)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.UDIR_RESPONSE_NOT_RECEIVED_WITHIN_TTL)
            .target(DisputeWorkflowState.UDIR_COMPLAINT_REJECTED)
            .event(DisputeWorkflowEvent.NPCI_REJECTED_COMPLAINT)
            .action(updateDisputeStateAction);
    }

    @Override
    public DisputeStateMachineRegistryKey getRegistryKey() {
        return DisputeStateMachineRegistryKey.builder()
            .disputeWorkflowVersion(DisputeWorkflowVersion.V1)
            .disputeType(DisputeType.UDIR_OUTGOING_COMPLAINT)
            .disputeStage(DisputeStage.FIRST_LEVEL)
            .build();
    }


}
