package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.upi.statemachines;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.TransitionLockCommand;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.registries.StateMachineListenerRegistry;
import com.phonepe.merchant.platform.stratos.server.core.registries.keys.DisputeStateMachineRegistryKey;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.comments.impls.MandatoryCommentUpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.comments.impls.OptionalCommentUpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.AcceptDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.AcceptPartialDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.DebitDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.FraudCheckDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.ResetChargebackAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.UpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.interceptors.impls.DisputeErrorHandlingInterceptor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.ApproveRecoverChargebackAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.ApproveRecoveryReversalChargebackAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.BlockRefundAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.RaiseChargebackRecoveryAccountingEventAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.RaiseChargebackRecoveryReversalAccountingEventAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.UnblockRefundAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.AcceptDebitAndDebitDisputeAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.upi.statemachines.actions.UpiChargebackCreateEntryAction;
import java.util.Set;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;

@Slf4j
@Singleton
public class UpiPreArbChargebackStateMachine extends UpiChargebackStateMachine {

    private final MandatoryCommentUpdateDisputeStateAction mandatoryCommentUpdateAction;
    private final OptionalCommentUpdateDisputeStateAction optionalCommentUpdateAction;
    private final UpdateDisputeStateAction updateDisputeStateActionForFraudCheck;
    private final DebitDisputeAction debitDisputeAction;
    private final AcceptDebitAndDebitDisputeAction acceptDebitAndDebitDisputeAction;


    @Inject
    @SuppressWarnings("java:S107")
    protected UpiPreArbChargebackStateMachine(
        final TransitionLockCommand transitionLockCommand,
        final DisputeErrorHandlingInterceptor disputeErrorHandlingInterceptor,
        final RaiseChargebackRecoveryReversalAccountingEventAction raiseReversalAccountingEventAction,
        final ApproveRecoveryReversalChargebackAction approveRecoveryReversalChargebackAction,
        final RaiseChargebackRecoveryAccountingEventAction raiseAccountingEventAction,
        final MandatoryCommentUpdateDisputeStateAction mandatoryCommentUpdateAction,
        final OptionalCommentUpdateDisputeStateAction optionalCommentUpdateAction,
        final ApproveRecoverChargebackAction approveRecoverChargebackAction,
        final UpdateDisputeStateAction updateDisputeStateAction,
        final UpdateDisputeStateAction updateDisputeStateActionForFraudCheck,
        FraudCheckDisputeAction fraudCheckUpdateDisputeAction, final UpiChargebackCreateEntryAction createEntryAction,
        final BlockRefundAction blockRefundAction,
        final UnblockRefundAction unblockRefundAction,
        final AcceptPartialDisputeAction acceptPartialDisputeAction,
        final DebitDisputeAction debitDisputeAction,
        final ResetChargebackAction resetChargebackAction,
        final AcceptDisputeAction acceptDisputeAction,
        AcceptDebitAndDebitDisputeAction acceptDebitAndDebitDisputeAction,
        final StateMachineListenerRegistry<DisputeWorkflowState, DisputeWorkflowEvent> stateMachineListenerRegistry) {
        super(transitionLockCommand, disputeErrorHandlingInterceptor, createEntryAction,
            mandatoryCommentUpdateAction, optionalCommentUpdateAction, updateDisputeStateAction,
            fraudCheckUpdateDisputeAction,acceptDisputeAction,acceptPartialDisputeAction,
            raiseReversalAccountingEventAction, approveRecoveryReversalChargebackAction,
            raiseAccountingEventAction,approveRecoverChargebackAction,
            blockRefundAction, unblockRefundAction,resetChargebackAction,stateMachineListenerRegistry
        );
        this.mandatoryCommentUpdateAction = mandatoryCommentUpdateAction;
        this.optionalCommentUpdateAction = optionalCommentUpdateAction;
        this.updateDisputeStateActionForFraudCheck = updateDisputeStateActionForFraudCheck;
        this.debitDisputeAction = debitDisputeAction;
        this.acceptDebitAndDebitDisputeAction = acceptDebitAndDebitDisputeAction;
    }

    @Override
    public DisputeStateMachineRegistryKey getRegistryKey() {
        return DisputeStateMachineRegistryKey.builder()
            .disputeType(DisputeType.UPI_CHARGEBACK)
            .disputeStage(DisputeStage.PRE_ARBITRATION)
            .disputeWorkflowVersion(DisputeWorkflowVersion.V1)
            .build();
    }

    @Override
    protected Set<DisputeWorkflowState> endStates() {
        return Set.of();
    }

    @Override
    @SneakyThrows
    protected void configure(
        final StateMachineTransitionConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> transitions) {
        baseTransitions(transitions)

            .withExternal()
            .source(DisputeWorkflowState.FRAUD_REJECTED)
            .target(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
            .event(DisputeWorkflowEvent.FRAUD_REJECTED_TO_ACCEPTANCE) // Manual action
            .action(updateDisputeStateActionForFraudCheck)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.FRAUD_REJECTED)
            .target(DisputeWorkflowState.FULFILMENT_DOCUMENTS_RECEIVED)
            .event(DisputeWorkflowEvent.FRAUD_REJECTED_TO_FULFILMENT) // Manual action
            .action(updateDisputeStateActionForFraudCheck)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.FRAUD_REJECTED)
            .target(DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED)
            .event(DisputeWorkflowEvent.FRAUD_REJECTED_TO_PARTIAL_FULFILMENT) // Manual action
            .action(updateDisputeStateActionForFraudCheck)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.NPCI_PARTIAL_REPRESENTMENT_COMPLETED)
            .target(DisputeWorkflowState.PARTIAL_DEBIT_RECEIVED)
            .event(DisputeWorkflowEvent.RECEIVE_PARTIAL_DEBIT)
            .action(debitDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.PARTIAL_DEBIT_RECEIVED)
            .target(DisputeWorkflowState.ABSORB_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_ABSORB_CHARGEBACK)
            .action(mandatoryCommentUpdateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.PARTIAL_DEBIT_RECEIVED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_RECOVER_CHARGEBACK)
            .action(optionalCommentUpdateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.NPCI_ACCEPTANCE_COMPLETED)
            .target(DisputeWorkflowState.DEBIT_RECEIVED)
            .event(DisputeWorkflowEvent.RECEIVE_DEBIT)
            .action(debitDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
            .target(DisputeWorkflowState.DEBIT_RECEIVED)
            .event(DisputeWorkflowEvent.MERCHANT_ACCEPTED_CHARGEBACK_TO_DEBIT_RECEIVED)
            .action(acceptDebitAndDebitDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.DEBIT_RECEIVED)
            .target(DisputeWorkflowState.ABSORB_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_ABSORB_CHARGEBACK)
            .action(mandatoryCommentUpdateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.DEBIT_RECEIVED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_RECOVER_CHARGEBACK)
            .action(optionalCommentUpdateAction);
    }
}
