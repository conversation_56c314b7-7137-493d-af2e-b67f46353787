package com.phonepe.merchant.platform.stratos.server.core.statemachines;

import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.TransitionLockCommand;
import com.phonepe.merchant.platform.stratos.server.core.registries.StateMachineListenerRegistry;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.interceptors.ErrorHandlingInterceptor;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.interceptors.TransitionLockingInterceptor;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.listeners.EventNotAcceptedListener;
import io.dropwizard.lifecycle.Managed;
import java.util.Map;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.access.StateMachineAccess;
import org.springframework.statemachine.config.StateMachineBuilder;
import org.springframework.statemachine.config.StateMachineBuilder.Builder;
import org.springframework.statemachine.config.builders.StateMachineConfigurationConfigurer;
import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;
import org.springframework.statemachine.listener.StateMachineListener;
import org.springframework.statemachine.support.DefaultStateMachineContext;
import org.springframework.statemachine.support.StateMachineInterceptor;

public abstract class BaseStateMachine<S, E> implements Managed {

    protected final Builder<S, E> stateMachineBuilder;

    protected final StateMachineInterceptor<S, E> transitionLockingInterceptor;

    protected final StateMachineInterceptor<S, E> errorHandlingInterceptor;

    protected final StateMachineListenerRegistry<S,E> stateMachineListenerRegistry;

    protected BaseStateMachine(final TransitionLockCommand transitionLockCommand,
                               final ErrorHandlingInterceptor<S, E> errorHandlingInterceptor,
                               final StateMachineListenerRegistry<S,E> stateMachineListenerRegistry) {
        this.stateMachineBuilder = StateMachineBuilder.builder();
        this.transitionLockingInterceptor = new TransitionLockingInterceptor<>(
            transitionLockCommand);
        this.errorHandlingInterceptor = errorHandlingInterceptor;
        this.stateMachineListenerRegistry = stateMachineListenerRegistry;
    }

    public StateMachine<S, E> sendEvent(final S currentState, final E event,
        final Map<Object, Object> transitionContext) {

        final var stateMachine = stateMachineBuilder.build();
        stateMachineListenerRegistry.getListeners()
                .forEach(stateMachine::addStateListener);

        final var variables = stateMachine.getExtendedState().getVariables();
        variables.putAll(transitionContext);
        variables.put(StateMachineContextKeys.TRIGGER_EVENT, event);

        stateMachine.getStateMachineAccessor().doWithAllRegions(
            sma -> {
                sma.resetStateMachine(
                    new DefaultStateMachineContext<>(currentState, null, null, null));
                sma.addStateMachineInterceptor(transitionLockingInterceptor);
                sma.addStateMachineInterceptor(errorHandlingInterceptor);
                configure(sma);
            }
        );
        stateMachine.start();

        stateMachine.sendEvent(event);

        if (stateMachine.hasStateMachineError()) {
            final var throwable = stateMachine.getExtendedState()
                .get(StateMachineContextKeys.THROWN_EXCEPTION, Throwable.class);
            throw (RuntimeException) throwable;
        }

        return stateMachine;
    }

    public StateMachine<S, E> internalApiGetStateMachine() {
        return stateMachineBuilder.build();
    }

    @Override
    public void start() {
        configure(stateMachineBuilder.configureConfiguration());
        configure(stateMachineBuilder.configureStates());
        configure(stateMachineBuilder.configureTransitions());
    }

    @Override
    public void stop() {
        // NOOP
    }

    protected void configure(final StateMachineAccess<S, E> stateMachineAccess) {
        // NOOP
    }

    protected abstract void configure(final StateMachineConfigurationConfigurer<S, E> config);

    protected abstract void configure(final StateMachineStateConfigurer<S, E> states);

    protected abstract void configure(final StateMachineTransitionConfigurer<S, E> transitions);
}
