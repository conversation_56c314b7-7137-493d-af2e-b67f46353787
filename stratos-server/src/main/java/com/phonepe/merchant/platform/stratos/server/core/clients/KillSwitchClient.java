package com.phonepe.merchant.platform.stratos.server.core.clients;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.configs.KillSwitchConfig;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs.KillSwitchClientConfig;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs.KillSwitchConfiguration;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.HttpClientUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.TypeReferences;
import com.phonepe.merchant.platform.stratos.server.core.visitors.UserDetailsEmailIdVisitor;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import com.phonepe.olympus.im.models.user.UserDetails;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import com.phonepe.platform.http.v2.executor.HeaderPair;
import com.phonepe.platform.http.v2.executor.factory.HttpExecutorBuilderFactory;
import com.phonepe.platform.http.v2.executor.httpdata.SerializableHttpData;
import com.phonepe.platform.killswitch.common.Killswitch;
import com.phonepe.platform.killswitch.common.RecommendedAction;
import com.phonepe.platform.killswitch.common.decay.NoDecay;
import com.phonepe.platform.killswitch.common.ops.killswitch.EngageRequest;
import io.dropwizard.util.Duration;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

@Slf4j
@Singleton
public class KillSwitchClient {

    private final HttpExecutorBuilderFactory httpExecutorBuilderFactory;
    private final KillSwitchConfig killSwitchConfig;
    private final OlympusIMClient olympusIMClient;


    @Inject
    public KillSwitchClient(
        @KillSwitchClientConfig final KillSwitchConfig killSwitchClientConfig,
        @KillSwitchConfiguration final HttpConfiguration killSwitchConfig,
        final ServiceEndpointProviderFactory serviceEndpointProviderFactory,
        final ObjectMapper mapper,
        final MetricRegistry metricRegistry,
        final OlympusIMClient olympusIMClient) {
        this.killSwitchConfig = killSwitchClientConfig;
        this.httpExecutorBuilderFactory = HttpClientUtils.getHttpExecutorBuilderFactory(
            KillSwitchClient.class, killSwitchConfig,
            serviceEndpointProviderFactory, mapper, metricRegistry);
        this.olympusIMClient = olympusIMClient;
    }

    public List<Killswitch> getAllStratosKillSwitch() {
        final var url = String.format("/apis/killswitches/v1/%s", killSwitchConfig.getProjectId());

        return HttpClientUtils.executeGet(
            httpExecutorBuilderFactory, "getAllStratosKillSwitch", url,
            TypeReferences.KILL_SWITCH_LIST_RESPONSE,
            olympusIMClient);
    }

    public Optional<Killswitch> getP2pmToaKillSwitch() {
        List<Killswitch> stratosKillSwitch = getAllStratosKillSwitch();
        return stratosKillSwitch.stream()
            .filter(k -> k.getTemplateId().equals(killSwitchConfig.getToaHoldTemplateId())
                && k.getRule().contains(
                DisputeType.P2PM_TOA.name()))
            .findFirst();
    }

    public Optional<Killswitch> getToaKillSwitch(final DisputeType disputeType) {
        return getAllStratosKillSwitch().stream()
                .filter(k -> k.getTemplateId().equals(killSwitchConfig.getToaHoldTemplateId())
                        && k.getRule().contains(
                        disputeType.name()))
                .findFirst();
    }

    public Killswitch engageP2pmKillSwitch(final String reason, final UserAuthDetails userAuthDetails){
        UserDetails userDetails = userAuthDetails.getUserDetails();
        var engageRequest = EngageRequest.builder()
            .from(Date.from(Instant.now().plusSeconds(1)))
            .duration(Duration.seconds(killSwitchConfig.getToaHoldKillSwitchApplyDurationInSec()))
            .createdBy(userDetails.accept(new UserDetailsEmailIdVisitor()))
            .reason(reason)
            .decay(new NoDecay())
            .params(Map.of("TYPE", DisputeType.P2PM_TOA.name()))
            .percentage(100)
            .recommendedAction(RecommendedAction.BLOCK)
            .priority(0)
            .build();

        return engageKillSwitch(killSwitchConfig.getToaHoldTemplateId(),engageRequest);
    }

    public Killswitch engageKillSwitch(final String reason, final UserAuthDetails userAuthDetails, final DisputeType disputeType) {
        final UserDetails userDetails = userAuthDetails.getUserDetails();
        val engageRequest = EngageRequest.builder()
                .from(Date.from(Instant.now().plusSeconds(1)))
                .duration(Duration.seconds(killSwitchConfig.getToaHoldKillSwitchApplyDurationInSec()))
                .createdBy(userDetails.accept(new UserDetailsEmailIdVisitor()))
                .reason(reason)
                .decay(new NoDecay())
                .params(Map.of("TYPE", disputeType.name()))
                .percentage(100)
                .recommendedAction(RecommendedAction.BLOCK)
                .priority(0)
                .build();

        return engageKillSwitch(killSwitchConfig.getToaHoldTemplateId(), engageRequest);
    }

    private Killswitch engageKillSwitch(String templateId, EngageRequest engageRequest){
        final var url = String.format("/apis/killswitches/v1/%s/%s/engage", killSwitchConfig.getProjectId(),templateId);

        return HttpClientUtils.executePost(httpExecutorBuilderFactory,"engageKillSwitch", url,
            new SerializableHttpData(MediaType.APPLICATION_JSON, engageRequest), TypeReferences.KILL_SWITCH_RESPONSE,
            olympusIMClient);
    }

    public Killswitch deactivateKillSwitch(String killSwitchId){
        final var url = String.format("/apis/killswitches/v1/%s/%s/deactivate", killSwitchConfig.getProjectId(), killSwitchId);

       return HttpClientUtils.executePut(httpExecutorBuilderFactory, "deactivateKillSwitch", url,
            Constants.EMPTY_HTTP_DATA, TypeReferences.KILL_SWITCH_RESPONSE, List.of(
               HeaderPair.builder()
                   .name(HttpHeaders.ACCEPT)
                   .value(MediaType.APPLICATION_JSON)
                   .build()
           ));
    }

}
