package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.visitors;

import com.google.inject.Singleton;
import com.phonepe.models.payments.pay.instrument.*;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@NoArgsConstructor
public class NetworkVisitor implements PaymentInstrumentVisitor<String> {
    @Override
    public String visit(AccountPaymentInstrument accountPaymentInstrument) {
        return "";
    }

    @Override
    public String visit(CreditCardPaymentInstrument creditCardPaymentInstrument) {
        return creditCardPaymentInstrument.getCardIssuer().getDisplayName();
    }

    @Override
    public String visit(DebitCardPaymentInstrument debitCardPaymentInstrument) {
        return debitCardPaymentInstrument.getCardIssuer().getDisplayName();
    }

    @Override
    public String visit(NetBankingPaymentInstrument netBankingPaymentInstrument) {
        return "";
    }

    @Override
    public String visit(WalletPaymentInstrument walletPaymentInstrument) {
        return "";
    }

    @Override
    public String visit(GiftCardPaymentInstrument giftCardPaymentInstrument) {
        return "";
    }

    @Override
    public String visit(ExternalVpaPaymentInstrument externalVpaPaymentInstrument) {
        return "";
    }

    @Override
    public String visit(ExternalWalletPaymentInstrument externalWalletPaymentInstrument) {
        return "";
    }

    @Override
    public String visit(BnplPaymentInstrument bnplPaymentInstrument) {
        return "";
    }

    @Override
    public String visit(CreditLinePaymentInstrument creditLinePaymentInstrument) {
        return "";
    }
}
