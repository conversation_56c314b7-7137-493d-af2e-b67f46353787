package com.phonepe.merchant.platform.stratos.server.core.clients;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.HttpClientUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.TypeReferences;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.payments.netpe.model.NetpeResponse;
import com.phonepe.payments.netpe.model.responses.TransactionResponse;
import com.phonepe.platform.http.v2.common.hub.RangerHubConfiguration;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import com.phonepe.platform.http.v2.discovery.utils.DiscoveryUtils;
import com.phonepe.platform.http.v2.executor.factory.HttpExecutorBuilderFactory;

@Singleton
public class NetPeServiceClient {

    private final HttpExecutorBuilderFactory httpExecutorBuilderFactory;
    private final OlympusIMClient olympusIMClient;

    @Inject
    public NetPeServiceClient(final ServiceEndpointProviderFactory serviceEndpointProviderFactory,
        final ObjectMapper mapper,
        final MetricRegistry metricRegistry,
        final RangerHubConfiguration rangerHubConfiguration,
        final OlympusIMClient olympusIMClient) {

        httpExecutorBuilderFactory = HttpClientUtils.getHttpExecutorBuilderFactory(
            NetPeServiceClient.class, DiscoveryUtils.getConfiguration(
                rangerHubConfiguration, Constants.NETPE_CLIENT_ID
            ).orElse(null),
            serviceEndpointProviderFactory, mapper, metricRegistry);
        this.olympusIMClient = olympusIMClient;
    }

    public NetpeResponse<TransactionResponse> getTransactionDetailsFromNetpeTxnId(
        String netpeTxnId) {

        final var url = String.format(
            "/v1/transactions/status/%s", netpeTxnId);

        return HttpClientUtils.executeGet(
            httpExecutorBuilderFactory, "paymentsIdFromNetpeTxnId", url,
            TypeReferences.NETPE_STATUS_RESPONSE, olympusIMClient);

    }

}
