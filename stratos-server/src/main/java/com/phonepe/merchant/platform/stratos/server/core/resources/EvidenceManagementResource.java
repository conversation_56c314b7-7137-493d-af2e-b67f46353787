package com.phonepe.merchant.platform.stratos.server.core.resources;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.google.inject.Inject;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.requests.EvidenceFilterParams;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.requests.EvidenceIdentifierParams;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.requests.EvidenceUploadPayload;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.responses.EvidenceDetail;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.responses.EvidenceResponse;
import com.phonepe.merchant.platform.stratos.server.core.models.ApiState;
import com.phonepe.merchant.platform.stratos.server.core.services.EvidenceService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.ValidationUtils;
import com.phonepe.olympus.im.client.annotations.AccessAllowed;
import com.phonepe.olympus.im.models.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import io.dropwizard.auth.Auth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.glassfish.jersey.media.multipart.FormDataBodyPart;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;
import org.glassfish.jersey.media.multipart.FormDataParam;

import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import java.io.InputStream;

import static com.phonepe.olympus.im.models.core.CoreConstants.BEARER;

@Slf4j
@Path("/v1")
@Tag(name = "Evidence Management APIs")
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
@SecurityScheme(name = BEARER, type = SecuritySchemeType.APIKEY, in = SecuritySchemeIn.HEADER, paramName = "Authorization")
@SecurityScheme(name = Constants.END_USER_TOKEN, type = SecuritySchemeType.APIKEY, in = SecuritySchemeIn.HEADER, paramName = "x-enduser-token")
@SecurityRequirement(name = BEARER)
@SecurityRequirement(name = Constants.END_USER_TOKEN)
public class EvidenceManagementResource {
    private final EvidenceService evidenceService;
    @POST
    @ExceptionMetered
    @RolesAllowed("evidences_upload")
    @Path("/disputes/evidences/upload")
    @Operation(summary = "Upload evidence for given dispute",
        requestBody = @RequestBody(
                content = {
                        @Content(mediaType = MediaType.MULTIPART_FORM_DATA,
                        schema = @Schema(implementation = MultiPartEvidencesRequest.class))
                }
        ))
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Produces(MediaType.APPLICATION_JSON)
    @ApiKillerMeta(tags = {ApiState.ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public EvidenceDetail uploadEvidence(
            @Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
            @HeaderParam("x-enduser-token") @NotNull @Parameter(required = true, hidden = true) String userAuthToken,
            @FormDataParam("payload") FormDataBodyPart payload,
            @FormDataParam("file") final FormDataContentDisposition fileMetaData,
            @FormDataParam("file") final InputStream inputStream
            ) {
        payload.setMediaType(MediaType.APPLICATION_JSON_TYPE);
        EvidenceUploadPayload evidencePayload = payload.getValueAs(EvidenceUploadPayload.class);

        ValidationUtils.validateMerchantId(userAuthToken,evidencePayload.getMerchantId());
        return evidenceService.upload(evidencePayload, inputStream,fileMetaData);
    }

    @POST
    @ExceptionMetered
    @RolesAllowed("evidences_inspect")
    @Path("/disputes/evidences/listAll")
    @Operation(summary = "Fetch list of evidences")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ApiKillerMeta(tags = {ApiState.ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public EvidenceResponse listEvidence(
            @Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
            @HeaderParam("x-enduser-token") @NotNull @Parameter(required = true, hidden = true) String userAuthToken,
            @Valid @NotNull final EvidenceFilterParams evidenceFilterParams) {
        ValidationUtils.validateMerchantId(userAuthToken,evidenceFilterParams.getMerchantId());
        return evidenceService.listEvidences(evidenceFilterParams);
    }

    @DELETE
    @ExceptionMetered
    @RolesAllowed("evidences_delete")
    @Path("/disputes/evidences")
    @Operation(summary = "Delete given evidence")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ApiKillerMeta(tags = {ApiState.ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public EvidenceDetail deleteEvidence(
            @Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
            @HeaderParam("x-enduser-token") @NotNull @Parameter(required = true, hidden = true) String userAuthToken,
            @Valid @NotNull final EvidenceIdentifierParams evidenceIdentifierParams) {
        ValidationUtils.validateMerchantId(userAuthToken,evidenceIdentifierParams.getMerchantId());
        return evidenceService.delete(evidenceIdentifierParams);
    }


}
