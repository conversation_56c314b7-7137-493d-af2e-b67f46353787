package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils;

import com.phonepe.merchant.platform.stratos.models.accountingevents.AccountingEvent;
import com.phonepe.merchant.platform.stratos.models.accountingevents.AccountingEventType;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.models.files.responses.FileUploadResponse;
import com.phonepe.merchant.platform.stratos.server.core.clients.kratos.KratosRecommendedAction;
import com.phonepe.merchant.platform.stratos.server.core.events.BaseEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.PulseStatus;
import com.phonepe.merchant.platform.stratos.server.core.events.type.AccountingEventStatus;
import com.phonepe.merchant.platform.stratos.server.core.events.type.CallbackEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.DisputeActionFailureEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.DisputeActionSuccessEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.DisputeSignalEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.FoxtrotChargebackAccountingEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.KratosRecommendedActionEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.NotificationErrorEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.P2pmToaPulseEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.RefundActionEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.SidelineSkippedEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.ToaRetryEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.ToaStatusEvent;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.File;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.FileState;
import com.phonepe.merchant.platform.stratos.server.core.models.FileType;
import com.phonepe.merchant.platform.stratos.server.core.models.primus.PrimusFileState;
import com.phonepe.merchant.platform.stratos.server.core.queue.ActionType;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.CallbackMessage;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.ToaProcessorMessage;
import com.phonepe.merchant.platform.stratos.server.core.utils.AuthorizationUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.files.events.EOFEvent;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.files.events.FileProcessStateEvent;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.files.events.FileUploadEvent;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.udir.events.UdirOutgoingCallBackEvent;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.udir.events.UdirOutgoingComplaintRequestEvent;
import com.phonepe.models.payments.upi.udircomplaint.UdirOutgoingComplaintRequest;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import com.phonepe.olympus.im.models.user.UserDetails;
import com.phonepe.payments.upiclientmodel.complaint.UPIClientOutgoingComplaintResponse;
import com.phonepe.ruleengine.model.integration.FraudAction;
import java.util.Optional;
import lombok.experimental.UtilityClass;
import lombok.val;

@UtilityClass
public class FoxtrotEventUtils {

    public FoxtrotChargebackAccountingEvent toChargebackAccountingEvent(
        final Dispute dispute,
        final DisputeWorkflow disputeWorkflow,
        final AccountingEvent accountingEvent,
        final AccountingEventStatus accountingEventStatus) {

        return toChargebackAccountingEvent(
            dispute, disputeWorkflow,
            accountingEvent.getHeader().getEventType(),
            accountingEvent.getHeader().getTransactionId(),
            accountingEvent.getTransaction().getAmount(),
            accountingEventStatus);
    }

    public FoxtrotChargebackAccountingEvent toChargebackAccountingEvent(
        final Dispute dispute,
        final DisputeWorkflow disputeWorkflow,
        final AccountingEventType eventType,
        final String eventId,
        final long eventAmount,
        final AccountingEventStatus accountingEventStatus) {

        return FoxtrotChargebackAccountingEvent.builder()
            .disputeId(dispute.getDisputeId())
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .disputeWorkflowVersion(disputeWorkflow.getDisputeWorkflowVersion())
            .disputeType(dispute.getDisputeType())
            .disputeStage(disputeWorkflow.getDisputeStage())
            .transactionReferenceId(dispute.getTransactionReferenceId())
            .merchantId(dispute.getMerchantId())
            .merchantTransactionId(dispute.getMerchantTransactionId())
            .instrumentTransactionId(dispute.getInstrumentTransactionId())
            .disputeReferenceId(dispute.getDisputeReferenceId())
            .rrn(dispute.getRrn())
            .transactionAmount(dispute.getTransactionAmount())
            .disputedAmount(disputeWorkflow.getDisputedAmount())
            .chargebackAccountingEventType(eventType)
            .chargebackAccountingEventId(eventId)
            .chargebackAccountingEventAmount(eventAmount)
            .accountingEventStatus(accountingEventStatus)
            .build();
    }

    public DisputeActionSuccessEvent toDisputeActionSuccessEvent(
        final Dispute updatedDispute,
        final DisputeWorkflow updatedDisputeWorkflow, final DisputeWorkflowState fromState) {

        return DisputeActionSuccessEvent.builder()
            .disputeAmount(updatedDisputeWorkflow.getDisputedAmount())
            .disputeCategory(updatedDispute.getDisputeCategory())
            .acceptedAmount(Optional.of(updatedDisputeWorkflow)
                .filter(FinancialDisputeWorkflow.class::isInstance)
                .map(
                    disputeWorkflow -> ((FinancialDisputeWorkflow) disputeWorkflow).getAcceptedAmount())
                .orElse(0L)
            )
            .disputeIssuer(updatedDispute.getDisputeIssuer())
            .disputeSourceId(updatedDisputeWorkflow.getDisputeSourceId())
            .disputeWorkflowEvent(updatedDisputeWorkflow.getCurrentEvent())
            .fromState(fromState)
            .toState(updatedDisputeWorkflow.getCurrentState())
            .disputeId(updatedDispute.getDisputeId())
            .disputeStage(updatedDisputeWorkflow.getDisputeStage())
            .disputeType(updatedDisputeWorkflow.getDisputeType())
            .disputeWorkflowId(updatedDisputeWorkflow.getDisputeWorkflowId())
            .gandalfUserId(updatedDisputeWorkflow.getGandalfUserId())
            .disputeReferenceId(updatedDispute.getDisputeReferenceId())
            .disputeWorkflowVersion(updatedDisputeWorkflow.getDisputeWorkflowVersion())
            .transactionAmount(updatedDispute.getTransactionAmount())
            .instrumentTransactionId(updatedDispute.getInstrumentTransactionId())
            .transactionReferenceId(updatedDisputeWorkflow.getTransactionReferenceId())
            .build();
    }

    public DisputeActionFailureEvent toDisputeActionFailedEvent(
            final DisputeWorkflow updatedDisputeWorkflow, final DisputeWorkflowState toState,String errorContext,String errorCode) {

        return DisputeActionFailureEvent.builder()
                .transactionReferenceId(updatedDisputeWorkflow.getTransactionReferenceId())
                .disputeWorkflowId(updatedDisputeWorkflow.getDisputeWorkflowId())
                .disputeWorkflowEvent(updatedDisputeWorkflow.getCurrentEvent())
                .fromState(updatedDisputeWorkflow.getCurrentState())
                .toState(toState)
                .errorContext(errorContext)
                .errorCode(errorCode)
                .build();
    }

    public UdirOutgoingComplaintRequestEvent toUdirOutgoingComplaintRequestEvent(
        final UdirOutgoingComplaintRequest complaintRequest) {
        return UdirOutgoingComplaintRequestEvent.builder()
            .complaintId(complaintRequest.getComplaintId())
            .requestType(complaintRequest.getRequestType())
            .adjAmount(complaintRequest.getAdjAmount())
            .transactionReferenceId(complaintRequest.getTransactionId())
            .requestDate(complaintRequest.getRequestDate())
            .build();
    }

    public UdirOutgoingCallBackEvent toUdirOutgoingCallBackEvent(
        final UPIClientOutgoingComplaintResponse upiClientOutgoingComplaintResponse,
        final String transactionReferenceId
    ) {

        return UdirOutgoingCallBackEvent.builder()
            .complaintId(upiClientOutgoingComplaintResponse.getComplaintId())
            .state(upiClientOutgoingComplaintResponse.getState())
            .adjFlag(upiClientOutgoingComplaintResponse.getAdjFlag())
            .adjCode(upiClientOutgoingComplaintResponse.getAdjCode())
            .transactionReferenceId(transactionReferenceId)
            .type(upiClientOutgoingComplaintResponse.getType())
            .success(upiClientOutgoingComplaintResponse.isSuccess())
            .resultType(upiClientOutgoingComplaintResponse.getResultType())
            .errorCode(upiClientOutgoingComplaintResponse.getErrorCode())
            .requestDate(upiClientOutgoingComplaintResponse.getRequestDate())
            .crn(upiClientOutgoingComplaintResponse.getCrn())
            .adjRemark(upiClientOutgoingComplaintResponse.getAdjRemark())
            .adjReason(upiClientOutgoingComplaintResponse.getAdjReason())
            .respAdjAmount(upiClientOutgoingComplaintResponse.getRespAdjAmount())
            .build();
    }

    public static BaseEvent toToaPulseEvent(ToaProcessorMessage toaProcessorMessage, PulseStatus pulseStatus, StratosErrorCodeKey stratosErrorCode) {
        return P2pmToaPulseEvent.builder()
            .pulseEventId(toaProcessorMessage.getEventId())
            .errorCode(stratosErrorCode)
            .transactionReferenceId(toaProcessorMessage.getTransactionId())
            .pulseStatus(pulseStatus)
            .build();
    }

    public static BaseEvent toToaRetryEvent(DisputeWorkflow disputeWorkflow, UserAuthDetails userAuthDetails, long retryCount) {
        return ToaRetryEvent.builder()
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .disputeId(disputeWorkflow.getDisputeId())
            .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
            .currentDisputeEvent(disputeWorkflow.getCurrentEvent())
            .disputeType(disputeWorkflow.getDisputeType())
            .disputeAmount(disputeWorkflow.getDisputedAmount())
            .gandalfUserId(userAuthDetails.getUserDetails().getUserId())
            .retryCount(retryCount)
            .build();
    }
    public FileUploadEvent toFileUploadEvent(
        final FileUploadResponse fileUploadResponse,
        final FileType fileType,
        final int fileRows,
        final FileState fileState,
        final DisputeType disputeType,
        final UserDetails userDetails,String message){

        return FileUploadEvent.builder()
            .fileId(Optional.ofNullable(fileUploadResponse.getId()).orElse("Null"))
            .fileName(fileUploadResponse.getFileName())
            .fileType(fileType)
            .fileRows(fileRows)
            .fileState(fileState)
            .disputeType(disputeType)
            .userId(userDetails.getUserId())
            .userType(AuthorizationUtils.getUserType(userDetails))
            .message(message)
            .build();

    }

    public FileProcessStateEvent toFileProcessStateEvent(
        File file
    ){
        return FileProcessStateEvent.builder()
            .fileId(file.getFileId())
            .fileName(file.getFileName())
            .fileState(file.getFileState())
            .fileType(file.getFileType())
            .disputeType(file.getDisputeType())
            .createdAt(file.getCreatedAt())
            .updatedAt(file.getUpdatedAt())
            .build();
    }

    public static BaseEvent toSidelineSkippedEvent(StratosErrorCodeKey errorCode, String message, ActionType actionType) {
        return SidelineSkippedEvent.builder()
            .errorCode(errorCode)
            .message(message)
            .actionType(actionType)
            .build();
    }

    public EOFEvent toEOFEvent(
        File file, String reason, PrimusFileState primusFileState
    ){
        return EOFEvent.builder()
            .fileId(file.getFileId())
            .fileName(file.getFileName())
            .fileType(file.getFileType())
            .disputeType(file.getDisputeType())
            .createdAt(file.getCreatedAt())
            .updatedAt(file.getUpdatedAt())
            .reason(reason)
            .primusFileState(primusFileState)
            .build();
    }

    public KratosRecommendedActionEvent toKratosRecommendedActionEvent(DisputeWorkflow disputeWorkflow,
        KratosRecommendedAction recommendedAction, FraudAction fraudAction, String requestId){
        return KratosRecommendedActionEvent.builder()
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .requestId(requestId)
            .responseCode(TransformationUtils.getSafeEnumName(fraudAction.getResponseCode()))
            .recommendedAction(recommendedAction)
            .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
            .transactionAmount(disputeWorkflow.getDisputedAmount())
            .currentEvent(disputeWorkflow.getCurrentEvent())
            .currentState(disputeWorkflow.getCurrentState())
            .disputeType(disputeWorkflow.getDisputeType())
            .disputeStage(disputeWorkflow.getDisputeStage())
            .build();
    }



    public RefundActionEvent toRefundActionEvent(
        String disputeWorkflowId, String transactionReferenceId,String refundId,
        String refundAction,String errorCode, String errorContext
    ){
        return RefundActionEvent.builder()
            .refundId(refundId)
            .refundAction(refundAction)
            .errorContext(errorContext)
            .errorCode(errorCode)
            .disputeWorkflowId(disputeWorkflowId)
            .transactionReferenceId(transactionReferenceId)
            .build();
    }

    public ToaStatusEvent toToaStatusEvent(final DisputeWorkflow disputeWorkflow, final String toaState) {
        val dispute = disputeWorkflow.getDispute();
        return ToaStatusEvent.builder()
                .communicationId(dispute.getDisputeReferenceId())
                .toaState(toaState)
                .amount(disputeWorkflow.getDisputedAmount())
                .creationDate(dispute.getCreatedAt().toString())
                .updationDate(dispute.getUpdatedAt().toString())
                .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
                .build();
    }

    public BaseEvent toCallbackEvent(
            CallbackMessage message, String identifier, String serviceId, boolean success) {
        Dispute dispute = message.getDispute();
        return CallbackEvent.builder()
            .callbackType(message.getCallbackType())
            .success(success)
            .previousState(message.getPreviousState())
            .currentStatus(message.getCurrentStatus())
            .disputeId(dispute.getDisputeId())
            .transactionReferenceId(dispute.getTransactionReferenceId())
            .disputeType(dispute.getDisputeType())
            .currentDisputeStage(dispute.getCurrentDisputeStage())
            .merchantId(dispute.getMerchantId())
            .entityIdentifier(identifier)
            .serviceId(serviceId)
            .eventTime(message.getEventTime())
            .build();
    }

    public BaseEvent toSignalEvent(
            long signalAmount, DisputeSignalEvent.SignalType signalType,String disputeId, DisputeStage disputeStage,
            DisputeType disputeType, String disputeWorkflowId, String transactionReferenceId,
            DisputeWorkflowState currentState,DisputeWorkflowEvent workflowEvent

    ) {
        return DisputeSignalEvent.builder()
                .signalAmount(signalAmount)
                .signalType(signalType)
                .disputeId(disputeId)
                .disputeStage(disputeStage)
                .disputeType(disputeType)
                .disputeWorkflowId(disputeWorkflowId)
                .transactionReferenceId(transactionReferenceId)
                .workflowEvent(workflowEvent)
                .fromState(currentState)
                .build();
    }

    public BaseEvent getSignalEvent(DisputeWorkflow storedDisputeWorkflow,DisputeWorkflowEvent workflowEvent,DisputeSignalEvent.SignalType signalType) {
        return FoxtrotEventUtils.toSignalEvent(storedDisputeWorkflow.getDisputedAmount(), signalType,
                storedDisputeWorkflow.getDisputeId(), storedDisputeWorkflow.getDisputeStage(),
                storedDisputeWorkflow.getDisputeType(), storedDisputeWorkflow.getDisputeWorkflowId(),
                storedDisputeWorkflow.getTransactionReferenceId(), storedDisputeWorkflow.getCurrentState(), workflowEvent);
    }

    public BaseEvent getNotificationErrorEvent(StratosErrorCodeKey errorCodeKey, String message){
        return NotificationErrorEvent.builder()
            .stratosErrorCodeKey(errorCodeKey)
            .message(message)
            .build();
    }
}
