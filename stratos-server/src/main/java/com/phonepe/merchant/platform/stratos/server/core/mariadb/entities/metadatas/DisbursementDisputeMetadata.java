package com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas;

import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.PrimaryKey;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeMetadataType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.envers.Audited;

import javax.persistence.Column;
import javax.persistence.Entity;
import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@Audited
@ToString
@DynamicInsert
@DynamicUpdate
@NoArgsConstructor
@FieldNameConstants
public class DisbursementDisputeMetadata extends DisputeMetadata {

    private static final long serialVersionUID = -457764526925051254L;

    @Column(name = "disbursement_transaction_id", columnDefinition = "varchar(32)")
    private String disbursementTransactionId;

    @SuppressWarnings("java:S107")
    public DisbursementDisputeMetadata(
        final PrimaryKey key,
        final String disputeMetadataId,
        final String disputeWorkflowId,
        final String transactionReferenceId,
        final LocalDateTime createdAt,
        final LocalDateTime updatedAt,
        final DisputeMetadataType disputeMetadataType,
        final String disbursementTransactionId) {
        super(key, disputeMetadataId,
            disputeWorkflowId, transactionReferenceId,
                disputeMetadataType,
            createdAt, updatedAt);
        this.disbursementTransactionId = disbursementTransactionId;
    }

}
