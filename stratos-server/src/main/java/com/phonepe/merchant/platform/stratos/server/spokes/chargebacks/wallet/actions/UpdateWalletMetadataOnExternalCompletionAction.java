package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.actions;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.phonepe.merchant.platform.stratos.models.commons.TransitionContext;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.RefundContext;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.clients.PaymentsTxnlClient;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.WalletDisputeMetadataRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.queue.actors.CallbackActor;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.DisputeWorkflowMessage;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.UpdateDisputeStateBaseAction;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.statechange.StateChangeHandlerActor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.ValidationUtils;
import com.phonepe.models.payments.pay.TransactionDetail;
import java.util.Map;
import java.util.Objects;
import lombok.SneakyThrows;
import org.springframework.statemachine.StateContext;

public class UpdateWalletMetadataOnExternalCompletionAction extends UpdateDisputeStateBaseAction{

    private final WalletDisputeMetadataRepository walletDisputeMetadataRepository;
    private final PaymentsTxnlClient paymentsTxnlClient;
    private final Provider<StateChangeHandlerActor> stateChangeHandlerProvider;

    @Inject
    public UpdateWalletMetadataOnExternalCompletionAction(final DisputeService disputeService,
        final DisputeWorkflowRepository disputeWorkflowRepository,
        final EventIngester eventIngester,
        final CallbackActor callbackActor,
        final WalletDisputeMetadataRepository walletDisputeMetadataRepository,
        final PaymentsTxnlClient paymentsTxnlClient,
        final Provider<StateChangeHandlerActor> stateChangeHandlerProvider) {
        super(disputeService, disputeWorkflowRepository, eventIngester, callbackActor);
        this.walletDisputeMetadataRepository = walletDisputeMetadataRepository;
        this.paymentsTxnlClient = paymentsTxnlClient;
        this.stateChangeHandlerProvider = stateChangeHandlerProvider;
    }

    @Override
    public void transition(final DisputeWorkflow disputeWorkflow,
        final StateContext<DisputeWorkflowState, DisputeWorkflowEvent> stateContext) {
        final RefundContext refundContext = stateContext.getExtendedState().get(TransitionContext.class, RefundContext.class);
        if (Objects.isNull(refundContext)) {
            throw DisputeExceptionUtil.error(
                StratosErrorCodeKey.VALIDATION_FAILURE, Map.of(Constants.MESSAGE,
                    "Refund context should not be null"));
        }
        validateRefundTransactionId(refundContext.getRefundTransactionId());
        walletDisputeMetadataRepository.updateDisbursementTransactionId(refundContext.getOriginalTransactionId(),
            disputeWorkflow.getDisputeWorkflowId(), refundTransaction ->{
            refundTransaction.setDisbursementTransactionId(refundContext.getRefundTransactionId());
            return refundTransaction;
        });
    }

    private void validateRefundTransactionId(final String refundTransactionId) {
            final TransactionDetail transactionDetails = paymentsTxnlClient
                .getTransactionDetails(refundTransactionId).getData();
            ValidationUtils.validateExternalRefundTransactionIdForWallet(transactionDetails);
    }

    @Override
    @SneakyThrows
    public void postTransition(final DisputeWorkflow disputeWorkflow) {

        final var disputeWorkflowMessage = DisputeWorkflowMessage.builder()
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
            .disputeWorkflowEvent(DisputeWorkflowEvent.ACCEPTED_CHARGEBACK)
            .build();

        stateChangeHandlerProvider.get().publish(disputeWorkflowMessage);
    }

}
