package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.upi.statemachines;

import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.TransitionLockCommand;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.registries.StateMachineListenerRegistry;
import com.phonepe.merchant.platform.stratos.server.core.registries.keys.DisputeStateMachineRegistryKey;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.DisputeStateMachine;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.comments.impls.MandatoryCommentUpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.comments.impls.OptionalCommentUpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.AcceptDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.AcceptPartialDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.FraudCheckDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.ResetChargebackAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.UpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.interceptors.impls.DisputeErrorHandlingInterceptor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.ApproveRecoverChargebackAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.ApproveRecoveryReversalChargebackAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.BlockRefundAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.RaiseChargebackRecoveryAccountingEventAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.RaiseChargebackRecoveryReversalAccountingEventAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.UnblockRefundAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.upi.statemachines.actions.UpiChargebackCreateEntryAction;
import java.util.EnumSet;
import java.util.Set;
import lombok.SneakyThrows;
import org.springframework.statemachine.config.builders.StateMachineConfigurationConfigurer;
import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;
import org.springframework.statemachine.config.configurers.StateConfigurer;

public abstract class UpiChargebackStateMachine extends DisputeStateMachine {

    private final UpiChargebackCreateEntryAction createEntryAction;
    private final MandatoryCommentUpdateDisputeStateAction mandatoryCommentUpdateAction;
    private final OptionalCommentUpdateDisputeStateAction optionalCommentUpdateAction;
    private final UpdateDisputeStateAction updateDisputeStateAction;
    private final UpdateDisputeStateAction updateDisputeStateActionForFraudCheck;
    private final FraudCheckDisputeAction fraudCheckUpdateDisputeAction;
    private final AcceptDisputeAction acceptDisputeAction;
    private final AcceptPartialDisputeAction acceptPartialDisputeAction;
    private final RaiseChargebackRecoveryReversalAccountingEventAction raiseReversalAccountingEventAction;
    private final ApproveRecoveryReversalChargebackAction approveRecoveryReversalChargebackAction;
    private final RaiseChargebackRecoveryAccountingEventAction raiseAccountingEventAction;
    private final ApproveRecoverChargebackAction approveRecoverChargebackAction;
    private final BlockRefundAction blockRefundAction;
    private final UnblockRefundAction unblockRefundAction;
    private final ResetChargebackAction resetChargebackAction;

    @Override
    public abstract DisputeStateMachineRegistryKey getRegistryKey();

    protected abstract Set<DisputeWorkflowState> endStates();

    @Override
    @SneakyThrows
    protected void configure(
        final StateMachineStateConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> states) {
        StateConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> stateConfigurer = states
            .withStates()
            .initial(DisputeWorkflowState.RECEIVED)
            .states(EnumSet.allOf(DisputeWorkflowState.class))
            .end(DisputeWorkflowState.CHARGEBACK_ABSORB_REVERSED)
            .end(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT_ACCEPTED)
            .end(DisputeWorkflowState.CHARGEBACK_CANCELLED);

        endStates().forEach((stateConfigurer::end));
    }


    @Override
    @SneakyThrows
    protected void configure(
        final StateMachineConfigurationConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> config) {
        config
            .withConfiguration()
            .autoStartup(false);
    }

    @SuppressWarnings("java:S107")
    protected UpiChargebackStateMachine(
        final TransitionLockCommand transitionLockCommand,
        final DisputeErrorHandlingInterceptor disputeErrorHandlingInterceptor,
        final UpiChargebackCreateEntryAction createEntryAction,
        final MandatoryCommentUpdateDisputeStateAction mandatoryCommentUpdateAction,
        final OptionalCommentUpdateDisputeStateAction optionalCommentUpdateAction,
        final UpdateDisputeStateAction updateDisputeStateAction,
        final FraudCheckDisputeAction fraudCheckUpdateDisputeAction,
        final AcceptDisputeAction acceptDisputeAction,
        final AcceptPartialDisputeAction acceptPartialDisputeAction,
        final RaiseChargebackRecoveryReversalAccountingEventAction raiseReversalAccountingEventAction,
        final ApproveRecoveryReversalChargebackAction approveRecoveryReversalChargebackAction,
        final RaiseChargebackRecoveryAccountingEventAction raiseAccountingEventAction,
        final ApproveRecoverChargebackAction approveRecoverChargebackAction,
        final BlockRefundAction blockRefundAction,
        final UnblockRefundAction unblockRefundAction,
        final ResetChargebackAction resetChargebackAction,
        final StateMachineListenerRegistry<DisputeWorkflowState, DisputeWorkflowEvent> stateMachineListenerRegistry
        ) {
        super(transitionLockCommand, disputeErrorHandlingInterceptor,stateMachineListenerRegistry);
        this.createEntryAction=createEntryAction;
        this.mandatoryCommentUpdateAction=mandatoryCommentUpdateAction;
        this.optionalCommentUpdateAction=optionalCommentUpdateAction;
        this.updateDisputeStateAction=updateDisputeStateAction;
        this.fraudCheckUpdateDisputeAction=fraudCheckUpdateDisputeAction;
        this.acceptDisputeAction=acceptDisputeAction;
        this.raiseReversalAccountingEventAction=raiseReversalAccountingEventAction;
        this.acceptPartialDisputeAction=acceptPartialDisputeAction;
        this.approveRecoveryReversalChargebackAction=approveRecoveryReversalChargebackAction;
        this.raiseAccountingEventAction=raiseAccountingEventAction;
        this.approveRecoverChargebackAction=approveRecoverChargebackAction;
        this.blockRefundAction=blockRefundAction;
        this.unblockRefundAction=unblockRefundAction;
        this.resetChargebackAction=resetChargebackAction;
        this.updateDisputeStateActionForFraudCheck = updateDisputeStateAction;



    }

    @SneakyThrows
    protected StateMachineTransitionConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> baseTransitions(
        final StateMachineTransitionConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> transitions){

        return transitions.withExternal()
            .source(DisputeWorkflowState.RECEIVED)
            .target(DisputeWorkflowState.RECEIVED)
            .event(DisputeWorkflowEvent.CREATE_ENTRY)
            .action(createEntryAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECEIVED)
            .target(DisputeWorkflowState.INTERNAL_MID_REPRESENTMENT_REQUIRED)
            .event(DisputeWorkflowEvent.INTERNAL_MID_REQUEST_REPRESENTMENT)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.INTERNAL_MID_REPRESENTMENT_REQUIRED)
            .target(DisputeWorkflowState.NPCI_REPRESENTMENT_COMPLETED)
            .event(DisputeWorkflowEvent.COMPLETE_NPCI_REPRESENTMENT)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECEIVED)
            .target(DisputeWorkflowState.REPRESENTMENT_REQUIRED)
            .event(DisputeWorkflowEvent.REQUEST_REPRESENTMENT)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REPRESENTMENT_REQUIRED)
            .target(DisputeWorkflowState.NPCI_REPRESENTMENT_COMPLETED)
            .event(DisputeWorkflowEvent.COMPLETE_NPCI_REPRESENTMENT)
            .action(unblockRefundAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.NPCI_REPRESENTMENT_COMPLETED)
            .target(DisputeWorkflowState.CHARGEBACK_CANCELLED)
            .event(DisputeWorkflowEvent.RESET_CHARGEBACK)
            .action(resetChargebackAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECEIVED)
            .target(DisputeWorkflowState.REFUND_BLOCKED)
            .event(DisputeWorkflowEvent.BLOCK_REFUND)
            .action(blockRefundAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REFUND_BLOCKED)
            .target(DisputeWorkflowState.FULFILMENT_DOCUMENTS_RECEIVED)
            .event(DisputeWorkflowEvent.RECEIVE_FULFILMENT_DOCUMENTS)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.FULFILMENT_DOCUMENTS_RECEIVED)
            .target(DisputeWorkflowState.NPCI_REPRESENTMENT_COMPLETED)
            .event(DisputeWorkflowEvent.COMPLETE_NPCI_REPRESENTMENT)
            .action(unblockRefundAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.NPCI_PARTIAL_REPRESENTMENT_COMPLETED)
            .target(DisputeWorkflowState.CHARGEBACK_CANCELLED)
            .event(DisputeWorkflowEvent.RESET_CHARGEBACK)
            .action(resetChargebackAction)


            .and()
            .withExternal()
            .source(DisputeWorkflowState.REFUND_BLOCKED)
            .target(DisputeWorkflowState.FRAUD_REJECTED)
            .event(DisputeWorkflowEvent.FRAUD_REJECT)
            .action(updateDisputeStateActionForFraudCheck)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.FRAUD_REJECTED)
            .target(DisputeWorkflowState.NPCI_REPRESENTMENT_COMPLETED)
            .event(DisputeWorkflowEvent.FRAUD_REJECTED_TO_REPRESENTMENT) // Manual action
            .action(updateDisputeStateActionForFraudCheck)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REFUND_BLOCKED)
            .target(DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED)
            .event(DisputeWorkflowEvent.RECEIVE_PARTIAL_FULFILMENT_DOCUMENTS)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED)
            .target(DisputeWorkflowState.SUSPECTED_FRAUD)
            .event(DisputeWorkflowEvent.SUSPECTED_FRAUD)
            .action(updateDisputeStateActionForFraudCheck)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED)
            .target(DisputeWorkflowState.FRAUD_REJECTED)
            .event(DisputeWorkflowEvent.FRAUD_REJECT)
            .action(updateDisputeStateActionForFraudCheck)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED)
            .target(DisputeWorkflowState.NPCI_PARTIAL_REPRESENTMENT_COMPLETED)
            .event(DisputeWorkflowEvent.COMPLETE_NPCI_PARTIAL_REPRESENTMENT)
            .action(acceptPartialDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REFUND_BLOCKED)
            .target(DisputeWorkflowState.MERCHANT_NOT_RESPONDED_WITHIN_TTL)
            .event(DisputeWorkflowEvent.NO_RESPONSE_FROM_MERCHANT_WITHIN_TTL)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_NOT_RESPONDED_WITHIN_TTL)
            .target(DisputeWorkflowState.NPCI_REPRESENTMENT_COMPLETED)
            .event(DisputeWorkflowEvent.COMPLETE_NPCI_REPRESENTMENT)
            .action(unblockRefundAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_NOT_RESPONDED_WITHIN_TTL)
            .target(DisputeWorkflowState.NPCI_PARTIAL_REPRESENTMENT_COMPLETED)
            .event(DisputeWorkflowEvent.COMPLETE_NPCI_PARTIAL_REPRESENTMENT)
            .action(acceptPartialDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_NOT_RESPONDED_WITHIN_TTL)
            .target(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
            .event(DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK)
            .action(fraudCheckUpdateDisputeAction)


            .and()
            .withExternal()
            .source(DisputeWorkflowState.NPCI_ACCEPTANCE_COMPLETED)
            .target(DisputeWorkflowState.CHARGEBACK_CANCELLED)
            .event(DisputeWorkflowEvent.RESET_CHARGEBACK)
            .action(resetChargebackAction)


            .and()
            .withExternal()
            .source(DisputeWorkflowState.REFUND_BLOCKED)
            .target(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
            .event(DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK)
            .action(fraudCheckUpdateDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
            .target(DisputeWorkflowState.NPCI_ACCEPTANCE_COMPLETED)
            .event(DisputeWorkflowEvent.COMPLETE_NPCI_ACCEPTANCE)
            .action(acceptDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
            .target(DisputeWorkflowState.SUSPECTED_FRAUD)
            .event(DisputeWorkflowEvent.SUSPECTED_FRAUD)
            .action(updateDisputeStateActionForFraudCheck)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.SUSPECTED_FRAUD)
            .target(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
            .event(DisputeWorkflowEvent.SUSPECTED_FRAUD_TO_ACCEPTANCE)
            .action(updateDisputeStateActionForFraudCheck)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.SUSPECTED_FRAUD)
            .target(DisputeWorkflowState.FRAUD_REJECTED)
            .event(DisputeWorkflowEvent.FRAUD_REJECT)
            .action(updateDisputeStateActionForFraudCheck)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
            .target(DisputeWorkflowState.FRAUD_REJECTED)
            .event(DisputeWorkflowEvent.FRAUD_REJECT)
            .action(updateDisputeStateActionForFraudCheck)

            // Absorb flow
            .and()
            .withExternal()
            .source(DisputeWorkflowState.ABSORB_CHARGEBACK_REQUESTED)
            .target(DisputeWorkflowState.ABSORB_CHARGEBACK_APPROVED)
            .event(DisputeWorkflowEvent.APPROVE_ABSORB_CHARGEBACK)
            .action(optionalCommentUpdateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.ABSORB_CHARGEBACK_APPROVED)
            .target(DisputeWorkflowState.CHARGEBACK_ABSORBED)
            .event(DisputeWorkflowEvent.ABSORB_CHARGEBACK)
            .action(optionalCommentUpdateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.CHARGEBACK_ABSORBED)
            .target(DisputeWorkflowState.CHARGEBACK_ABSORB_REVERSED)
            .event(DisputeWorkflowEvent.REVERSE_ABSORB_CHARGEBACK)
            .action(mandatoryCommentUpdateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.ABSORB_CHARGEBACK_REQUESTED)
            .target(DisputeWorkflowState.ABSORB_CHARGEBACK_REJECTED)
            .event(DisputeWorkflowEvent.REJECT_ABSORB_CHARGEBACK)
            .action(mandatoryCommentUpdateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.ABSORB_CHARGEBACK_REJECTED)
            .target(DisputeWorkflowState.ABSORB_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_ABSORB_CHARGEBACK)
            .action(mandatoryCommentUpdateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.ABSORB_CHARGEBACK_REJECTED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_RECOVER_CHARGEBACK)
            .action(optionalCommentUpdateAction)

            // Recovery
            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECOVER_CHARGEBACK_REQUESTED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_APPROVED)
            .event(DisputeWorkflowEvent.APPROVE_RECOVER_CHARGEBACK)
            .action(approveRecoverChargebackAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECOVER_CHARGEBACK_APPROVED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_RAISED)
            .event(DisputeWorkflowEvent.RAISE_RECOVER_CHARGEBACK_EVENT)
            .action(raiseAccountingEventAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_RAISED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_ACCEPTED)
            .event(DisputeWorkflowEvent.ACCEPT_RECOVER_CHARGEBACK_EVENT)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_ACCEPTED)
            .target(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_REVERSAL_OF_RECOVERED_CHARGEBACK)
            .action(mandatoryCommentUpdateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_REQUESTED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_ACCEPTED)
            .event(DisputeWorkflowEvent.REJECT_REVERSAL_OF_RECOVERED_CHARGEBACK)
            .action(mandatoryCommentUpdateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_REQUESTED)
            .target(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_APPROVED)
            .event(DisputeWorkflowEvent.APPROVE_REVERSAL_OF_RECOVERED_CHARGEBACK)
            .action(approveRecoveryReversalChargebackAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_APPROVED)
            .target(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT_RAISED)
            .event(DisputeWorkflowEvent.RAISE_REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT)
            .action(raiseReversalAccountingEventAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT_RAISED)
            .target(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT_ACCEPTED)
            .event(DisputeWorkflowEvent.ACCEPT_REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECOVER_CHARGEBACK_REQUESTED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_REJECTED)
            .event(DisputeWorkflowEvent.REJECT_RECOVER_CHARGEBACK)
            .action(mandatoryCommentUpdateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECOVER_CHARGEBACK_REJECTED)
            .target(DisputeWorkflowState.ABSORB_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_ABSORB_CHARGEBACK)
            .action(mandatoryCommentUpdateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECOVER_CHARGEBACK_REJECTED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_RECOVER_CHARGEBACK)
            .action(optionalCommentUpdateAction)
            .and();


    }

}
