package com.phonepe.merchant.platform.stratos.server.spokes.toa.notionalcredit.statemachine;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.TransitionLockCommand;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.registries.StateMachineListenerRegistry;
import com.phonepe.merchant.platform.stratos.server.core.registries.keys.DisputeStateMachineRegistryKey;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.UpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.interceptors.impls.DisputeErrorHandlingInterceptor;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.common.BaseToaStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.common.actions.ToaCompletionAction;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.common.actions.ToaCreateEntryAction;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.common.actions.ToaMandatoryCommentAction;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.common.actions.ToaPayPostEntryAction;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.common.actions.ToaPayStatusCheckAction;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.common.actions.ToaRetryAction;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.common.actions.ToaStateUpdateAction;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.notionalcredit.actions.ToaOpenAction;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;

@Slf4j
@Singleton
public class NotionalCreditStateMachine extends BaseToaStateMachine {

    private final ToaOpenAction toaOpenAction;

    @Inject
    @SuppressWarnings("java:S107")
    protected NotionalCreditStateMachine(
            final TransitionLockCommand transitionLockCommand,
            final DisputeErrorHandlingInterceptor disputeErrorHandlingInterceptor,
            final UpdateDisputeStateAction updateDisputeStateAction,
            final ToaCreateEntryAction toaCreateEntryAction,
            final ToaPayStatusCheckAction toaPayStatusCheckAction,
            final ToaCompletionAction toaCompletionAction,
            final ToaPayPostEntryAction toaPayPostEntryAction,
            final ToaRetryAction toaRetryAction,
            final ToaOpenAction toaOpenAction,
            final ToaMandatoryCommentAction toaMandatoryCommentAction,
            final ToaStateUpdateAction toaStateUpdateAction,
            final StateMachineListenerRegistry<DisputeWorkflowState, DisputeWorkflowEvent> stateMachineListenerRegistry) {
        super(transitionLockCommand, disputeErrorHandlingInterceptor, updateDisputeStateAction,
                toaCreateEntryAction, toaPayStatusCheckAction, toaCompletionAction, toaPayPostEntryAction,
                toaRetryAction, toaMandatoryCommentAction, toaStateUpdateAction, stateMachineListenerRegistry);
        this.toaOpenAction = toaOpenAction;
    }


    @Override
    public DisputeStateMachineRegistryKey getRegistryKey() {
        return DisputeStateMachineRegistryKey.builder()
                .disputeType(DisputeType.NOTIONAL_CREDIT_TOA)
                .disputeStage(DisputeStage.FIRST_LEVEL)
                .disputeWorkflowVersion(DisputeWorkflowVersion.V1)
                .build();
    }

    @Override
    @SneakyThrows
    protected void extendStates(StateMachineTransitionConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> transitions) {
        transitions
                .withExternal()
                .source(DisputeWorkflowState.RECEIVED)
                .target(DisputeWorkflowState.RECEIVED)
                .event(DisputeWorkflowEvent.CREATE_ENTRY)
                .action(toaOpenAction)

                .and()
                .withExternal()
                .source(DisputeWorkflowState.RECEIVED)
                .target(DisputeWorkflowState.TOA_OPENED)
                .event(DisputeWorkflowEvent.TOA_RECEIVED_TO_OPENED)
                .action(toaStateUpdateAction)

                .and()
                .withExternal()
                .source(DisputeWorkflowState.TOA_OPENED)
                .target(DisputeWorkflowState.TOA_CLOSED)
                .event(DisputeWorkflowEvent.TOA_OPENED_TO_CLOSED)
                .action(toaStateUpdateAction)

                .and()
                .withExternal()
                .source(DisputeWorkflowState.TOA_OPENED)
                .target(DisputeWorkflowState.RECEIVED)
                .event(DisputeWorkflowEvent.TOA_OPENED_TO_INITIATED)
                .action(toaPayPostEntryAction)

                .and()
                .withExternal()
                .source(DisputeWorkflowState.TOA_FAILED_AFTER_MAX_AUTO_RETRY)
                .target(DisputeWorkflowState.TOA_COMPLETED_EXTERNALLY)
                .event(DisputeWorkflowEvent.MAX_RETRY_TO_PROCESSED_EXTERNALLY) // MANUAL ACTION
                .action(toaMandatoryCommentAction)

                .and()
                .withExternal()
                .source(DisputeWorkflowState.TOA_INITIATION_FAILED)
                .target(DisputeWorkflowState.TOA_COMPLETED_EXTERNALLY)
                .event(DisputeWorkflowEvent.INITIATION_FAILED_TO_EXTERNAL_PROCESSED)
                .action(toaMandatoryCommentAction);

    }

}
