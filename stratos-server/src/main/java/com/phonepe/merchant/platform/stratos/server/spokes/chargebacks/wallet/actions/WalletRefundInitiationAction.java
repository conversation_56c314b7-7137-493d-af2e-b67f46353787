package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.actions;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.WalletTransactionDetails;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow.Fields;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.queue.actors.CallbackActor;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.DisputeWorkflowMessage;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.RefundService;
import com.phonepe.merchant.platform.stratos.server.core.services.impls.WalletTransactionService;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.UpdateDisputeStateBaseAction;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeWorkflowUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.RefundUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.statechange.StateChangeHandlerActor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.FoxtrotEventUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.ValidationUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.model.DisputeRefundV2;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.service.WalletChargebackService;
import com.phonepe.services.refund.orchestrator.models.RefundStatus;
import java.util.Map;
import java.util.Objects;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;


@Slf4j
@Singleton
public class WalletRefundInitiationAction extends UpdateDisputeStateBaseAction {

    private final RefundService refundService;
    private final WalletChargebackService walletChargebackService;
    private final Provider<StateChangeHandlerActor> stateChangeHandlerProvider;
    private final EventIngester eventIngestor;
    protected DisputeRefundV2 disputedRefund;
    private final WalletTransactionService walletTransactionService;

    @Inject
    @SuppressWarnings("java:S107")
    public WalletRefundInitiationAction(
        DisputeService disputeService,
        DisputeWorkflowRepository disputeWorkflowRepository,
        RefundService refundService,
        WalletChargebackService walletChargebackService,
        Provider<StateChangeHandlerActor> stateChangeHandlerProvider,
        EventIngester eventIngestor, WalletTransactionService walletTransactionService,
        CallbackActor callbackActor) {
        super(disputeService, disputeWorkflowRepository, eventIngestor, callbackActor);
        this.refundService = refundService;
        this.walletChargebackService = walletChargebackService;
        this.stateChangeHandlerProvider = stateChangeHandlerProvider;
        this.eventIngestor = eventIngestor;
        this.walletTransactionService = walletTransactionService;
    }

    @Override
    protected void preTransition(final DisputeWorkflow disputeWorkflow,
        final StateContext<DisputeWorkflowState, DisputeWorkflowEvent> stateContext) {

        final String txnRefId = stateContext.getExtendedState()
            .get(Fields.transactionReferenceId, String.class);

        var dispute = disputeWorkflow.getDispute();

        try {
            Objects.requireNonNull(dispute);
            stateContext.getExtendedState().getVariables().put(Dispute.class, dispute);
            ValidationUtils.validateDisputeId(disputeWorkflow, dispute);
            ValidationUtils.validateEntryDisputeAmount(disputeWorkflow, dispute,
                disputeService);

        } catch (Exception exception) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.ROW_NOT_FOUND,
                Map.of(Constants.MESSAGE, String.format(
                    "Dispute not found against DisputeWorkflowId %s, TransactionId %s",
                    disputeWorkflow.getDisputeWorkflowId(),
                    txnRefId)));
        }

    }

    @Override
    protected void transition(final DisputeWorkflow disputeWorkflow,
        final StateContext<DisputeWorkflowState, DisputeWorkflowEvent> stateContext) {

        final var transactionId = stateContext.getExtendedState()
            .get(Fields.transactionReferenceId, String.class);

        final WalletTransactionDetails transactionDetail = walletTransactionService.getTransactionDetails(
            transactionId);
        log.info("Wallet transaction Details for dispute workflow id {} is {}", disputeWorkflow.getDisputeWorkflowId(), transactionDetail);

        final WalletTransactionDetails walletTransactionDetail = walletTransactionService.getTransactionDetailFromUpiId(
            transactionDetail.getUpiTransactionId(),
            transactionDetail.getRrn(),
            transactionDetail.getSentTime());

        log.info("Wallet transaction Details for dispute workflow id {} for upi transaction id is {}",
            disputeWorkflow.getDisputeWorkflowId(), transactionDetail);
        final Dispute dispute = stateContext.getExtendedState().get(Dispute.class, Dispute.class);
        Objects.requireNonNull(dispute);
        Objects.requireNonNull(disputeWorkflow);

        FinancialDisputeWorkflow financialDisputeWorkflow = DisputeWorkflowUtils
            .getFinancialDisputeWorkflow(disputeWorkflow);

        Objects.requireNonNull(financialDisputeWorkflow);

        disputedRefund = RefundUtils.toDisputedRefundV2(financialDisputeWorkflow, walletTransactionDetail);
        log.info("dispute refund for disputeworkflow id  {}, and transaction id {} is {}",
            disputeWorkflow.getDisputeWorkflowId(), disputeWorkflow.getTransactionReferenceId(), disputedRefund);

        validateAndInitiateRefund(disputedRefund);

    }

    private String getRefundId() {

        return walletChargebackService.generateRefundTxnId(
            disputedRefund.getDisputeWorkflow());
    }

    @SneakyThrows
    protected void validateAndInitiateRefund(DisputeRefundV2 disputedRefund) {
        String refundId = getRefundId();
        initiateRefund(disputedRefund, refundId);
        eventIngestor.generateEvent(
            FoxtrotEventUtils.toRefundActionEvent(
                disputedRefund.getDisputeWorkflow().getDisputeWorkflowId(),
                disputedRefund.getTransactionDetail().getTransactionId(), disputedRefund.getRefundId(),
                RefundStatus.INITIATED.name(), null, null)
        );
    }



    public void initiateRefund(DisputeRefundV2 disputedRefund, String refundId) {

        disputedRefund.setRefundId(refundId);
        var response = refundService.initiateRefund(disputedRefund);
        log.info("refund response for wallet: {}", response);
        if (!response.isSuccess() || response.getData().getStatus()
            .equals(RefundStatus.FAILED)) {

            log.error("Error occurred while initiating refund for txnId {}, error : {}",
                disputedRefund.getDisputeWorkflow().getTransactionReferenceId(),
                response.getData().getErrorContext());

            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INTERNAL_SERVER_ERROR,
                Map.of("error", response.getData().getErrorContext()));

        } else {

            log.info("Refund initiated Successfully for workflow {} with refundId {}",
                disputedRefund.getDisputeWorkflow(), refundId);

            walletChargebackService.updateDisputeMeta(disputedRefund.getDisputeWorkflow(), refundId);
        }
    }

    @Override
    @SneakyThrows
    protected void postTransition(final DisputeWorkflow disputeWorkflow) {

        final var disputeWorkflowMessage = DisputeWorkflowMessage.builder()
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
            .disputeWorkflowEvent(DisputeWorkflowEvent.INITIATION_COMPLETED)
            .build();

        stateChangeHandlerProvider.get().publish(disputeWorkflowMessage);
    }

}
