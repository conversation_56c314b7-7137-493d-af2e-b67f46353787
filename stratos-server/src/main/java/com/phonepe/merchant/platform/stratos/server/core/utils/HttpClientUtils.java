package com.phonepe.merchant.platform.stratos.server.core.utils;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.platform.http.v2.client.ClientFactory;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.common.ServiceEndpointProvider;
import com.phonepe.platform.http.v2.common.hub.RangerHubConfiguration;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import com.phonepe.platform.http.v2.discovery.utils.DiscoveryUtils;
import com.phonepe.platform.http.v2.executor.BaseHttpData;
import com.phonepe.platform.http.v2.executor.Consumer;
import com.phonepe.platform.http.v2.executor.HeaderPair;
import com.phonepe.platform.http.v2.executor.exception.HttpException;
import com.phonepe.platform.http.v2.executor.factory.HttpExecutorBuilderFactory;
import io.dropwizard.setup.Environment;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;
import lombok.SneakyThrows;
import lombok.experimental.UtilityClass;

@UtilityClass
public class HttpClientUtils {

    @SneakyThrows
    public HttpExecutorBuilderFactory getHttpExecutorBuilderFactory(
        final Class<?> callerClass,
        final HttpConfiguration httpConfiguration,
        final ServiceEndpointProviderFactory serviceEndpointProviderFactory,
        final Environment environment, final ObjectMapper mapper,
        final MetricRegistry metricRegistry) {

        final var client = ClientFactory.newHttpClientBuilder()
            .withMetricRegistry(metricRegistry)
            .withConfiguration(httpConfiguration)
            .build();

        final Supplier<ServiceEndpointProvider> endpointProviderSupplier = () -> serviceEndpointProviderFactory
            .provider(httpConfiguration);

        return HttpExecutorBuilderFactory.builder()
            .callerClass(callerClass)
            .mapper(mapper)
            .client(client)
            .endpointProviderSupplier(endpointProviderSupplier)
            .build();
    }

    @SuppressWarnings("java:S107")
    public <T> T executeGet(
        final HttpExecutorBuilderFactory httpExecutorBuilderFactory,
        final String command, final String url, final Class<T> responseClass,
        final OlympusIMClient olympusIMClient) {
        return httpExecutorBuilderFactory.<T>httpGetExecutorBuilder()
            .command(command)
            .url(url)
            .header(HeaderPair.builder()
                .name(Constants.AUTHORIZATION)
                .value(olympusIMClient.getSystemAuthHeader())
                .build())
            .responseType(responseClass)
            .nonSuccessResponseConsumer(ErrorUtils.nonSuccessResponseHandler())
            .exceptionConsumer(ErrorUtils.exceptionConsumer())
            .build()
            .executeTracked();
    }

    @SuppressWarnings("java:S107")
    public <T> T executeGet(
        final HttpExecutorBuilderFactory httpExecutorBuilderFactory,
        final String command, final String url, final TypeReference<T> responseTypeReference,
        final OlympusIMClient olympusIMClient) {

        return httpExecutorBuilderFactory.<T>httpGetExecutorBuilder()
            .command(command)
            .url(url)
            .header(HeaderPair.builder()
                .name(Constants.AUTHORIZATION)
                .value(olympusIMClient.getSystemAuthHeader())
                .build())
            .responseTypeReference(responseTypeReference)
            .nonSuccessResponseConsumer(ErrorUtils.nonSuccessResponseHandler())
            .exceptionConsumer(ErrorUtils.exceptionConsumer())
            .build()
            .executeTracked();
    }

    public <T> T executeGet(
        final HttpExecutorBuilderFactory httpExecutorBuilderFactory,
        final String command, final String url, final Class<T> responseClass,
        List<HeaderPair> headerPairList) {

        return httpExecutorBuilderFactory.<T>httpGetExecutorBuilder()
            .command(command)
            .url(url)
            .headers(headerPairList)
            .responseType(responseClass)
            .nonSuccessResponseConsumer(ErrorUtils.nonSuccessResponseHandler())
            .exceptionConsumer(ErrorUtils.exceptionConsumer())
            .build()
            .executeTracked();
    }

    @SuppressWarnings("java:S107")
    public <T> T executePost(
        final HttpExecutorBuilderFactory httpExecutorBuilderFactory,
        final String command, final String url, final BaseHttpData requestHttpData,
        final Class<T> responseClass,
        final OlympusIMClient olympusIMClient) {

        return httpExecutorBuilderFactory.<T>httpPostExecutorBuilder()
            .command(command)
            .url(url)
            .header(HeaderPair.builder()
                .name(Constants.AUTHORIZATION)
                .value(olympusIMClient.getSystemAuthHeader())
                .build())
            .httpData(requestHttpData)
            .responseType(responseClass)
            .nonSuccessResponseConsumer(ErrorUtils.nonSuccessResponseHandler())
            .exceptionConsumer(ErrorUtils.exceptionConsumer())
            .build()
            .executeTracked();
    }

    public <T> T executePost(
        final HttpExecutorBuilderFactory httpExecutorBuilderFactory,
        final String command, final String url, final BaseHttpData requestHttpData,
        final TypeReference<T> responseTypeReference,
        final List<HeaderPair> headerPairList, Consumer<HttpException, T> exceptionConsumer){

        return httpExecutorBuilderFactory.<T>httpPostExecutorBuilder()
            .command(command)
            .url(url)
            .headers(headerPairList)
            .httpData(requestHttpData)
            .responseTypeReference(responseTypeReference)
            .nonSuccessResponseConsumer(ErrorUtils.nonSuccessResponseHandler())
            .exceptionConsumer(exceptionConsumer)
            .build()
            .executeTracked();

    }

    public <T> T executePut(
        final HttpExecutorBuilderFactory httpExecutorBuilderFactory,
        final String command, final String url, final BaseHttpData requestHttpData,
        final TypeReference<T> responseTypeReference,
        final List<HeaderPair> headerPairList){

        return httpExecutorBuilderFactory.<T>httpPutExecutorBuilder()
            .command(command)
            .url(url)
            .headers(headerPairList)
            .httpData(requestHttpData)
            .responseTypeReference(responseTypeReference)
            .nonSuccessResponseConsumer(ErrorUtils.nonSuccessResponseHandler())
            .exceptionConsumer(ErrorUtils.exceptionConsumer())
            .build()
            .executeTracked();
    }

    @SuppressWarnings("java:S107")
    public <T> T executePost(
        final HttpExecutorBuilderFactory httpExecutorBuilderFactory,
        final String command, final String url, final BaseHttpData requestHttpData,
        final TypeReference<T> responseTypeReference,
        final OlympusIMClient olympusIMClient) {

        return executePost(httpExecutorBuilderFactory, command, url, requestHttpData, responseTypeReference,
            Collections.singletonList(HeaderPair.builder()
                .name(Constants.AUTHORIZATION)
                .value(olympusIMClient.getSystemAuthHeader())
                .build()), ErrorUtils.exceptionConsumer());
    }


    @SuppressWarnings("java:S107")
    public <T> T executePost(
        final HttpExecutorBuilderFactory httpExecutorBuilderFactory,
        final String command, final String url, final BaseHttpData requestHttpData,
        final Class<T> responseClass, final Consumer<HttpException, T> exceptionConsumer,
        final OlympusIMClient olympusIMClient) {

        return httpExecutorBuilderFactory.<T>httpPostExecutorBuilder()
            .command(command)
            .url(url)
            .header(HeaderPair.builder()
                .name(Constants.AUTHORIZATION)
                .value(olympusIMClient.getSystemAuthHeader())
                .build())
            .httpData(requestHttpData)
            .responseType(responseClass)
            .nonSuccessResponseConsumer(ErrorUtils.nonSuccessResponseHandler())
            .exceptionConsumer(exceptionConsumer)
            .build()
            .executeTracked();
    }

    public static HttpConfiguration getConfig(final RangerHubConfiguration rangerHubConfiguration, String clientId) {
        return DiscoveryUtils.getConfiguration(
                rangerHubConfiguration, clientId
        ).orElseThrow(()-> DisputeExceptionUtil.error(StratosErrorCodeKey.VALIDATION_FAILURE));
    }

    public <T> T dataResolve(final GenericResponse<T> genericResponse) {

        if (!genericResponse.isSuccess()) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.SERVICE_CLIENT_ERROR,
                Map.of("Messages", String.format("Service call failed with error %s : %s",
                    genericResponse.getCode(), genericResponse.getMessage())));
        }
        return genericResponse.getData();
    }
}
