package com.phonepe.merchant.platform.stratos.server.core.events.type;

import com.phonepe.merchant.platform.stratos.server.core.events.BaseEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.EventType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class DisputeActionFailureEvent extends BaseEvent {

    private final DisputeWorkflowEvent disputeWorkflowEvent;

    private final DisputeWorkflowState fromState;

    private final DisputeWorkflowState toState;

    private final String disputeWorkflowId;

    private final String transactionReferenceId;

    private final String errorCode;

    private final String errorContext;
    private final String merchantId;

    @Builder
    public DisputeActionFailureEvent(
        final DisputeWorkflowEvent disputeWorkflowEvent,
        final DisputeWorkflowState fromState,
        final DisputeWorkflowState toState, final String disputeWorkflowId,
        final String transactionReferenceId,
        final String errorCode, final String errorContext,
        final String merchantId) {
        super(EventType.DISPUTE_ACTION_FAILURE_EVENT);
        this.disputeWorkflowEvent = disputeWorkflowEvent;
        this.fromState = fromState;
        this.toState = toState;
        this.disputeWorkflowId = disputeWorkflowId;
        this.transactionReferenceId = transactionReferenceId;
        this.errorCode = errorCode;
        this.errorContext = errorContext;
        this.merchantId = merchantId;
    }

    @Override
    protected String getGroupingKey() {
        return transactionReferenceId;
    }
}
