package com.phonepe.merchant.platform.stratos.server.core.validation;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.pipeline.core.executor.types.Validator;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.CreateDisputeRequest;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.DtoUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import java.util.Map;
import java.util.Optional;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class DuplicateTransactionValidation extends Validator<CreateDisputeRequest> {
    private final DisputeService disputeService;

    @Inject
    public DuplicateTransactionValidation(@NonNull String name, DisputeService disputeService) {
        super("DuplicateValidation");
        this.disputeService = disputeService;
    }

    @Override
    public void validate(final CreateDisputeRequest createDisputeRequest){
        DisputeType disputeType = DtoUtils.transactionTypeToDisputeType(
            createDisputeRequest.getTransactionType(), createDisputeRequest.getDisputeData()
                .getDisputeType());
        Optional<DisputeWorkflow> disputeWorkflow = disputeService.getDisputeWorkflowOptional
            (createDisputeRequest.getTransactionId(), disputeType,
            DtoUtils.disputeDataStageToDisputeStage(
                createDisputeRequest.getDisputeData().getDisputeStage()));

        if(disputeWorkflow.isPresent()){
            log.info("Dispute is already present for createDisputeRequest {} , with dispute Id {} and dispute workflow Id {}",
                createDisputeRequest, disputeWorkflow.get().getDisputeId(), disputeWorkflow.get().getDisputeWorkflowId());
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.DUPLICATE_CHARGEBACK,
                Map.of(
                    Constants.MESSAGE, StratosErrorCodeKey.DUPLICATE_CHARGEBACK.getKey(),
                    "dispute Id ", disputeWorkflow.get().getDisputeId(),
                    "with create createDisputeRequest", createDisputeRequest));
        }
    }
}
