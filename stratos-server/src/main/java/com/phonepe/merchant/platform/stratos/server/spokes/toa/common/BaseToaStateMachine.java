package com.phonepe.merchant.platform.stratos.server.spokes.toa.common;

import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.TransitionLockCommand;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.registries.keys.DisputeStateMachineRegistryKey;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.DisputeStateMachine;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.UpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.interceptors.impls.DisputeErrorHandlingInterceptor;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.common.actions.ToaCompletionAction;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.common.actions.ToaCreateEntryAction;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.common.actions.ToaMandatoryCommentAction;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.common.actions.ToaPayPostEntryAction;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.common.actions.ToaPayStatusCheckAction;

import java.util.EnumSet;

import com.phonepe.merchant.platform.stratos.server.spokes.toa.common.actions.ToaRetryAction;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.common.actions.ToaStateUpdateAction;
import lombok.SneakyThrows;
import org.springframework.statemachine.config.builders.StateMachineConfigurationConfigurer;
import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;

public abstract class BaseToaStateMachine extends DisputeStateMachine {

    protected final UpdateDisputeStateAction updateDisputeStateAction;
    protected final ToaCreateEntryAction toaCreateEntryAction;
    protected final ToaPayStatusCheckAction toaPayStatusCheckAction;
    protected final ToaCompletionAction toaCompletionAction;
    protected final ToaPayPostEntryAction toaPayPostEntryAction;
    protected final ToaRetryAction toaRetryAction;
    protected final ToaMandatoryCommentAction toaMandatoryCommentAction;
    protected final ToaStateUpdateAction toaStateUpdateAction;

    @SuppressWarnings("java:S107")
    protected BaseToaStateMachine(
            TransitionLockCommand transitionLockCommand,
            DisputeErrorHandlingInterceptor disputeErrorHandlingInterceptor,
            UpdateDisputeStateAction updateDisputeStateAction,
            ToaCreateEntryAction toaCreateEntryAction,
            ToaPayStatusCheckAction toaPayStatusCheckAction,
            ToaCompletionAction toaCompletionAction,
            ToaPayPostEntryAction toaPayPostEntryAction,
            ToaRetryAction toaRetryAction,
            ToaMandatoryCommentAction toaMandatoryCommentAction,
            ToaStateUpdateAction toaStateUpdateAction) {
        super(transitionLockCommand, disputeErrorHandlingInterceptor);
        this.updateDisputeStateAction = updateDisputeStateAction;
        this.toaCreateEntryAction = toaCreateEntryAction;
        this.toaPayStatusCheckAction = toaPayStatusCheckAction;
        this.toaCompletionAction = toaCompletionAction;
        this.toaPayPostEntryAction = toaPayPostEntryAction;
        this.toaRetryAction = toaRetryAction;
        this.toaMandatoryCommentAction = toaMandatoryCommentAction;
        this.toaStateUpdateAction = toaStateUpdateAction;
    }

    @Override
    @SneakyThrows
    protected void configure(
            final StateMachineConfigurationConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> config) {
        config
                .withConfiguration()
                .autoStartup(false);
    }

    @Override
    @SneakyThrows
    protected void configure(
            final StateMachineStateConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> states) {
        states
                .withStates()
                .initial(DisputeWorkflowState.RECEIVED)
                .states(EnumSet.allOf(DisputeWorkflowState.class))
                .end(DisputeWorkflowState.TOA_COMPLETED)
                .end(DisputeWorkflowState.TOA_COMPLETED_EXTERNALLY)
                .end(DisputeWorkflowState.TOA_UNABLE_TO_PROCESS);
    }

    @Override
    @SneakyThrows
    protected void configure(
            final StateMachineTransitionConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> transitions) {
        transitions

                .withExternal()
                .source(DisputeWorkflowState.RECEIVED)
                .target(DisputeWorkflowState.TOA_BLOCKED_DUE_TO_KS)
                .event(DisputeWorkflowEvent.KS_ENABLED)
                .action(updateDisputeStateAction)

                .and()
                .withExternal()
                .source(DisputeWorkflowState.TOA_BLOCKED_DUE_TO_KS)
                .target(DisputeWorkflowState.RECEIVED)
                .event(DisputeWorkflowEvent.TOA_BLOCKED_TO_RECEIVED)
                .action(toaPayPostEntryAction)

                .and()
                .withExternal()
                .source(DisputeWorkflowState.TOA_BLOCKED_DUE_TO_KS)
                .target(DisputeWorkflowState.TOA_COMPLETED_EXTERNALLY)
                .event(DisputeWorkflowEvent.TOA_BLOCKED_TO_EXTERNAL_PROCESSED)
                .action(toaMandatoryCommentAction)

                .and()
                .withExternal()
                .source(DisputeWorkflowState.RECEIVED)
                .target(DisputeWorkflowState.TOA_INITIATED)
                .event(DisputeWorkflowEvent.TOA_INITIATED)
                .action(toaPayStatusCheckAction)

                .and()
                .withExternal()
                .source(DisputeWorkflowState.TOA_INITIATION_FAILED)
                .target(DisputeWorkflowState.TOA_INITIATED)
                .event(DisputeWorkflowEvent.RE_INITIATE_TOA) //from console
                .action(toaPayStatusCheckAction)

                .and()
                .withExternal()
                .source(DisputeWorkflowState.TOA_INITIATED)
                .target(DisputeWorkflowState.TOA_PENDING)
                .event(DisputeWorkflowEvent.INITIATED_TO_PENDING_AFTER_MAX_AUTO_RETRY)
                .action(updateDisputeStateAction)

                .and()
                .withExternal()
                .source(DisputeWorkflowState.TOA_INITIATED)
                .target(DisputeWorkflowState.TOA_FAILED)
                .event(DisputeWorkflowEvent.INITIATED_TO_FAILED)
                .action(toaRetryAction) // retry logic

                .and()
                .withExternal()
                .source(DisputeWorkflowState.TOA_INITIATED)
                .target(DisputeWorkflowState.TOA_COMPLETED)
                .event(DisputeWorkflowEvent.INITIATED_TO_COMPLETED)
                .action(toaCompletionAction)

                .and()
                .withExternal()
                .source(DisputeWorkflowState.TOA_PENDING)
                .target(DisputeWorkflowState.TOA_FAILED)
                .event(DisputeWorkflowEvent.PENDING_TO_FAILED)
                .action(toaRetryAction)

                .and()
                .withExternal()
                .source(DisputeWorkflowState.TOA_PENDING)
                .target(DisputeWorkflowState.TOA_COMPLETED)
                .event(DisputeWorkflowEvent.PENDING_TO_COMPLETED)
                .action(toaCompletionAction)

                .and()
                .withExternal()
                .source(DisputeWorkflowState.TOA_FAILED)
                .target(DisputeWorkflowState.TOA_FAILED_AFTER_MAX_AUTO_RETRY)
                .event(DisputeWorkflowEvent.PAY_FAILED_TO_MAX_RETRY)
                .action(updateDisputeStateAction)

                .and()
                .withExternal()
                .source(DisputeWorkflowState.TOA_FAILED)
                .target(DisputeWorkflowState.RECEIVED)
                .event(DisputeWorkflowEvent.PAY_FAILED_TO_RECEIVED)
                .action(toaPayPostEntryAction)

                .and()
                .withExternal()
                .source(DisputeWorkflowState.TOA_FAILED)
                .target(DisputeWorkflowState.TOA_INITIATED)
                .event(DisputeWorkflowEvent.PAY_FAILED_TO_INITIATED)
                .action(toaPayStatusCheckAction)

                .and()
                .withExternal()
                .source(DisputeWorkflowState.TOA_FAILED_AFTER_MAX_AUTO_RETRY)
                .target(DisputeWorkflowState.TOA_UNABLE_TO_PROCESS)
                .event(DisputeWorkflowEvent.MAX_RETRY_TO_UNABLE_TO_PROCESS) // MANUAL ACTION
                .action(toaMandatoryCommentAction)

                .and()
                .withExternal()
                .source(DisputeWorkflowState.RECEIVED)
                .target(DisputeWorkflowState.TOA_INITIATION_FAILED)
                .event(DisputeWorkflowEvent.TOA_INITIATION_FAILED_AFTER_MAX_AUTO_RETRY)
                .action(toaStateUpdateAction)

                .and();

        extendStates(transitions);

    }

    @Override
    public abstract DisputeStateMachineRegistryKey getRegistryKey();

    protected void extendStates(StateMachineTransitionConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> transitions) {
        //NOOP
    }
}
