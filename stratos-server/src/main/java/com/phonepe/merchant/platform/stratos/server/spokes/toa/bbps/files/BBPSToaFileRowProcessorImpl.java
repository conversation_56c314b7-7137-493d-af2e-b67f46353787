package com.phonepe.merchant.platform.stratos.server.spokes.toa.bbps.files;

import com.google.inject.Inject;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.configs.FileConfig;
import com.phonepe.merchant.platform.stratos.server.core.configs.toa.ToaConfig;
import com.phonepe.merchant.platform.stratos.server.core.handlebars.HandleBarsService;
import com.phonepe.merchant.platform.stratos.server.core.helpers.id.IdHelper;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeCategory;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeIssuer;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.models.SourceType;
import com.phonepe.merchant.platform.stratos.server.core.models.UserType;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.MerchantMandateService;
import com.phonepe.merchant.platform.stratos.server.core.services.PaymentsService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.StorageUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.files.BaseFileRowProcessor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.files.models.FileRowMeta;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.visitors.PaymentContextMerchantTransactionIdVisitor;
import com.phonepe.models.common.enums.PaymentState;
import com.phonepe.models.payments.pay.TransactionDetail;
import com.phonepe.models.payments.pay.receivers.Receiver;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;

@Slf4j
public class BBPSToaFileRowProcessorImpl extends BaseFileRowProcessor<TransactionDetail> {
    private final IdHelper idHelper;

    @Inject
    public BBPSToaFileRowProcessorImpl(final HandleBarsService handleBarsService,
                                       final DisputeService disputeService,
                                       final MerchantMandateService merchantMandateService,
                                       final IdHelper idHelper) {
        super(handleBarsService, disputeService, merchantMandateService);
        this.idHelper = idHelper;
    }

    @Override
    public DisputeWorkflow enrichAndGetDisputeWorkflow(final TransactionDetail transactionDetails,
                                                       final FinancialDisputeWorkflow disputeWorkflowFilePojo,
                                                       final String fileId,
                                                       final DisputeType disputeType,
                                                       final FileConfig fileConfig,
                                                       final String disputeId) {
        return FinancialDisputeWorkflow.builder()
                .key(StorageUtils.primaryKey())
                .disputeWorkflowId(idHelper.disputeWorkflowId(disputeWorkflowFilePojo.getTransactionReferenceId()))
                .disputeWorkflowVersion(DisputeWorkflowVersion.V1)
                .disputeSourceType(SourceType.FILE)
                .disputeSourceId(fileId)
                .transactionReferenceId(disputeWorkflowFilePojo.getTransactionReferenceId())
                .disputeType(disputeType)
                .disputeStage(disputeWorkflowFilePojo.getDisputeStage())
                .currentEvent(DisputeWorkflowEvent.CREATE_ENTRY)
                .currentState(DisputeWorkflowState.RECEIVED)
                .disputedAmount(disputeWorkflowFilePojo.getDisputedAmount())
                .raisedAt(LocalDateTime.now())
                .respondBy(LocalDateTime.now().plusDays(5))
                .gandalfUserId(Constants.STRATOS_SYSTEM_USER_OLYMPUS.getUserDetails().getUserId())
                .userType(UserType.SYSTEM)
                .disputeId(disputeId)
                .build();
    }

    @Override
    public TransactionDetail getTransactionDetails(final String instrumentTransactionId,
                                                   final FileRowMeta fileRowMeta) {
        return null;
    }

    @Override
    @SneakyThrows
    public Dispute getDispute(final DisputeWorkflow disputeWorkflow,
                              final TransactionDetail transactionDetail,
                              final Dispute disputeFilePojo,
                              final DisputeType disputeType,
                              final String instrumentTransactionId) {
        final String transactionReferenceId = disputeFilePojo.getTransactionReferenceId();
        final String merchantId = disputeFilePojo.getMerchantId();
        final long amount = disputeWorkflow.getDisputedAmount();
        final String merchantTransactionId = disputeFilePojo.getMerchantTransactionId();
        return Dispute.builder()
                .key(StorageUtils.primaryKey())
                .disputeId(idHelper.disputeId(transactionReferenceId))
                .transactionReferenceId(transactionReferenceId)
                .disputeType(DisputeType.BBPS_TAT_BREACH_TOA)
                .currentDisputeStage(DisputeStage.FIRST_LEVEL)
                .merchantId(merchantId)
                .merchantTransactionId(merchantTransactionId)
                .disputeIssuer(DisputeIssuer.BBPS)
                .disputeReferenceId(disputeFilePojo.getDisputeReferenceId())
                .transactionAmount(amount)
                .disputeCategory(DisputeCategory.TOA)
                .build();
    }
}
