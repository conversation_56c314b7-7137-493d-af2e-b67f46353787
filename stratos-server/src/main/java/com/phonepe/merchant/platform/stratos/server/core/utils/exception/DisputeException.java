package com.phonepe.merchant.platform.stratos.server.core.utils.exception;

import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import lombok.Builder;
import lombok.Getter;

import java.util.Map;

@Getter
public class DisputeException extends RuntimeException {

  @Getter
  private final StratosErrorCodeKey errorCode;


  private final String message;
  private final transient Map<String, Object> context;


  @Builder
  DisputeException(Throwable e, StratosErrorCodeKey errorCode, String error, Map<String, Object> context) {
    super(e);
    this.errorCode = errorCode;
    this.message = error;
    this.context = context;
  }



}

