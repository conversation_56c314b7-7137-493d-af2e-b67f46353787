package com.phonepe.merchant.platform.stratos.server.core.guice;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.data.provider.rosey.bundle.RoseyConfigProviderBundle;
import com.phonepe.dataplatform.EventIngestorClient;
import com.phonepe.growth.mustang.MustangEngine;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.StratosConfiguration;
import com.phonepe.merchant.platform.stratos.server.core.utils.HttpClientUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.platform.docstore.client.DocstoreClient;
import com.phonepe.platform.docstore.client.impl.HttpDocstoreClient;
import com.phonepe.platform.http.v2.client.ClientFactory;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.discovery.HttpDiscoveryBundle;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import com.phonepe.platform.http.v2.discovery.utils.DiscoveryUtils;
import com.phonepe.platform.http.v2.executor.factory.HttpExecutorBuilderFactory;
import com.phonepe.platform.killswitch.client.KillswitchClient;
import com.phonepe.platform.sentinel.SentinelClient;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.utils.Constants;
import io.dropwizard.setup.Environment;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public class CoreModule extends AbstractModule {

    private final RoseyConfigProviderBundle<StratosConfiguration> roseyConfigProviderBundle;
    private final ObjectMapper objectMapper;
    private final MetricRegistry metricRegistry;
    private final HttpDiscoveryBundle<StratosConfiguration> httpDiscoveryBundle;

    @Override
    protected void configure() {
        bind(KaizenConfig.class).to(StratosConfiguration.class);

    }
    @Provides
    public ObjectMapper getObjectMapper() {
        return objectMapper;
    }

    @Provides
    public MetricRegistry metricRegistry() {
        return metricRegistry;
    }

    @Provides
    public DataProvider<StratosConfiguration> getDataProvider() {
        return roseyConfigProviderBundle.getDataProvider();
    }
    @Provides
    public DataProvider<KaizenConfig> getDataProvider(StratosConfiguration config) {
        return new DataProvider<>() {
            @Override
            public void update() {
                // no need to implement
            }

            @Override
            public KaizenConfig getData() {
                return config;
            }
        };
    }

    @Provides
    @Singleton
    public EventIngestorClient getEventIngestorClient(
            final StratosConfiguration config,
            final ServiceEndpointProviderFactory serviceEndpointProviderFactory,
            final OlympusIMClient olympusIMClient) throws Exception {
        try {
                return new EventIngestorClient(config.getEventIngestor(), serviceEndpointProviderFactory, objectMapper,
                    metricRegistry);
        } catch (final Exception e) {
            log.error("Error while creating event Ingestor Client", e);
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.INIT_ERROR, e);
        }
    }

    @Provides
    @Singleton
    @SneakyThrows
    public SentinelClient provideSentinelClient(final KaizenConfig config,
                                                final Environment environment,
                                                final ObjectMapper mapper,
                                                final OlympusIMClient olympusIMClient) {

        final var sentinalHttpConfiguration = DiscoveryUtils.getConfiguration(config.getRangerHubConfiguration(),
                        Constants.ClientIds.SENTINEL)
                .orElseThrow(() -> new IllegalArgumentException("sentinel HttpConfig missing."));

        return new SentinelClient(mapper, ClientFactory.newHttpClientBuilder()
                .withMetricRegistry(environment.metrics())
                .withConfiguration(sentinalHttpConfiguration)
                .build(), () -> httpDiscoveryBundle.getEndpointProviderFactory()
                .provider(Constants.ClientIds.SENTINEL), olympusIMClient::getSystemAuthHeader);
    }


    @Provides
    @Singleton
    public DocstoreClient provideDocstoreClient(
            StratosConfiguration stratosConfiguration,
            final ServiceEndpointProviderFactory serviceEndpointProviderFactory, ObjectMapper mapper,
            OlympusIMClient olympusIMClient,
            final MetricRegistry metricRegistry) {
        try {
            HttpConfiguration httpConfiguration = DiscoveryUtils.getConfiguration(
                            stratosConfiguration.getRangerHubConfiguration(), "kaizenDocstore"
                    )
                    .orElseThrow(() -> new IllegalArgumentException("kaizenDocstore HttpConfig missing."));
            Objects.requireNonNull(olympusIMClient);
            HttpExecutorBuilderFactory httpExecutorBuilderFactory = HttpClientUtils.getHttpExecutorBuilderFactory(
                    CoreModule.class, httpConfiguration,
                    serviceEndpointProviderFactory, mapper, metricRegistry);
            return new HttpDocstoreClient(mapper, olympusIMClient::getSystemAuthHeader,false, httpExecutorBuilderFactory);
        } catch (Exception e) {
            log.error("Error while creating Docstore client client", e);
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.INIT_ERROR, e);
        }
    }

    @Singleton
    @Provides
    public KillswitchClient provideKillswitchClient(StratosConfiguration stratosConfiguration, ObjectMapper mapper, Environment environment) {

        final var killswitchHttpConfig = DiscoveryUtils.getConfiguration(stratosConfiguration.getRangerHubConfiguration(),
                        "kaizenKillswitch")
                .orElseThrow(() -> new IllegalArgumentException("Killswitch HttpConfig missing."));

        return KillswitchClient.builder()
                .projectId(stratosConfiguration.getKillswitchConfig()
                        .getProjectId())
                .endpointProviderSupplier(() -> httpDiscoveryBundle.getEndpointProviderFactory()
                        .provider(killswitchHttpConfig))
                .mapper(mapper)
                .killswitchesConfig(killswitchHttpConfig)
                .skipCache(false)
                .metricRegistry(environment.metrics())
                .build();
    }

    @Provides
    @Singleton
    public MustangEngine provideMustangEngine() {
        return MustangEngine.builder().mapper(objectMapper).build();
    }
}
