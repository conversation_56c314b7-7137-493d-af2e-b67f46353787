package com.phonepe.merchant.platform.stratos.server.core.clients;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.HttpClientUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.TypeReferences;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.platform.http.v2.common.hub.RangerHubConfiguration;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import com.phonepe.platform.http.v2.discovery.utils.DiscoveryUtils;
import com.phonepe.platform.http.v2.executor.factory.HttpExecutorBuilderFactory;
import com.phonepe.platform.http.v2.executor.httpdata.SerializableHttpData;
import com.phonepe.services.refund.orchestrator.models.v1.ROStatusRequest;
import com.phonepe.services.refund.orchestrator.models.v1.RefundRequest;
import com.phonepe.services.refund.orchestrator.models.v1.RefundResponse;
import javax.ws.rs.core.MediaType;

@Singleton
public class RefundOrchestratorClient {

    private final HttpExecutorBuilderFactory httpExecutorBuilderFactory;

    private final OlympusIMClient olympusIMClient;

    @Inject
    public RefundOrchestratorClient(
        final ServiceEndpointProviderFactory serviceEndpointProviderFactory,
        final ObjectMapper mapper,
        final MetricRegistry metricRegistry,
        final RangerHubConfiguration rangerHubConfiguration,
        final OlympusIMClient olympusIMClient) {

        this.httpExecutorBuilderFactory = HttpClientUtils.getHttpExecutorBuilderFactory(
            RefundOrchestratorClient.class, DiscoveryUtils.getConfiguration(
                rangerHubConfiguration, Constants.RO_CLIENT_ID
            ).orElse(null),
            serviceEndpointProviderFactory, mapper, metricRegistry);

        this.olympusIMClient = olympusIMClient;
    }

    public GenericResponse<RefundResponse> initateRefund(RefundRequest refundRequest) {

        final var url = "/v1/refund/STRATOS";

        return HttpClientUtils.executePost(
            httpExecutorBuilderFactory, "initateRefund", url,
            SerializableHttpData.builder()
                .mediaType(MediaType.APPLICATION_JSON)
                .data(refundRequest).build(), TypeReferences.REFUND_RESPONSE, olympusIMClient);

    }

    public GenericResponse<RefundResponse> getRefundStatus(final ROStatusRequest request) {

        final var url = "/v1/refund/STRATOS/status";

        return HttpClientUtils.executePost(
            httpExecutorBuilderFactory, "getRefundStatus", url,
            SerializableHttpData.builder()
                .mediaType(MediaType.APPLICATION_JSON)
                .data(request).build(), TypeReferences.REFUND_RESPONSE, olympusIMClient);
    }

}
