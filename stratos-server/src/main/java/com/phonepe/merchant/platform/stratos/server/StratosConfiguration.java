package com.phonepe.merchant.platform.stratos.server;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.hystrix.configurator.config.HystrixConfig;
import com.phonepe.central.stratos.penalty.server.aerospike.PenaltyAerospikeSet;
import com.phonepe.central.stratos.penalty.server.config.PenaltyTenantConfig;
import com.phonepe.dataplatform.EventIngestorClientConfig;
import com.phonepe.error.configurator.model.ErrorConfiguratorConfig;
import com.phonepe.gandalf.models.client.GandalfClientConfig;
import com.phonepe.merchant.platform.stratos.models.accountingevents.AccountingEventType;
import com.phonepe.merchant.platform.stratos.models.accountingevents.AccountingTransactionType;
import com.phonepe.merchant.platform.stratos.models.callbacks.CallbackType;
import com.phonepe.merchant.platform.stratos.server.core.configs.ApiFilterConfig;
import com.phonepe.merchant.platform.stratos.server.core.configs.AutoApprovalConfig;
import com.phonepe.merchant.platform.stratos.server.core.configs.DisputeWorkflowStateConfig;
import com.phonepe.merchant.platform.stratos.server.core.configs.FileConfig;
import com.phonepe.merchant.platform.stratos.server.core.configs.KratosConfiguration;
import com.phonepe.merchant.platform.stratos.server.core.configs.NpciDisputeFlag;
import com.phonepe.merchant.platform.stratos.server.core.configs.P2pmToaConfig;
import com.phonepe.merchant.platform.stratos.server.core.configs.TstoreClientConfig;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.AerospikeSet;
import com.phonepe.merchant.platform.stratos.server.core.configs.*;
import com.phonepe.merchant.platform.stratos.server.core.configs.toa.ToaConfig;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeCategory;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.models.EventGenerationType;
import com.phonepe.merchant.platform.stratos.server.core.neuron.PulseType;
import com.phonepe.merchant.platform.stratos.server.core.queue.ActionType;
import com.phonepe.merchants.platform.notificationbundle.config.NotificationClientConfig;
import com.phonepe.metrics.config.ReporterConfig;
import com.phonepe.olympus.im.client.config.OlympusIMClientConfig;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.common.hub.RangerHubConfiguration;
import com.phonepe.verified.kaizen.queue.ActorType;
import com.platform.validation.ValidationConfig;
import in.vectorpro.dropwizard.swagger.SwaggerBundleConfiguration;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.config.RMQConfig;
import io.appform.dropwizard.sharding.config.ShardedHibernateFactory;
import io.appform.ranger.discovery.bundle.ServiceDiscoveryConfiguration;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class StratosConfiguration extends StratosKaizenConfig implements ErrorConfiguratorConfig {

    @Valid
    @NotNull
    private HystrixConfig hystrixConfig = new HystrixConfig();

    @Valid
    @NotNull
    private ReporterConfig reporterConfig;

    @Valid
    @NotNull
    private SwaggerBundleConfiguration swagger = new SwaggerBundleConfiguration();

    @Valid
    @NotNull
    private ServiceDiscoveryConfiguration discovery = new ServiceDiscoveryConfiguration();

    @Valid
    @NotNull
    private EventIngestorClientConfig eventIngestor = new EventIngestorClientConfig();

    @Valid
    @NotNull
    private KratosConfiguration kratosConfiguration;

    private boolean disableEventIngestion = false;

    private boolean loggingEnabled = false;

    @Valid
    @NotNull
    private ShardedHibernateFactory database = new ShardedHibernateFactory();

    @Valid
    @NotNull
    private ValidationConfig validationConfig;

    @Valid
    @NotNull
    private TstoreClientConfig tstoreClientConfig;

    @Valid
    @NotNull
    private RMQConfig rmqConfig;

    @Valid
    @NotEmpty
    private Map<ActionType, ActorConfig> stratosActorsConfig;

    @Valid
    @NotEmpty
    private Map<ActorType, ActorConfig> actorsConfig;

    @Valid
    @NotNull
    private Map<String, FileConfig> fileConfigs;

    @NotEmpty
    private Map<DisputeType, Map<EventGenerationType, AccountingEventType>> accountingEventTypeMapping;

    @NotEmpty
    private Map<DisputeType, Map<EventGenerationType, AccountingTransactionType>> accountingTransactionTypeMapping;

    private Map<DisputeCategory, Set<String>> disputeCategoryMapping;

    private DisputeWorkflowStateConfig disputeWorkflowStateConfig;

    @Valid
    @NotNull
    private Set<String> chargebackAnomalyMerchants;

    @NotNull
    @Valid
    private OlympusIMClientConfig olympusIMClientConfig;
    @Valid
    @NotNull
    private Map<PulseType, String> pulseTypeCellMapping;

    @Valid
    @NotNull
    private P2pmToaConfig p2pmToaConfig;

    @Valid
    @NotNull
    private NotificationClientConfig notificationClientConfig;

    @Valid
    private Map<ActionType, List<StratosErrorCodeKey>> actorIgnoreErrorCodes = new EnumMap<>(ActionType.class);

    @Valid
    private RangerHubConfiguration rangerHubConfiguration;

    @NotNull
    @Valid
    private ApiFilterConfig apiFilterConfig;

    @Valid
    private  Map<DisputeType, ToaConfig> toaConfig;

    @Valid
    @NotNull
    private NpciDisputeFlag npciDisputeFlag;

    @NotEmpty
    private String errorPropertiesPath;

    private String baseName ="resource";

    @Valid
    @NotNull
    private Map<DisputeWorkflowState, Long> disputeWorkflowStateTTLDaysMap;

    @Valid
    private Map<String,List<AutoApprovalConfig>> autoApprovalConfig;

    @Valid
    @NotEmpty
    private Map<DisputeType, TtlConfig> ttlConfig;

    @NotEmpty
    private String docstoreBaseUrl;

    @Valid
    private Map<String, HttpConfiguration> httpCallbackClientConfigMap;

    @Valid
    private Map<CallbackType,List<CallbackConfigEntity>> callbackConfig;

    @Valid
    @NotNull
    private GandalfClientConfig gandalfClientConfig;

    @Valid
    @NotEmpty
    private Map<AerospikeSet, Integer> stratosAerospikeTTLSeconds;

    @Valid
    @NotEmpty
    private Map<PenaltyAerospikeSet, Integer> penaltyAerospikeTTLSeconds;

    @Valid
    @NotEmpty
    private Map<DisputeType, String> olympusTenantIdMap;

    @Valid
    private List<DisputeType> fileUploadChecker;

    @NotNull
    @Valid
    private Map<DisputeType, DisputeCategoryTtlConfig> fraudChargebackTtl;

    @NotNull
    @Valid
    private Map<DisputeStage, String> walletDisputeStageMap;

    @Valid
    @NotNull
    private List<PenaltyTenantConfig> penaltyTenantConfigs;

    @Valid
    private Map<DisputeType,Map<DisputeWorkflowVersion, List<DisputeWorkflowState>>> ignoreSignalStateConfig;

}
