package com.phonepe.merchant.platform.stratos.server.core.statemachines;

import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.TransitionLockCommand;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.registries.StateMachineListenerRegistry;
import com.phonepe.merchant.platform.stratos.server.core.registries.keys.DisputeStateMachineRegistryKey;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.interceptors.impls.DisputeErrorHandlingInterceptor;

public abstract class DisputeStateMachine extends
    BaseStateMachine<DisputeWorkflowState, DisputeWorkflowEvent> {

    protected DisputeStateMachine(
            final TransitionLockCommand transitionLockCommand,
            final DisputeErrorHandlingInterceptor disputeErrorHandlingInterceptor,
            final StateMachineListenerRegistry<DisputeWorkflowState,DisputeWorkflowEvent> stateMachineListenerRegistry) {
        super(transitionLockCommand, disputeErrorHandlingInterceptor,stateMachineListenerRegistry);
    }

    public abstract DisputeStateMachineRegistryKey getRegistryKey();
}
