package com.phonepe.merchant.platform.stratos.server.core.services.impls;


import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Injector;
import com.phonepe.dataplatform.EventIngestorClient;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.StratosApplication;
import com.phonepe.merchant.platform.stratos.server.StratosConfiguration;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.services.KratosService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import com.phonepe.ruleengine.model.integration.FraudActionHolder;
import com.phonepe.ruleengine.client.RuleEngineEvalClient;
import com.phonepe.ruleengine.client.RuleEnginePayload;
import com.phonepe.ruleengine.model.integration.FraudAction;
import io.dropwizard.lifecycle.Managed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import javax.inject.Inject;
import javax.inject.Singleton;
import java.util.Collections;
import java.util.Map;
import java.util.Set;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor_ = @Inject)
public class KratosServiceImpl implements Managed, KratosService {


    private final RuleEngineEvalClient ruleEngineClient;
    private final ObjectMapper objectMapper;
    private final EventIngestorClient eventIngestorClient;
    private final MetricRegistry metricRegistry;
    private final Injector injector;
    private final Supplier<String> authTokenSupplier;
    private final StratosConfiguration stratosConfiguration;
    private final ServiceEndpointProviderFactory serviceEndpointProviderFactory;

    @Override
    public void start() throws Exception {
            this.ruleEngineClient.start(
            stratosConfiguration.getKratosConfiguration().getRuleEngineClientConfiguration(),
            stratosConfiguration.getHystrixConfig(),
            eventIngestorClient,
            metricRegistry,
            objectMapper, authTokenSupplier,
            injector, null, serviceEndpointProviderFactory);
    }

    @Override
    public void stop() throws Exception {
        this.ruleEngineClient.cleanUp();
    }


    private Set<FraudAction> recommendActions(final Object payload, final String referenceId, final String type, String requestId){

        RuleEnginePayload ruleEnginePayload = RuleEnginePayload.builder()
            .data(payload)
            .context(Collections.emptyMap())
            .timestamp(System.currentTimeMillis())
            .id(requestId)
            .app(StratosApplication.APP_NAME)
            .referenceId(referenceId)
            .type(type)
            .build();

        log.info("Calling Kratos for {} with payload {}", referenceId, payload);

        return ruleEngineClient.recommendActions(ruleEnginePayload).getRecommendedActions().stream()
                .map(FraudActionHolder::getFraudAction)
                .collect(Collectors.toSet());
    }

    @Override
    public FraudAction chargebackRecommendAction(DisputeWorkflow disputeWorkflow, Object payload,
        String referenceId, String requestId) {


        Set<FraudAction> recommendationActions = recommendActions(payload, referenceId,
            stratosConfiguration.getKratosConfiguration().getChargebackAcceptanceType(), requestId);

        if(recommendationActions.size() != 1){
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_KRATOS_ACTION, Map.of(Constants.MESSAGE, String.format("Expecting only one action but found actions: %s",
                String.join(",", recommendationActions.stream().map(FraudAction::getType).toList()))));
        }

        return recommendationActions.stream().findFirst().get(); //NOSONAR
    }
}
