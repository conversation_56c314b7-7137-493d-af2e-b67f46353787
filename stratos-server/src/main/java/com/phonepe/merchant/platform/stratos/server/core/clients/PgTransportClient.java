package com.phonepe.merchant.platform.stratos.server.core.clients;


import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.clients.response.pgtransport.PgTransactionDetails;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs.PgTransportServiceConfig;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.HttpClientUtils;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import com.phonepe.platform.http.v2.executor.HeaderPair;
import com.phonepe.platform.http.v2.executor.factory.HttpExecutorBuilderFactory;
import java.util.ArrayList;
import java.util.List;

@Singleton
public class PgTransportClient {

    private final HttpExecutorBuilderFactory httpExecutorBuilderFactory;
    private final OlympusIMClient olympusIMClient;

    @Inject
    public PgTransportClient(
        final ServiceEndpointProviderFactory serviceEndpointProviderFactory,
        final ObjectMapper mapper,
        final MetricRegistry metricRegistry,
        final @PgTransportServiceConfig HttpConfiguration pgTransportConfig,
        final OlympusIMClient olympusIMClient) {


        this.httpExecutorBuilderFactory = HttpClientUtils.getHttpExecutorBuilderFactory(
            PgTransportClient.class, pgTransportConfig,
            serviceEndpointProviderFactory, mapper, metricRegistry);

        this.olympusIMClient = olympusIMClient;
    }


    public PgTransactionDetails getTransactionDetailsFromPgTxnId(String pgTxnId) {

        final var url = String
            .format("/pg-transport/v1/internal/transaction/%s", pgTxnId);

        List<HeaderPair> headerPairList = new ArrayList<>();

        headerPairList.add(HeaderPair.builder()
            .name("X-REQUEST-ID")
            .value("PhonePe")
                .build());

        headerPairList.add(HeaderPair.builder()
            .name(Constants.AUTHORIZATION)
            .value(olympusIMClient.getSystemAuthHeader())
            .build());

        return HttpClientUtils.executeGet(
            httpExecutorBuilderFactory, "paymentsIdFromPgId", url,
            PgTransactionDetails.class, headerPairList);
    }

}
