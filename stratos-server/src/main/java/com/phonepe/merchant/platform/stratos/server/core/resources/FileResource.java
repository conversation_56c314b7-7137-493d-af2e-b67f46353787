package com.phonepe.merchant.platform.stratos.server.core.resources;

import static com.phonepe.olympus.im.models.core.CoreConstants.BEARER;
import static javax.ws.rs.core.HttpHeaders.CONTENT_DISPOSITION;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.google.inject.Inject;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeTypeDto;
import com.phonepe.merchant.platform.stratos.models.files.FileFormat;
import com.phonepe.merchant.platform.stratos.models.files.FileTypeDto;
import com.phonepe.merchant.platform.stratos.models.files.FileFilter;
import com.phonepe.merchant.platform.stratos.models.files.responses.FileHistoryResponse;
import com.phonepe.merchant.platform.stratos.models.files.responses.FileRowResponse;
import com.phonepe.merchant.platform.stratos.models.files.responses.FileUploadResponse;
import com.phonepe.merchant.platform.stratos.server.core.models.ApiState;
import com.phonepe.merchant.platform.stratos.server.core.services.FileService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.DtoUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.FileUtils;
import com.phonepe.olympus.im.client.annotations.AccessAllowed;
import com.phonepe.olympus.im.models.security.ServiceUserPrincipal;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import io.dropwizard.auth.Auth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.io.InputStream;
import java.util.Arrays;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;
import org.glassfish.jersey.media.multipart.FormDataParam;

@Slf4j
@Path("/v1/file/")
@Tag(name = "File Related APIs")
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
@SecurityScheme(name = BEARER, type = SecuritySchemeType.APIKEY, in = SecuritySchemeIn.HEADER, paramName = "Authorization")
@SecurityRequirement(name = BEARER)
public class FileResource {

    private final FileService fileService;

    @GET
    @ExceptionMetered
    @Path("/type/meta")
    @AccessAllowed
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ApiKillerMeta(tags = {ApiState.ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Map<DisputeTypeDto, Set<FileTypeDto>> meta() {

        return Arrays
            .stream(FileTypeDto.values())
            .collect(Collectors.groupingBy(
                FileTypeDto::getDisputeType,
                Collectors.mapping(Function.identity(), Collectors.toSet())));

    }

    @POST
    @ExceptionMetered
    @RolesAllowed("chargeback/upload-file")
    @Path("/upload/{disputeType}/{fileType}")
    @Operation(summary = "File Upload Api", requestBody = @RequestBody(content = @Content(mediaType = MediaType.MULTIPART_FORM_DATA, schema = @Schema(implementation = MultiPartFileRequest.class))))
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Produces(MediaType.APPLICATION_JSON)
    @ApiKillerMeta(tags = {ApiState.ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public FileUploadResponse upload(
            @NotNull @PathParam("disputeType") final DisputeTypeDto disputeType,
            @NotNull @PathParam("fileType") final FileTypeDto fileType,
            @FormDataParam("file") final InputStream inputStream,
            @FormDataParam("file") final FormDataContentDisposition fileMetaData,
            @HeaderParam(Constants.AUTHORIZATION) @Parameter(required = true, hidden = true) String authToken,
            @Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal) {

        final UserAuthDetails userAuthDetails = serviceUserPrincipal.getUserAuthDetails();
        FileUtils.validateDisputeTypeFileTypeMapping(disputeType, fileType);

        return fileService.upload(inputStream, fileMetaData.getFileName(),
            DtoUtils.disputeDtoToType(disputeType), DtoUtils.fileDtoToType(fileType),
            userAuthDetails.getUserDetails(), authToken);
    }


    @POST
    @ExceptionMetered
    @Path("/history")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @RolesAllowed("chargeback/file-history")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public FileHistoryResponse history(@Valid final FileFilter filter) {
        return fileService.getHistory(filter);

    }

    @GET
    @ExceptionMetered
    @Path("/{fileId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    @RolesAllowed("chargeback/file-history")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response download(@NotNull @PathParam("fileId") final String fileId) {
        return fileService.getFile(fileId);
    }


    @GET
    @ExceptionMetered
    @Path("/row/{fileId}")
    @Produces(MediaType.APPLICATION_JSON)
    @RolesAllowed("chargeback/file-history")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public FileRowResponse fileRow(@NotNull @PathParam("fileId") final String fileId) {
        return fileService.fileRow(fileId);
    }

    @POST
    @ExceptionMetered
    @Path("/row/download/{fileId}")
    @Produces({Constants.MEDIA_TYPE_CSV, MediaType.APPLICATION_JSON})
    @Operation(summary = "Download Chargeback Processing rows based on FileId ")
    @RolesAllowed("chargeback/file-history")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response downloadProcessingRow(@NotNull @QueryParam("format") final FileFormat fileFormat,
        @NotNull @PathParam("fileId") final String fileId) {
        return Response.ok()
            .header(CONTENT_DISPOSITION, "attachment;filename=" + "fileHistory_summary.csv")
            .entity(fileService.downloadProcessingRow(fileFormat,fileId))
            .build();
    }
}
