package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.edc.statemachines;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.TransitionLockCommand;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.registries.StateMachineListenerRegistry;
import com.phonepe.merchant.platform.stratos.server.core.registries.keys.DisputeStateMachineRegistryKey;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.DisputeStateMachine;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.comments.impls.MandatoryCommentUpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.comments.impls.OptionalCommentUpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.AcceptDebitAndDebitDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.AcceptDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.AcceptPartialDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.DebitDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.FraudCheckDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.ResetChargebackAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.UpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.interceptors.impls.DisputeErrorHandlingInterceptor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.ApproveRecoverChargebackAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.ApproveRecoveryReversalChargebackAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.RaiseChargebackRecoveryAccountingEventAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.RaiseChargebackRecoveryReversalAccountingEventAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.edc.statemachines.actions.EdcBlockRefundAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.edc.statemachines.actions.EdcChargebackCreateEntryAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.edc.statemachines.actions.EdcUnblockRefundAction;
import java.util.EnumSet;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.config.builders.StateMachineConfigurationConfigurer;
import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;
@Slf4j
@Singleton
public class EdcChargebackStateMachine extends DisputeStateMachine {

    private final EdcChargebackCreateEntryAction createEntryAction;
    private final UpdateDisputeStateAction updateDisputeStateAction;
    private final EdcUnblockRefundAction edcUnblockRefundAction;
    private final EdcBlockRefundAction edcBlockRefundAction;
    private final AcceptDisputeAction acceptDisputeAction;
    private final AcceptPartialDisputeAction acceptPartialDisputeAction;
    private final DebitDisputeAction debitDisputeAction;
    private final ResetChargebackAction resetChargebackAction;
    private final MandatoryCommentUpdateDisputeStateAction mandatoryCommentUpdateDisputeStateAction;
    private final OptionalCommentUpdateDisputeStateAction optionalCommentUpdateDisputeStateAction;
    private final ApproveRecoverChargebackAction approveRecoverChargebackAction;
    private final RaiseChargebackRecoveryAccountingEventAction raiseChargebackRecoveryAccountingEventAction;
    private final ApproveRecoveryReversalChargebackAction approveRecoveryReversalChargebackAction;
    private final RaiseChargebackRecoveryReversalAccountingEventAction raiseChargebackRecoveryReversalAccountingEventAction;
    private final FraudCheckDisputeAction fraudCheckUpdateDisputeAction;
    private final AcceptDebitAndDebitDisputeAction acceptDebitAndDebitDisputeAction;



    @Inject
    @SuppressWarnings("java:S107")
    protected EdcChargebackStateMachine(
            final TransitionLockCommand transitionLockCommand,
            final DisputeErrorHandlingInterceptor disputeErrorHandlingInterceptor,
            final EdcChargebackCreateEntryAction createEntryAction,
            final UpdateDisputeStateAction updateDisputeStateAction,
            final EdcUnblockRefundAction edcUnblockRefundAction,
            final EdcBlockRefundAction edcBlockRefundAction,
            final AcceptDisputeAction acceptDisputeAction,
            final AcceptPartialDisputeAction acceptPartialDisputeAction,
            final DebitDisputeAction debitDisputeAction,
            final ResetChargebackAction resetChargebackAction,
            final MandatoryCommentUpdateDisputeStateAction mandatoryCommentUpdateDisputeStateAction,
            final OptionalCommentUpdateDisputeStateAction optionalCommentUpdateDisputeStateAction,
            final ApproveRecoverChargebackAction approveRecoverChargebackAction,
            final RaiseChargebackRecoveryAccountingEventAction raiseChargebackRecoveryAccountingEventAction,
            final ApproveRecoveryReversalChargebackAction approveRecoveryReversalChargebackAction,
            final RaiseChargebackRecoveryReversalAccountingEventAction raiseChargebackRecoveryReversalAccountingEventAction,
            final FraudCheckDisputeAction fraudCheckUpdateDisputeAction,
            final AcceptDebitAndDebitDisputeAction acceptDebitAndDebitDisputeAction,
            final StateMachineListenerRegistry<DisputeWorkflowState, DisputeWorkflowEvent> stateMachineListenerRegistry) {
        super(transitionLockCommand, disputeErrorHandlingInterceptor, stateMachineListenerRegistry);
        this.createEntryAction = createEntryAction;
        this.updateDisputeStateAction = updateDisputeStateAction;
        this.edcUnblockRefundAction = edcUnblockRefundAction;
        this.edcBlockRefundAction = edcBlockRefundAction;
        this.acceptDisputeAction = acceptDisputeAction;
        this.acceptPartialDisputeAction = acceptPartialDisputeAction;
        this.debitDisputeAction = debitDisputeAction;
        this.resetChargebackAction = resetChargebackAction;
        this.mandatoryCommentUpdateDisputeStateAction = mandatoryCommentUpdateDisputeStateAction;
        this.optionalCommentUpdateDisputeStateAction = optionalCommentUpdateDisputeStateAction;
        this.approveRecoverChargebackAction = approveRecoverChargebackAction;
        this.raiseChargebackRecoveryAccountingEventAction = raiseChargebackRecoveryAccountingEventAction;
        this.approveRecoveryReversalChargebackAction = approveRecoveryReversalChargebackAction;
        this.raiseChargebackRecoveryReversalAccountingEventAction = raiseChargebackRecoveryReversalAccountingEventAction;
        this.fraudCheckUpdateDisputeAction = fraudCheckUpdateDisputeAction;
        this.acceptDebitAndDebitDisputeAction = acceptDebitAndDebitDisputeAction;
    }

    @Override
    @SneakyThrows
    protected void configure(
        StateMachineConfigurationConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> config) {
        config
            .withConfiguration()
            .autoStartup(false);
    }

    @Override
    @SneakyThrows
    protected void configure(
        StateMachineStateConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> states) {
        states
            .withStates()
            .initial(DisputeWorkflowState.RECEIVED)
            .states(EnumSet.allOf(DisputeWorkflowState.class))
            .end(DisputeWorkflowState.CHARGEBACK_ABSORB_REVERSED)
            .end(DisputeWorkflowState.CHARGEBACK_CANCELLED)
            .end(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT_ACCEPTED);
    }

    @Override
    @SneakyThrows
    protected void configure(
        StateMachineTransitionConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> transitions) {
        transitions.
            withExternal()
            .source(DisputeWorkflowState.RECEIVED)
            .target(DisputeWorkflowState.RECEIVED)
            .event(DisputeWorkflowEvent.CREATE_ENTRY)
            .action(createEntryAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECEIVED)
            .target(DisputeWorkflowState.REFUND_BLOCKED)
            .event(DisputeWorkflowEvent.BLOCK_REFUND)
            .action(edcBlockRefundAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECEIVED)
            .target(DisputeWorkflowState.REPRESENTMENT_REQUIRED)
            .event(DisputeWorkflowEvent.REQUEST_REPRESENTMENT)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REPRESENTMENT_REQUIRED)
            .target(DisputeWorkflowState.REPRESENTMENT_COMPLETED)
            .event(DisputeWorkflowEvent.COMPLETE_REPRESENTMENT)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.FRAUD_REPRESENTMENT_COMPLETED)
            .target(DisputeWorkflowState.DEBIT_RECEIVED)
            .event(DisputeWorkflowEvent.RECEIVE_DEBIT)
            .action(acceptDebitAndDebitDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REPRESENTMENT_COMPLETED)
            .target(DisputeWorkflowState.CHARGEBACK_CANCELLED)
            .event(DisputeWorkflowEvent.RESET_CHARGEBACK)
            .action(resetChargebackAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REFUND_BLOCKED)
            .target(DisputeWorkflowState.FRAUD_REJECTED)
            .event(DisputeWorkflowEvent.FRAUD_REJECT)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.FRAUD_REJECTED)
            .target(DisputeWorkflowState.FRAUD_REPRESENTMENT_COMPLETED)
            .event(DisputeWorkflowEvent.FRAUD_REPRESENTMENT_COMPLETED)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.FRAUD_REPRESENTMENT_COMPLETED)
            .target(DisputeWorkflowState.REPRESENTMENT_COMPLETED)
            .event(DisputeWorkflowEvent.COMPLETE_REPRESENTMENT)
            .action(edcUnblockRefundAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REFUND_BLOCKED)
            .target(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
            .event(DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK)
            .action(fraudCheckUpdateDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
            .target(DisputeWorkflowState.SUSPECTED_FRAUD)
            .event(DisputeWorkflowEvent.SUSPECTED_FRAUD)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.SUSPECTED_FRAUD)
            .target(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
            .event(DisputeWorkflowEvent.SUSPECTED_FRAUD_TO_ACCEPTANCE)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.SUSPECTED_FRAUD)
            .target(DisputeWorkflowState.FRAUD_REJECTED)
            .event(DisputeWorkflowEvent.FRAUD_REJECT)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
            .target(DisputeWorkflowState.FRAUD_REJECTED)
            .event(DisputeWorkflowEvent.FRAUD_REJECT)
            .action(updateDisputeStateAction)


            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
            .target(DisputeWorkflowState.ACCEPTANCE_COMPLETED)
            .event(DisputeWorkflowEvent.COMPLETE_ACCEPTANCE)
            .action(acceptDisputeAction)


            .and()
            .withExternal()
            .source(DisputeWorkflowState.REFUND_BLOCKED)
            .target(DisputeWorkflowState.MERCHANT_NOT_RESPONDED_WITHIN_TTL)
            .event(DisputeWorkflowEvent.NO_RESPONSE_FROM_MERCHANT_WITHIN_TTL)
            .action(fraudCheckUpdateDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_NOT_RESPONDED_WITHIN_TTL)
            .target(DisputeWorkflowState.FRAUD_REJECTED)
            .event(DisputeWorkflowEvent.FRAUD_REJECT)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_NOT_RESPONDED_WITHIN_TTL)
            .target(DisputeWorkflowState.SUSPECTED_FRAUD)
            .event(DisputeWorkflowEvent.SUSPECTED_FRAUD)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_NOT_RESPONDED_WITHIN_TTL)
            .target(DisputeWorkflowState.ACCEPTANCE_COMPLETED)
            .event(DisputeWorkflowEvent.COMPLETE_ACCEPTANCE)
            .action(acceptDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_NOT_RESPONDED_WITHIN_TTL)
            .target(DisputeWorkflowState.REPRESENTMENT_COMPLETED)
            .event(DisputeWorkflowEvent.COMPLETE_REPRESENTMENT)
            .action(edcUnblockRefundAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_NOT_RESPONDED_WITHIN_TTL)
            .target(DisputeWorkflowState.PARTIAL_REPRESENTMENT_COMPLETED)
            .event(DisputeWorkflowEvent.COMPLETE_PARTIAL_REPRESENTMENT)
            .action(acceptPartialDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REFUND_BLOCKED)
            .target(DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED)
            .event(DisputeWorkflowEvent.RECEIVE_PARTIAL_FULFILMENT_DOCUMENTS)
            .action(fraudCheckUpdateDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED)
            .target(DisputeWorkflowState.SUSPECTED_FRAUD)
            .event(DisputeWorkflowEvent.SUSPECTED_FRAUD)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED)
            .target(DisputeWorkflowState.FRAUD_REJECTED)
            .event(DisputeWorkflowEvent.FRAUD_REJECT)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED)
            .target(DisputeWorkflowState.PARTIAL_REPRESENTMENT_COMPLETED)
            .event(DisputeWorkflowEvent.COMPLETE_PARTIAL_REPRESENTMENT)
            .action(acceptPartialDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REFUND_BLOCKED)
            .target(DisputeWorkflowState.FULFILMENT_DOCUMENTS_RECEIVED)
            .event(DisputeWorkflowEvent.RECEIVE_FULFILMENT_DOCUMENTS)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.FULFILMENT_DOCUMENTS_RECEIVED)
            .target(DisputeWorkflowState.REPRESENTMENT_COMPLETED)
            .event(DisputeWorkflowEvent.COMPLETE_REPRESENTMENT)
            .action(edcUnblockRefundAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.PARTIAL_REPRESENTMENT_COMPLETED)
            .target(DisputeWorkflowState.PARTIAL_DEBIT_RECEIVED)
            .event(DisputeWorkflowEvent.RECEIVE_PARTIAL_DEBIT)
            .action(debitDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.PARTIAL_REPRESENTMENT_COMPLETED)
            .target(DisputeWorkflowState.CHARGEBACK_CANCELLED)
            .event(DisputeWorkflowEvent.RESET_CHARGEBACK)
            .action(resetChargebackAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.ACCEPTANCE_COMPLETED)
            .target(DisputeWorkflowState.DEBIT_RECEIVED)
            .event(DisputeWorkflowEvent.RECEIVE_DEBIT)
            .action(debitDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.ACCEPTANCE_COMPLETED)
            .target(DisputeWorkflowState.CHARGEBACK_CANCELLED)
            .event(DisputeWorkflowEvent.RESET_CHARGEBACK)
            .action(resetChargebackAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.PARTIAL_DEBIT_RECEIVED)
            .target(DisputeWorkflowState.ABSORB_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_ABSORB_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.PARTIAL_DEBIT_RECEIVED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_RECOVER_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.DEBIT_RECEIVED)
            .target(DisputeWorkflowState.ABSORB_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_ABSORB_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.DEBIT_RECEIVED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_RECOVER_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.ABSORB_CHARGEBACK_REQUESTED)
            .target(DisputeWorkflowState.ABSORB_CHARGEBACK_APPROVED)
            .event(DisputeWorkflowEvent.APPROVE_ABSORB_CHARGEBACK)
            .action(optionalCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.ABSORB_CHARGEBACK_APPROVED)
            .target(DisputeWorkflowState.CHARGEBACK_ABSORBED)
            .event(DisputeWorkflowEvent.ABSORB_CHARGEBACK)
            .action(optionalCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.CHARGEBACK_ABSORBED)
            .target(DisputeWorkflowState.CHARGEBACK_ABSORB_REVERSED)
            .event(DisputeWorkflowEvent.REVERSE_ABSORB_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.ABSORB_CHARGEBACK_REQUESTED)
            .target(DisputeWorkflowState.ABSORB_CHARGEBACK_REJECTED)
            .event(DisputeWorkflowEvent.REJECT_ABSORB_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.ABSORB_CHARGEBACK_REJECTED)
            .target(DisputeWorkflowState.ABSORB_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_ABSORB_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECOVER_CHARGEBACK_REJECTED)
            .target(DisputeWorkflowState.ABSORB_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_ABSORB_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECOVER_CHARGEBACK_REJECTED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_RECOVER_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.ABSORB_CHARGEBACK_REJECTED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_RECOVER_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECOVER_CHARGEBACK_REQUESTED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_REJECTED)
            .event(DisputeWorkflowEvent.REJECT_RECOVER_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECOVER_CHARGEBACK_REQUESTED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_APPROVED)
            .event(DisputeWorkflowEvent.APPROVE_RECOVER_CHARGEBACK)
            .action(approveRecoverChargebackAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECOVER_CHARGEBACK_APPROVED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_RAISED)
            .event(DisputeWorkflowEvent.RAISE_RECOVER_CHARGEBACK_EVENT)
            .action(raiseChargebackRecoveryAccountingEventAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_RAISED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_ACCEPTED)
            .event(DisputeWorkflowEvent.ACCEPT_RECOVER_CHARGEBACK_EVENT)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_ACCEPTED)
            .target(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_REVERSAL_OF_RECOVERED_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_REQUESTED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_ACCEPTED)
            .event(DisputeWorkflowEvent.REJECT_REVERSAL_OF_RECOVERED_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_REQUESTED)
            .target(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_APPROVED)
            .event(DisputeWorkflowEvent.APPROVE_REVERSAL_OF_RECOVERED_CHARGEBACK)
            .action(approveRecoveryReversalChargebackAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_APPROVED)
            .target(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT_RAISED)
            .event(DisputeWorkflowEvent.RAISE_REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT)
            .action(raiseChargebackRecoveryReversalAccountingEventAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT_RAISED)
            .target(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT_ACCEPTED)
            .event(DisputeWorkflowEvent.ACCEPT_REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT)
            .action(updateDisputeStateAction);
    }

    @Override
    public  DisputeStateMachineRegistryKey getRegistryKey(){
        return DisputeStateMachineRegistryKey.builder()
            .disputeType(DisputeType.EDC_CHARGEBACK)
            .disputeStage(DisputeStage.FIRST_LEVEL)
            .disputeWorkflowVersion(DisputeWorkflowVersion.V1)
            .build();
    }
}
