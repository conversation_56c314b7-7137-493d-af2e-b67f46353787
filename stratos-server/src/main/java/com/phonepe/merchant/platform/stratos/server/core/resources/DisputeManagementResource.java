package com.phonepe.merchant.platform.stratos.server.core.resources;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.google.inject.Inject;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.requests.ContestPayload;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.requests.DisputeIdentifierParams;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.requests.DisputeFilterParams;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.responses.DisputeDetails;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.responses.DisputeSummaries;
import com.phonepe.merchant.platform.stratos.server.core.models.ApiState;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.ValidationUtils;
import com.phonepe.olympus.im.models.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import io.dropwizard.auth.Auth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import static com.phonepe.olympus.im.models.core.CoreConstants.BEARER;

@Slf4j
@Path("/v1")
@Tag(name = "Dispute Management APIs")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
@SecurityScheme(name = BEARER, type = SecuritySchemeType.APIKEY, in = SecuritySchemeIn.HEADER, paramName = "Authorization")
@SecurityScheme(name = Constants.END_USER_TOKEN, type = SecuritySchemeType.APIKEY, in = SecuritySchemeIn.HEADER, paramName = "x-enduser-token")
@SecurityRequirement(name = BEARER)
@SecurityRequirement(name = Constants.END_USER_TOKEN)
public class DisputeManagementResource {
    private final DisputeService disputeService;
    @POST
    @ExceptionMetered
    @RolesAllowed("disputes_inspect")
    @Path("/disputes/listAll")
    @Operation(summary = "Fetch summary of disputes for given merchant")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public DisputeSummaries listDisputes(
            @Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
            @HeaderParam("x-enduser-token") @NotNull @Parameter(required = true, hidden = true) String userAuthToken,
            @Valid @NotNull final DisputeFilterParams disputeFilterParams
            ) {
        ValidationUtils.validateMerchantId(userAuthToken,disputeFilterParams.getMerchantId());
        ValidationUtils.validateDisputeFilterParams(disputeFilterParams);
        return disputeService.getDisputes(disputeFilterParams);
    }

    @POST
    @ExceptionMetered
    @RolesAllowed("disputes_read")
    @Path("/disputes/getDetails")
    @Operation(summary = "Fetch details of dispute.")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public DisputeDetails getDisputeDetails(
            @Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
            @HeaderParam("x-enduser-token") @NotNull @Parameter(required = true, hidden = true) String userAuthToken,
            @Valid @NotNull final DisputeIdentifierParams disputeFilterParams) {
        ValidationUtils.validateMerchantId(userAuthToken,disputeFilterParams.getMerchantId());
        return disputeService.getDisputeDetails(disputeFilterParams.getDisputeId(), disputeFilterParams.getMerchantId());
    }

    @POST
    @ExceptionMetered
    @RolesAllowed("disputes_accept")
    @Path("/disputes/accept")
    @Operation(summary = "Accept the dispute.")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public DisputeDetails acceptDispute(
            @Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
            @HeaderParam("x-enduser-token") @NotNull @Parameter(required = true, hidden = true) String userAuthToken,
            @Valid @NotNull final DisputeIdentifierParams disputeParams) {
        ValidationUtils.validateMerchantId(userAuthToken,disputeParams.getMerchantId());
        return disputeService.acceptDispute(disputeParams.getDisputeId(), disputeParams.getMerchantId());
    }

    @POST
    @ExceptionMetered
    @RolesAllowed("disputes_contest")
    @Path("/disputes/contest")
    @Operation(summary = "Contest the dispute.")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public DisputeDetails contestDispute(
            @Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
            @HeaderParam("x-enduser-token") @NotNull @Parameter(required = true, hidden = true) String userAuthToken,
            @Valid @NotNull final ContestPayload payload) {
        ValidationUtils.validateMerchantId(userAuthToken, payload.getMerchantId());
        return disputeService.contest(payload);
    }

}
