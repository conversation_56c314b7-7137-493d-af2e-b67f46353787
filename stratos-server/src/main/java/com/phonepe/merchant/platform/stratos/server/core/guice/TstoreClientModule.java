package com.phonepe.merchant.platform.stratos.server.core.guice;

import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.phonepe.merchant.platform.stratos.server.StratosConfiguration;
import com.phonepe.merchant.platform.stratos.server.core.configs.TstoreClientConfig;
import com.phonepe.platform.scroll.model.v2.CreatedSchemaParams;
import com.phonepe.tstore.client.TstoreClient;
import com.phonepe.tstore.client.TstoreClientImpl;
import com.phonepe.tstore.client.bundle.TstoreClientBundle;
import java.util.HashMap;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import com.phonepe.tstore.client.TstoreClientImpl;

@RequiredArgsConstructor
public class TstoreClientModule extends AbstractModule {

    private final TstoreClientBundle<StratosConfiguration> tstoreClientBundle;

    @Provides
    public TstoreClient provideTstoreClient() {
//        return tstoreClientBundle.getTstoreClient();
        return new TstoreClientImpl(null,null,null,false,null,null,null,null,null);
    }

    @Provides
    public Map<Class<?>, CreatedSchemaParams> provideSchemaParamMapping() {
        return Map.of();
//        return new HashMap<>();
    }

    @Provides
    public TstoreClientConfig provideTstoreClientConfig(final StratosConfiguration config) {
        return config.getTstoreClientConfig();
    }
}
