package com.phonepe.merchant.platform.stratos.server.core.clients;


import static com.phonepe.merchant.platform.stratos.server.core.utils.TypeReferences.MIDDLEMAN_MERCHANT_DETAILS;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs.MerchantMandateClientConfig;
import com.phonepe.merchant.platform.stratos.server.core.utils.HttpClientUtils;
import com.phonepe.models.mandates.merchant.chargeback.MerchantChargebackData;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import com.phonepe.platform.http.v2.executor.factory.HttpExecutorBuilderFactory;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class MerchantMandateClient {

    private final HttpExecutorBuilderFactory httpExecutorBuilderFactory;
    private final OlympusIMClient olympusIMClient;

    @Inject
    public MerchantMandateClient(
        @MerchantMandateClientConfig final HttpConfiguration httpConfiguration,
        final ServiceEndpointProviderFactory serviceEndpointProviderFactory,
        final ObjectMapper mapper,
        final MetricRegistry metricRegistry, OlympusIMClient olympusIMClient) {
        this.olympusIMClient = olympusIMClient;
        this.httpExecutorBuilderFactory = HttpClientUtils.getHttpExecutorBuilderFactory(
            MerchantMandateClient.class, httpConfiguration,
            serviceEndpointProviderFactory, mapper, metricRegistry);
    }

    public GenericResponse<MerchantChargebackData> getMiddleManChargebackDetails(
        final String mandateId, final String userId, final String executionId) {
        final var url = String.format("/v1/chargeback/details/%s/%s/%s", userId, mandateId,
            executionId);

        return HttpClientUtils.executeGet(
            httpExecutorBuilderFactory, "getMiddleManChargebackDetails", url,
            MIDDLEMAN_MERCHANT_DETAILS, olympusIMClient);
    }
}
