package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.statemachines;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.TransitionLockCommand;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.registries.keys.DisputeStateMachineRegistryKey;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.comments.impls.MandatoryCommentUpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.comments.impls.OptionalCommentUpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.AcceptPartialDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.FraudCheckDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.ResetChargebackAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.UpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.interceptors.impls.DisputeErrorHandlingInterceptor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.ApproveRecoverChargebackAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.ApproveRecoveryReversalChargebackAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.BlockRefundAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.RaiseChargebackRecoveryAccountingEventAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.RaiseChargebackRecoveryReversalAccountingEventAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.UnblockRefundAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.statemachines.actions.FullRefundCreationAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.statemachines.actions.NetBankingChargebackCreateEntryAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.statemachines.actions.RefundCreationAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.statemachines.actions.RefundInitiationAction;

@Singleton
public class NetBankingChargebackFirstLevelStateMachine extends NetBankingChargebackStateMachine {

    @Inject
    @SuppressWarnings("java:S107")
    public NetBankingChargebackFirstLevelStateMachine(
        TransitionLockCommand transitionLockCommand,
        DisputeErrorHandlingInterceptor disputeErrorHandlingInterceptor,
        NetBankingChargebackCreateEntryAction netbankingChargebackCreateEntryAction,
        BlockRefundAction blockRefundAction,
        UpdateDisputeStateAction updateDisputeStateAction,
        UnblockRefundAction unblockRefundAction,
        MandatoryCommentUpdateDisputeStateAction mandatoryCommentUpdateDisputeStateAction,
        OptionalCommentUpdateDisputeStateAction optionalCommentUpdateDisputeStateAction,
        ApproveRecoverChargebackAction approveRecoverChargebackAction,
        RaiseChargebackRecoveryAccountingEventAction raiseChargebackRecoveryAccountingEventAction,
        ApproveRecoveryReversalChargebackAction approveRecoveryReversalChargebackAction,
        RaiseChargebackRecoveryReversalAccountingEventAction raiseChargebackRecoveryReversalAccountingEventAction,
        ResetChargebackAction resetChargebackAction,
        RefundCreationAction refundCreationAction,
        AcceptPartialDisputeAction acceptPartialDisputeAction,
        RefundInitiationAction refundInitiationAction,
        FullRefundCreationAction fullRefundCreationAction,
        UpdateDisputeStateAction updateDisputeStateActionForFraudCheck,
        FraudCheckDisputeAction fraudCheckUpdateDisputeAction) {

        super(transitionLockCommand, disputeErrorHandlingInterceptor,
            netbankingChargebackCreateEntryAction, blockRefundAction, updateDisputeStateAction,
            unblockRefundAction, mandatoryCommentUpdateDisputeStateAction,
            optionalCommentUpdateDisputeStateAction, approveRecoverChargebackAction,
            raiseChargebackRecoveryAccountingEventAction, approveRecoveryReversalChargebackAction,
            raiseChargebackRecoveryReversalAccountingEventAction, resetChargebackAction,
            acceptPartialDisputeAction, fullRefundCreationAction, refundInitiationAction,
            refundCreationAction, fraudCheckUpdateDisputeAction);
    }

    @Override
    public DisputeStateMachineRegistryKey getRegistryKey() {
        return DisputeStateMachineRegistryKey.builder()
            .disputeType(DisputeType.NB_CHARGEBACK)
            .disputeStage(DisputeStage.FIRST_LEVEL)
            .disputeWorkflowVersion(DisputeWorkflowVersion.V1)
            .build();
    }
}
