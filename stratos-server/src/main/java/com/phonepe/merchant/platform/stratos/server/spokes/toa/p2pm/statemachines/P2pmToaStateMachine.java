package com.phonepe.merchant.platform.stratos.server.spokes.toa.p2pm.statemachines;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.TransitionLockCommand;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.registries.keys.DisputeStateMachineRegistryKey;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.DisputeStateMachine;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.comments.impls.MandatoryCommentUpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.UpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.interceptors.impls.DisputeErrorHandlingInterceptor;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.p2pm.statemachines.actions.P2pmToaCompleteAction;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.p2pm.statemachines.actions.P2pmToaCreateEntryAction;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.p2pm.statemachines.actions.P2pmToaPayPostEntryAction;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.p2pm.statemachines.actions.P2pmToaPayRetryAction;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.p2pm.statemachines.actions.P2pmToaPayStatusCheckAction;
import java.util.EnumSet;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.config.builders.StateMachineConfigurationConfigurer;
import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;

@Slf4j
@Singleton
public class P2pmToaStateMachine extends DisputeStateMachine {


    private final P2pmToaCreateEntryAction p2pmToaCreateEntryAction;
    private final P2pmToaPayPostEntryAction p2pmToaPayPostEntryAction;
    private final UpdateDisputeStateAction updateDisputeStateAction;
    private final P2pmToaPayStatusCheckAction p2pmToaPayStatusCheckAction;
    private final MandatoryCommentUpdateDisputeStateAction mandatoryCommentUpdateDisputeStateAction;
    private final P2pmToaPayRetryAction p2pmToaPayRetryAction;
    private final P2pmToaCompleteAction p2pmToaCompleteAction;

    @Inject
    @SuppressWarnings("java:S107")
    public P2pmToaStateMachine(
        final TransitionLockCommand transitionLockCommand,
        final DisputeErrorHandlingInterceptor disputeErrorHandlingInterceptor,
        final P2pmToaCreateEntryAction p2pmToaCreateEntryAction,
        final P2pmToaPayPostEntryAction p2pmToaPayPostEntryAction,
        final UpdateDisputeStateAction updateDisputeStateAction,
        final P2pmToaPayStatusCheckAction p2pmToaPayStatusCheckAction,
        final MandatoryCommentUpdateDisputeStateAction mandatoryCommentUpdateDisputeStateAction,
        final P2pmToaPayRetryAction p2pmToaPayRetryAction,
        final P2pmToaCompleteAction p2pmToaCompleteAction
    ) {
        super(transitionLockCommand, disputeErrorHandlingInterceptor);
        this.p2pmToaCreateEntryAction = p2pmToaCreateEntryAction;
        this.p2pmToaPayPostEntryAction = p2pmToaPayPostEntryAction;
        this.updateDisputeStateAction = updateDisputeStateAction;
        this.p2pmToaPayStatusCheckAction = p2pmToaPayStatusCheckAction;
        this.mandatoryCommentUpdateDisputeStateAction = mandatoryCommentUpdateDisputeStateAction;
        this.p2pmToaPayRetryAction = p2pmToaPayRetryAction;
        this.p2pmToaCompleteAction = p2pmToaCompleteAction;
    }

    @Override
    @SneakyThrows
    protected void configure(
        final StateMachineConfigurationConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> config) {
        config
            .withConfiguration()
            .autoStartup(false);

    }

    @Override
    @SneakyThrows
    protected void configure(
        final StateMachineStateConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> states) {
        states
            .withStates()
            .initial(DisputeWorkflowState.RECEIVED)
            .states(EnumSet.allOf(DisputeWorkflowState.class))
            .end(DisputeWorkflowState.P2PM_TOA_COMPLETED)
            .end(DisputeWorkflowState.TOA_COMPLETED_EXTERNALLY);


    }

    @Override
    @SneakyThrows
    protected void configure(
        final StateMachineTransitionConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> transitions) {
        transitions
            .withExternal()
            .source(DisputeWorkflowState.RECEIVED)
            .target(DisputeWorkflowState.RECEIVED)
            .event(DisputeWorkflowEvent.CREATE_ENTRY)
            .action(p2pmToaCreateEntryAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECEIVED)
            .target(DisputeWorkflowState.TOA_BLOCKED_DUE_TO_KS)
            .event(DisputeWorkflowEvent.KS_ENABLED)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.TOA_BLOCKED_DUE_TO_KS)
            .target(DisputeWorkflowState.RECEIVED)
            .event(DisputeWorkflowEvent.TOA_BLOCKED_TO_RECEIVED)
            .action(p2pmToaPayPostEntryAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.TOA_BLOCKED_DUE_TO_KS)
            .target(DisputeWorkflowState.TOA_COMPLETED_EXTERNALLY)
            .event(DisputeWorkflowEvent.TOA_BLOCKED_TO_EXTERNAL_PROCESSED)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECEIVED)
            .target(DisputeWorkflowState.P2PM_TOA_INITIATED)
            .event(DisputeWorkflowEvent.TOA_INITIATED)
            .action(p2pmToaPayStatusCheckAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECEIVED)
            .target(DisputeWorkflowState.P2PM_TOA_INITIATION_FAILED)
            .event(DisputeWorkflowEvent.TOA_INITIATION_FAILED_AFTER_MAX_AUTO_RETRY)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.P2PM_TOA_INITIATION_FAILED)
            .target(DisputeWorkflowState.P2PM_TOA_INITIATED)
            .event(DisputeWorkflowEvent.RE_INITIATE_TOA) // MANUAL ACTION
            .action(p2pmToaPayStatusCheckAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.P2PM_TOA_INITIATED)
            .target(DisputeWorkflowState.P2PM_TOA_PENDING)
            .event(DisputeWorkflowEvent.INITIATED_TO_PENDING_AFTER_MAX_AUTO_RETRY)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.P2PM_TOA_INITIATED)
            .target(DisputeWorkflowState.P2PM_TOA_FAILED)
            .event(DisputeWorkflowEvent.INITIATED_TO_FAILED)
            .action(p2pmToaPayRetryAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.P2PM_TOA_INITIATED)
            .target(DisputeWorkflowState.P2PM_TOA_COMPLETED)
            .event(DisputeWorkflowEvent.INITIATED_TO_COMPLETED)
            .action(p2pmToaCompleteAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.P2PM_TOA_PENDING)
            .target(DisputeWorkflowState.P2PM_TOA_COMPLETED)
            .event(DisputeWorkflowEvent.PENDING_TO_COMPLETED)
            .action(p2pmToaCompleteAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.P2PM_TOA_PENDING)
            .target(DisputeWorkflowState.P2PM_TOA_FAILED)
            .event(DisputeWorkflowEvent.PENDING_TO_FAILED)
            .action(p2pmToaPayRetryAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.P2PM_TOA_FAILED)
            .target(DisputeWorkflowState.RECEIVED)
            .event(DisputeWorkflowEvent.PAY_FAILED_TO_RECEIVED)
            .action(p2pmToaPayPostEntryAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.P2PM_TOA_FAILED)
            .target(DisputeWorkflowState.P2PM_TOA_INITIATED)
            .event(DisputeWorkflowEvent.PAY_FAILED_TO_INITIATED)
            .action(p2pmToaPayStatusCheckAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.P2PM_TOA_FAILED)
            .target(DisputeWorkflowState.P2PM_TOA_FAILED_AFTER_MAX_AUTO_RETRY)
            .event(DisputeWorkflowEvent.PAY_FAILED_TO_MAX_RETRY)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.P2PM_TOA_INITIATION_FAILED)
            .target(DisputeWorkflowState.TOA_COMPLETED_EXTERNALLY)
            .event(DisputeWorkflowEvent.INITIATION_FAILED_TO_EXTERNAL_PROCESSED)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.P2PM_TOA_FAILED_AFTER_MAX_AUTO_RETRY)
            .target(DisputeWorkflowState.P2PM_TOA_INITIATED)
            .event(DisputeWorkflowEvent.RETRY_FAILED_TOA) // MANUAL ACTION
            .action(p2pmToaPayStatusCheckAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.P2PM_TOA_FAILED_AFTER_MAX_AUTO_RETRY)
            .target(DisputeWorkflowState.TOA_COMPLETED_EXTERNALLY)
            .event(DisputeWorkflowEvent.MAX_RETRY_TO_PROCESSED_EXTERNALLY) // MANUAL ACTION
            .action(mandatoryCommentUpdateDisputeStateAction);


    }

    @Override
    public DisputeStateMachineRegistryKey getRegistryKey() {
        return DisputeStateMachineRegistryKey.builder()
            .disputeType(DisputeType.P2PM_TOA)
            .disputeStage(DisputeStage.FIRST_LEVEL)
            .disputeWorkflowVersion(DisputeWorkflowVersion.V1)
            .build();
    }
}
