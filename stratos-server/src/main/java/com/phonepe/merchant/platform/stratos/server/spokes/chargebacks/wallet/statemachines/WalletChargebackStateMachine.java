package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.statemachines;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.TransitionLockCommand;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.registries.StateMachineListenerRegistry;
import com.phonepe.merchant.platform.stratos.server.core.registries.keys.DisputeStateMachineRegistryKey;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.DisputeStateMachine;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.RejectedDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.UpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.interceptors.impls.DisputeErrorHandlingInterceptor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.actions.UpdateAcceptedStateAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.actions.UpdateWalletMetadataOnExternalCompletionAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.actions.WalletMerchantAcceptedChargebackAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.actions.WalletRefundCreationAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.actions.WalletRefundInitiationAction;
import java.util.EnumSet;
import java.util.Set;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.config.builders.StateMachineConfigurationConfigurer;
import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;
import org.springframework.statemachine.config.configurers.StateConfigurer;

@Slf4j
@Singleton
public abstract class WalletChargebackStateMachine extends DisputeStateMachine {

    private final UpdateDisputeStateAction updateDisputeStateAction;
    private final WalletRefundInitiationAction refundInitiationAction;
    private final WalletRefundCreationAction refundCreationAction;
    private final WalletMerchantAcceptedChargebackAction walletMerchantAcceptedChargebackAction;
    private final RejectedDisputeAction rejectedDisputeAction;
    private final UpdateAcceptedStateAction updateAcceptedStateAction;
    private final UpdateWalletMetadataOnExternalCompletionAction updateWalletMetadataOnExternalCompletionAction;

    @Inject
    @SuppressWarnings("java:S107")
    protected WalletChargebackStateMachine(

        TransitionLockCommand transitionLockCommand,
        DisputeErrorHandlingInterceptor disputeErrorHandlingInterceptor,
        final UpdateDisputeStateAction updateDisputeStateAction,
        final WalletRefundInitiationAction refundInitiationAction,
        final WalletRefundCreationAction fullRefundCreationAction,
        final WalletMerchantAcceptedChargebackAction walletMerchantAcceptedChargebackAction,
        final RejectedDisputeAction rejectedDisputeAction,
        final UpdateAcceptedStateAction updateAcceptedStateAction,
        final UpdateWalletMetadataOnExternalCompletionAction updateWalletMetadataOnExternalCompletionAction,
        final StateMachineListenerRegistry<DisputeWorkflowState, DisputeWorkflowEvent> stateMachineListenerRegistry
    ) {
        super(transitionLockCommand, disputeErrorHandlingInterceptor,stateMachineListenerRegistry);
        this.updateDisputeStateAction = updateDisputeStateAction;
        this.refundInitiationAction = refundInitiationAction;
        this.refundCreationAction = fullRefundCreationAction;
        this.walletMerchantAcceptedChargebackAction = walletMerchantAcceptedChargebackAction;
        this.rejectedDisputeAction = rejectedDisputeAction;
        this.updateAcceptedStateAction = updateAcceptedStateAction;
        this.updateWalletMetadataOnExternalCompletionAction = updateWalletMetadataOnExternalCompletionAction;
    }

    protected abstract Set<DisputeWorkflowState> endStates();

    @Override
    public abstract DisputeStateMachineRegistryKey getRegistryKey();

    @Override
    @SneakyThrows
    protected void configure(
        final StateMachineConfigurationConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> config) {
        config
            .withConfiguration()
            .autoStartup(false);
    }

    @Override
    @SneakyThrows
    protected void configure(
        StateMachineStateConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> states) {
        StateConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> stateConfigurer = states
            .withStates()
            .initial(DisputeWorkflowState.RECEIVED)
            .states(EnumSet.allOf(DisputeWorkflowState.class))
            .end(DisputeWorkflowState.REJECTED_CHARGEBACK)
            .end(DisputeWorkflowState.ACCEPTED_CHARGEBACK);

        endStates().forEach((stateConfigurer::end));

    }


    @SneakyThrows
    protected StateMachineTransitionConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> baseTransitions(
        final StateMachineTransitionConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> transitions){

        return transitions.withExternal()
            .source(DisputeWorkflowState.RECEIVED)
            .target(DisputeWorkflowState.NPCI_ACK_CHARGEBACK)
            .event(DisputeWorkflowEvent.NPCI_ACK_CHARGEBACK)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.NPCI_ACK_CHARGEBACK)
            .target(DisputeWorkflowState.NPCI_REPRESENTMENT_COMPLETED)
            .event(DisputeWorkflowEvent.COMPLETE_NPCI_REPRESENTMENT)
            .action(rejectedDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.NPCI_REPRESENTMENT_COMPLETED)
            .target(DisputeWorkflowState.REJECTED_CHARGEBACK)
            .event(DisputeWorkflowEvent.REJECTED_CHARGEBACK)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.NPCI_ACK_CHARGEBACK)
            .target(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
            .event(DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK)
            .action(walletMerchantAcceptedChargebackAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
            .target(DisputeWorkflowState.PARTIAL_ACCEPTED_CHARGEBACK)
            .event(DisputeWorkflowEvent.PARTIAL_ACCEPT_CHARGEBACK)
            .action(refundCreationAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
            .target(DisputeWorkflowState.FULLY_ACCEPTED_CHARGEBACK)
            .event(DisputeWorkflowEvent.FULLY_ACCEPT_CHARGEBACK)
            .action(refundCreationAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.PARTIAL_ACCEPTED_CHARGEBACK)
            .target(DisputeWorkflowState.CB_REFUND_INITIATED)
            .event(DisputeWorkflowEvent.INITIATE_CHARGEBACK_REFUND)
            .action(refundInitiationAction) // make call to RO & state change based on inline success

            .and()
            .withExternal()
            .source(DisputeWorkflowState.FULLY_ACCEPTED_CHARGEBACK)
            .target(DisputeWorkflowState.CB_REFUND_INITIATED)
            .event(DisputeWorkflowEvent.INITIATE_CHARGEBACK_REFUND)
            .action(refundInitiationAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.CB_REFUND_INITIATED)
            .target(DisputeWorkflowState.CB_REFUND_INITIATED_COMPLETED)
            .event(DisputeWorkflowEvent.INITIATION_COMPLETED)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.CB_REFUND_INITIATED_COMPLETED)
            .target(DisputeWorkflowState.CB_REFUND_ACCEPTED)
            .event(DisputeWorkflowEvent.CHARGEBACK_REFUND_ACCEPTED)
            .action(updateAcceptedStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.CB_REFUND_ACCEPTED)
            .target(DisputeWorkflowState.ACCEPTED_CHARGEBACK)
            .event(DisputeWorkflowEvent.ACCEPTED_CHARGEBACK)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.CB_REFUND_INITIATED_COMPLETED)
            .target(DisputeWorkflowState.CB_REFUND_FAILED)
            .event(DisputeWorkflowEvent.INITIATED_TO_FAILED)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.CB_REFUND_FAILED)
            .target(DisputeWorkflowState.CB_REFUND_PROCESSED_EXTERNALLY)
            .event(DisputeWorkflowEvent.MAX_RETRY_TO_PROCESSED_EXTERNALLY)
            .action(updateWalletMetadataOnExternalCompletionAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.CB_REFUND_PROCESSED_EXTERNALLY)
            .target(DisputeWorkflowState.ACCEPTED_CHARGEBACK)
            .event(DisputeWorkflowEvent.ACCEPTED_CHARGEBACK)
            .action(updateDisputeStateAction)
            .and();
    }
}
