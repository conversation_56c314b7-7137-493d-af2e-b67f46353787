package com.phonepe.merchant.platform.stratos.server.core.clients;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.growth.zencast.channel.request.email.EmailMulticastRequest;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs.ZencastConfiguration;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.HttpClientUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.TypeReferences;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import com.phonepe.platform.http.v2.executor.factory.HttpExecutorBuilderFactory;
import com.phonepe.platform.http.v2.executor.httpdata.SerializableHttpData;
import io.appform.functionmetrics.MonitoredFunction;
import io.dropwizard.setup.Environment;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

@Slf4j
@Singleton
public class ZencastServiceClient {

    private final HttpExecutorBuilderFactory httpExecutorBuilderFactory;
    private final OlympusIMClient olympusIMClient;

    @Inject
    public ZencastServiceClient(
        @ZencastConfiguration final HttpConfiguration httpConfiguration,
        final ServiceEndpointProviderFactory serviceEndpointProviderFactory,
        final Environment environment,
        final ObjectMapper mapper,
        final MetricRegistry metricRegistry,
        final OlympusIMClient olympusIMClient) {
        this.httpExecutorBuilderFactory = HttpClientUtils.getHttpExecutorBuilderFactory(
            ZencastServiceClient.class, httpConfiguration,
            serviceEndpointProviderFactory, environment, mapper, metricRegistry);
        this.olympusIMClient = olympusIMClient;
    }

    @MonitoredFunction
    public void sendZencastEmail(final EmailMulticastRequest emailMulticastRequest) {

        final var url = "/v1/communication/send/email/multicast";
        val baseHttpData = new SerializableHttpData(Constants.APPLICATION_JSON, emailMulticastRequest);

        log.info("Sending email with request {}", emailMulticastRequest);
        final GenericResponse<JsonNode> genericResponse = HttpClientUtils.executePost(
            httpExecutorBuilderFactory, "sendEmail", url, baseHttpData,
            TypeReferences.ZENCAST_RESPONSE, olympusIMClient);

        HttpClientUtils.dataResolve(genericResponse);
    }
}
