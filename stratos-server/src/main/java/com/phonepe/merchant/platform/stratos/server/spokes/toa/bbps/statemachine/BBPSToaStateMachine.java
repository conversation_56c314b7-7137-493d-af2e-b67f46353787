package com.phonepe.merchant.platform.stratos.server.spokes.toa.bbps.statemachine;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.TransitionLockCommand;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.registries.keys.DisputeStateMachineRegistryKey;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.UpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.interceptors.impls.DisputeErrorHandlingInterceptor;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.bbps.actions.BBPSToaCreateEntryAction;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.common.BaseToaStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.common.actions.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;

@Slf4j
@Singleton
public class BBPSToaStateMachine extends BaseToaStateMachine {

    private final BBPSToaCreateEntryAction bbpsToaCreateEntryAction;
    private final UpdateToaMetadataOnExternalCompletionAction updateToaMetadataOnExternalCompletionAction;

    @Inject
    @SuppressWarnings("java:S107")
    protected BBPSToaStateMachine(final TransitionLockCommand transitionLockCommand,
                                  final DisputeErrorHandlingInterceptor disputeErrorHandlingInterceptor,
                                  final UpdateDisputeStateAction updateDisputeStateAction,
                                  final ToaCreateEntryAction toaCreateEntryAction,
                                  final ToaPayStatusCheckAction toaPayStatusCheckAction,
                                  final ToaCompletionAction toaCompletionAction,
                                  final ToaPayPostEntryAction toaPayPostEntryAction,
                                  final ToaRetryAction toaRetryAction,
                                  final ToaMandatoryCommentAction toaMandatoryCommentAction,
                                  final ToaStateUpdateAction toaStateUpdateAction,
                                  final BBPSToaCreateEntryAction bbpsToaCreateEntryAction,
                                  final UpdateToaMetadataOnExternalCompletionAction updateToaMetadataOnExternalCompletionAction) {
        super(transitionLockCommand, disputeErrorHandlingInterceptor, updateDisputeStateAction, toaCreateEntryAction,
                toaPayStatusCheckAction, toaCompletionAction, toaPayPostEntryAction, toaRetryAction,
                toaMandatoryCommentAction, toaStateUpdateAction);
        this.bbpsToaCreateEntryAction = bbpsToaCreateEntryAction;
        this.updateToaMetadataOnExternalCompletionAction = updateToaMetadataOnExternalCompletionAction;
    }

    @Override
    public DisputeStateMachineRegistryKey getRegistryKey() {
        return DisputeStateMachineRegistryKey.builder()
                .disputeType(DisputeType.BBPS_TAT_BREACH_TOA)
                .disputeStage(DisputeStage.FIRST_LEVEL)
                .disputeWorkflowVersion(DisputeWorkflowVersion.V1)
                .build();
    }

    @Override
    @SneakyThrows
    protected void extendStates(StateMachineTransitionConfigurer<DisputeWorkflowState,
                                DisputeWorkflowEvent> transitions) {
        transitions
                .withExternal()
                .source(DisputeWorkflowState.RECEIVED)
                .target(DisputeWorkflowState.RECEIVED)
                .event(DisputeWorkflowEvent.CREATE_ENTRY)
                .action(bbpsToaCreateEntryAction)

                .and()
                .withExternal()
                .source(DisputeWorkflowState.TOA_FAILED_AFTER_MAX_AUTO_RETRY)
                .target(DisputeWorkflowState.TOA_COMPLETED_EXTERNALLY)
                .event(DisputeWorkflowEvent.MAX_RETRY_TO_PROCESSED_EXTERNALLY) // MANUAL ACTION
                .action(updateToaMetadataOnExternalCompletionAction)

                .and()
                .withExternal()
                .source(DisputeWorkflowState.TOA_INITIATION_FAILED)
                .target(DisputeWorkflowState.TOA_COMPLETED_EXTERNALLY)
                .event(DisputeWorkflowEvent.INITIATION_FAILED_TO_EXTERNAL_PROCESSED) // MANUAL ACTION
                .action(updateToaMetadataOnExternalCompletionAction);
    }
}
