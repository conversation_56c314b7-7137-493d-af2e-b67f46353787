package com.phonepe.merchant.platform.stratos.server.core.guice;

import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.google.inject.TypeLiteral;
import com.phonepe.central.dms.server.mariadb.entities.TenantEntity;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.EscalationEntity;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.PenaltyClassDetailEntity;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.PenaltyClassEntity;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.PenaltyDisbursementEntity;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.PenaltyEntity;
import com.phonepe.merchant.platform.stratos.server.StratosConfiguration;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.verified.kaizen.configs.KaizenConfig;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.WorkflowRepository;
import io.appform.dropwizard.sharding.DBShardingBundle;
import io.appform.dropwizard.sharding.DBShardingBundleBase;
import io.appform.dropwizard.sharding.dao.RelationalDao;
import io.appform.dropwizard.sharding.utils.ShardCalculator;
import lombok.RequiredArgsConstructor;

import java.util.Objects;
import java.util.function.ToIntFunction;

@RequiredArgsConstructor
public class DBModule extends AbstractModule {

    private final DBShardingBundle<StratosConfiguration> dbShardingBundle;
    @Override
    protected void configure() {
        bind(new TypeLiteral<DBShardingBundleBase<? extends KaizenConfig>>() {}).toInstance(dbShardingBundle);
    }
    @Provides
    @Singleton
    public DBShardingBundle<StratosConfiguration> dbShardingBundle() {
        return dbShardingBundle;
    }

    @Provides
    @Singleton
    public RelationalDao<Dispute> relationalDao() {
        return dbShardingBundle.createRelatedObjectDao(Dispute.class);
    }

    @Singleton
    @Provides
    public ShardCalculator<String> shardCalculator(
        final RelationalDao<Dispute> disputeRelationalDao) {
        return disputeRelationalDao.getShardCalculator();
    }

    @Provides
    @Singleton
    public ToIntFunction<String> shardingFunction(WorkflowRepository workflowRepository) {
        Objects.requireNonNull(workflowRepository);
        return workflowRepository::getShardId;
    }


    @Provides
    @Singleton
    public RelationalDao<PenaltyClassEntity> penaltyClassEntityRelationalDao() {
        return dbShardingBundle.createRelatedObjectDao(PenaltyClassEntity.class);
    }

    @Provides
    @Singleton
    public RelationalDao<PenaltyClassDetailEntity> penaltyClassDetailEntityRelationalDao() {
        return dbShardingBundle.createRelatedObjectDao(PenaltyClassDetailEntity.class);
    }

    @Provides
    @Singleton
    public RelationalDao<EscalationEntity> escalationEntityRelationalDao() {
        return dbShardingBundle.createRelatedObjectDao(EscalationEntity.class);
    }

    @Provides
    @Singleton
    public RelationalDao<PenaltyEntity> penaltyEntityRelationalDao() {
        return dbShardingBundle.createRelatedObjectDao(PenaltyEntity.class);
    }

    @Provides
    @Singleton
    public RelationalDao<PenaltyDisbursementEntity> penaltyDisbursementEntityRelationalDao() {
        return dbShardingBundle.createRelatedObjectDao(PenaltyDisbursementEntity.class);
    }

    @Provides
    @Singleton
    public RelationalDao<TenantEntity> tenantEntityRelationalDao() {
        return dbShardingBundle.createRelatedObjectDao(TenantEntity.class);
    }

}
