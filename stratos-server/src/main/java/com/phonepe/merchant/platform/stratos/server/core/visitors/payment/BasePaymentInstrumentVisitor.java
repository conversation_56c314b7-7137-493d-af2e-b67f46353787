package com.phonepe.merchant.platform.stratos.server.core.visitors.payment;

import com.phonepe.models.payments.pay.instrument.AccountPaymentInstrument;
import com.phonepe.models.payments.pay.instrument.BnplPaymentInstrument;
import com.phonepe.models.payments.pay.instrument.CreditCardPaymentInstrument;
import com.phonepe.models.payments.pay.instrument.CreditLinePaymentInstrument;
import com.phonepe.models.payments.pay.instrument.DebitCardPaymentInstrument;
import com.phonepe.models.payments.pay.instrument.ExternalVpaPaymentInstrument;
import com.phonepe.models.payments.pay.instrument.ExternalWalletPaymentInstrument;
import com.phonepe.models.payments.pay.instrument.GiftCardPaymentInstrument;
import com.phonepe.models.payments.pay.instrument.NetBankingPaymentInstrument;
import com.phonepe.models.payments.pay.instrument.PaymentInstrumentVisitor;
import com.phonepe.models.payments.pay.instrument.WalletPaymentInstrument;

public abstract class BasePaymentInstrumentVisitor<T> implements PaymentInstrumentVisitor<T> {

    @Override
    public T visit(AccountPaymentInstrument accountPaymentInstrument) {
        return null;
    }

    @Override
    public T visit(CreditCardPaymentInstrument creditCardPaymentInstrument) {
        return null;
    }

    @Override
    public T visit(DebitCardPaymentInstrument debitCardPaymentInstrument) {
        return null;
    }

    @Override
    public T visit(NetBankingPaymentInstrument netBankingPaymentInstrument) {
        return null;
    }

    @Override
    public T visit(WalletPaymentInstrument walletPaymentInstrument) {
        return null;
    }

    @Override
    public T visit(GiftCardPaymentInstrument giftCardPaymentInstrument) {
        return null;
    }

    @Override
    public T visit(ExternalVpaPaymentInstrument externalVpaPaymentInstrument) {
        return null;
    }

    @Override
    public T visit(ExternalWalletPaymentInstrument externalWalletPaymentInstrument) {
        return null;
    }

    @Override
    public T visit(BnplPaymentInstrument bnplPaymentInstrument) {
        return null;
    }

    @Override
    public T visit(CreditLinePaymentInstrument creditLinePaymentInstrument) {
        return null;
    }

}
