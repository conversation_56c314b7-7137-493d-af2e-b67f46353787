package com.phonepe.merchant.platform.stratos.server.core.utils.exception;

import com.phonepe.error.configurator.model.service.ResourceErrorService;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;

@UtilityClass
@Slf4j
public class DisputeExceptionUtil {

    private  ResourceErrorService<StratosErrorCodeKey> resourceErrorService;

    public void init(final ResourceErrorService<StratosErrorCodeKey> resourceErrorServiceInput) {
        resourceErrorService = resourceErrorServiceInput;
    }

    public static String getMessage(StratosErrorCodeKey errorCodeKey, Map<String, Object> context) {
        return resourceErrorService.getValue(errorCodeKey,new ArrayList<>(context.values()));
    }

    public static DisputeException error(Throwable e, StratosErrorCodeKey errorCodeKey, Map<String, Object> context) {
        String errorMessage= resourceErrorService.getValue(errorCodeKey,new ArrayList<>(context.values()));
        return DisputeException.builder().e(e).error(errorMessage).errorCode(errorCodeKey).context(context).build();
    }

    public static DisputeException error(StratosErrorCodeKey errorCodeKey, Map<String, Object> context) {
        String errorMessage= resourceErrorService.getValue(errorCodeKey,new ArrayList<>(context.values()));
        return DisputeException.builder().error(errorMessage).errorCode(errorCodeKey).context(context).build();
    }

    public static DisputeException error(StratosErrorCodeKey errorCodeKey, String cause) {
        String errorMessage= resourceErrorService.getValue(errorCodeKey,new ArrayList<>(Map.of("cause",cause).values()));
        return DisputeException.builder().error(errorMessage).errorCode(errorCodeKey).context(Map.of("cause",cause)).build();
    }

    public static DisputeException error(StratosErrorCodeKey errorCodeKey) {
        String errorMessage= resourceErrorService.getValue(errorCodeKey,new ArrayList<>());
        return DisputeException.builder().error(errorMessage).context(new HashMap<>()).errorCode(errorCodeKey).build();
    }
    public static DisputeException error(Throwable e, StratosErrorCodeKey errorCodeKey) {
        String errorMessage= resourceErrorService.getValue(errorCodeKey,new ArrayList<>());

        return DisputeException.builder().e(e).error(errorMessage).context(Map.of("message",StringUtils.trimToEmpty(e.getMessage()))).errorCode(errorCodeKey).build();
    }

    public static DisputeException propagate(final Throwable e) {
        if (e instanceof DisputeException) {
            return (DisputeException) e;
        }
        return DisputeExceptionUtil.error(e,StratosErrorCodeKey.INTERNAL_SERVER_ERROR);
    }

    public static DisputeException propagate(final StratosErrorCodeKey errorCode, final Throwable e) {
        if (e instanceof DisputeException) {
            return (DisputeException) e;
        }
        return DisputeExceptionUtil.error(e,errorCode);
    }

    public static DisputeException propagate(final StratosErrorCodeKey errorCode, final Throwable e,
                                             final Map<String, Object> context) {
        if (e instanceof DisputeException) {
            return (DisputeException) e;
        }
        return DisputeExceptionUtil.error(e,errorCode, context);
    }
}
