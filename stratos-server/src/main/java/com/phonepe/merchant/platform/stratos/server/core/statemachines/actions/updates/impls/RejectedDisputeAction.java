package com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.queue.actors.CallbackActor;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.DisputeWorkflowMessage;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.UpdateDisputeStateBaseAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.statechange.StateChangeHandlerActor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Singleton
@Slf4j
public class RejectedDisputeAction extends UpdateDisputeStateBaseAction {

    private final Provider<StateChangeHandlerActor> stateChangeHandlerProvider;

    @Inject
    public RejectedDisputeAction(
        final DisputeService disputeService,
        final DisputeWorkflowRepository disputeWorkflowRepository,
        final EventIngester eventIngester,
        Provider<StateChangeHandlerActor> stateChangeHandlerProvider,
        CallbackActor callbackActor) {
        super(disputeService, disputeWorkflowRepository, eventIngester, callbackActor);
        this.stateChangeHandlerProvider = stateChangeHandlerProvider;
    }

    @Override
    @SneakyThrows
    protected void postTransition(final DisputeWorkflow disputeWorkflow) {
        final var disputeWorkflowMessage = DisputeWorkflowMessage.builder()
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
            .disputeWorkflowEvent(DisputeWorkflowEvent.REJECTED_CHARGEBACK)
            .build();

        log.info("Publishing dispute workflow message : {} to StateChangeHandlerActor for"
            + " REJECTED_CHARGEBACK", disputeWorkflowMessage);
        stateChangeHandlerProvider.get().publish(disputeWorkflowMessage);

    }
}
