package com.phonepe.merchant.platform.stratos.server.core.services.impls;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.clients.RefundOrchestratorClient;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.DisbursementDisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.RefundService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.FoxtrotEventUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.TransformationUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.models.DisputedRefund;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.model.DisputeRefundV2;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.services.refund.orchestrator.models.RefundStatus;
import com.phonepe.services.refund.orchestrator.models.v1.ROStatusRequest;
import com.phonepe.services.refund.orchestrator.models.v1.ROStatusRequestMerchant;
import com.phonepe.services.refund.orchestrator.models.v1.RefundRequest;
import com.phonepe.services.refund.orchestrator.models.v1.RefundResponse;
import com.phonepe.services.refund.orchestrator.models.v1.payer.RefundResponseV2;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Objects;
import org.eclipse.jetty.http.HttpStatus;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class RefundServiceImpl implements RefundService {

    private final RefundOrchestratorClient refundOrchestratorClient;

    private final DisputeService disputeService;
    private final EventIngester eventIngester;

    @Override
    @SneakyThrows
    public GenericResponse<RefundResponse> intiateRefund(final DisputedRefund disputedRefund) {

        log.info("Initiating refund for dispute {}, transactionId {}, refundId {}",
            disputedRefund.getDispute().getDisputeId(),
            disputedRefund.getDispute().getTransactionReferenceId(), disputedRefund.getRefundId());

        final RefundRequest refundRequest = TransformationUtils.getRefundRequestFromTransactionDetails(
            disputedRefund.getTransactionDetail(), disputedRefund.getDispute(),
            disputedRefund.getRefundId(), disputedRefund.getAmount());

        log.info("Refund request {} for workflowId {} ", refundRequest, disputedRefund.getDisputeWorkflow());

        return refundOrchestratorClient.initateRefund(refundRequest);
    }

    @Override
    @SneakyThrows
    public GenericResponse<RefundResponse> initiateRefund(final DisputeRefundV2 disputedRefund) {

        final RefundRequest refundRequest = TransformationUtils.toRefundRequestsForExternalMerchant(
            disputedRefund.getTransactionDetail(),
            disputedRefund.getRefundId(), disputedRefund.getAmount());

        log.info("Refund request for Initiating refund is {} for disputedRefund {} ", refundRequest,
            disputedRefund);

        return refundOrchestratorClient.initateRefund(refundRequest);
    }

    @Override
    public void refundStatusCallBack(RefundResponse refundResponse) {

        Objects.requireNonNull(refundResponse);

        DisbursementDisputeMetadata disbursementDisputeMetadata = validateAndGetDisputeMetadata(
            refundResponse);

        log.info("Dispute metadata for Refund callback response : {} is :{} ", refundResponse,
            disbursementDisputeMetadata);

        DisputeWorkflow disputeWorkflow = disputeService.validateAndGetDisputeWorkflow(
                disbursementDisputeMetadata.getTransactionReferenceId(),
                disbursementDisputeMetadata.getDisputeWorkflowId());

        RefundStatus status = refundResponse.getStatus();
        if (((Objects.requireNonNull(status) == RefundStatus.ACCEPTED) && !(disputeWorkflow.getDisputeType().equals(
            DisputeType.WALLET_CHARGEBACK)) ) || ((Objects.requireNonNull(status) == RefundStatus.COMPLETED)
            && (disputeWorkflow.getDisputeType().equals(DisputeType.WALLET_CHARGEBACK)))) {
            log.info("Triggering CHARGEBACK_REFUND_ACCEPTED for  disbursementDisputeMetadata :{}",disbursementDisputeMetadata );
            disputeService.triggerEvent(
                Constants.STRATOS_SYSTEM_USER_OLYMPUS,
                disputeWorkflow.getTransactionReferenceId(),
                disputeWorkflow.getDisputeWorkflowId(),
                DisputeWorkflowEvent.CHARGEBACK_REFUND_ACCEPTED,
                Constants.EMPTY_TRANSITION_CONTEXT);
        } else if (status == RefundStatus.FAILED) {
            log.info("Triggering INITIATED_TO_FAILED for  disbursementDisputeMetadata :{}",disbursementDisputeMetadata );
            disputeService.triggerEvent(
                Constants.STRATOS_SYSTEM_USER_OLYMPUS,
                disputeWorkflow.getTransactionReferenceId(),
                disputeWorkflow.getDisputeWorkflowId(),
                DisputeWorkflowEvent.INITIATED_TO_FAILED,
                Constants.EMPTY_TRANSITION_CONTEXT);
        }
        eventIngester.generateEvent(
            FoxtrotEventUtils.toRefundActionEvent(
                disputeWorkflow.getDisputeWorkflowId(),
                disputeWorkflow.getTransactionReferenceId(),
                refundResponse.getMerchantTxnId(),
                refundResponse.getStatus().name(), null, null));
    }

    @Override
    public RefundStatus getRefundStatus(ROStatusRequest request) {

        RefundResponse response = refundOrchestratorClient.getRefundStatus(request)
            .getData();

        return response.getStatus();

    }

    private DisbursementDisputeMetadata validateAndGetDisputeMetadata(RefundResponse refundResponse) {
        return this.disputeService.getDisbursementMetadata(refundResponse.getMerchantTxnId())
                .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_REFUND_ID,
                Map.of(Constants.MESSAGE, String.format("RefundId not present %s",
                        refundResponse.getMerchantTxnId()))));

    }
}
