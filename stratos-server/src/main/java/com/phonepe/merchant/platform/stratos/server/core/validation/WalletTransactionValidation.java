package com.phonepe.merchant.platform.stratos.server.core.validation;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.pipeline.core.executor.types.Validator;
import com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeCategory;
import com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeStage;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.TransactionDetails;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.WalletTransactionDetails;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.CreateDisputeRequest;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow.Fields;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.impls.WalletTransactionService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeWorkflowUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.DtoUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.models.common.enums.PaymentState;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class WalletTransactionValidation extends Validator<CreateDisputeRequest> {

    private final DisputeService disputeService;
    public static final Set<DisputeWorkflowState> TERMINAL_STATES = Set.of(
        DisputeWorkflowState.ACCEPTED_CHARGEBACK, DisputeWorkflowState.REJECTED_CHARGEBACK);
    private final WalletTransactionService walletTransactionService;

    @Inject
    public  WalletTransactionValidation(@NonNull String name, DisputeService disputeService,
        WalletTransactionService walletTransactionService) {
        super(name);
        this.disputeService = disputeService;
        this.walletTransactionService = walletTransactionService;
    }

    private void validateAmount(final TransactionDetails transactionDetail, final CreateDisputeRequest createDisputeRequest) {
        if (transactionDetail.getTransactionAmount() < createDisputeRequest.getDisputedAmount()) {
            log.info("Error is amount validation for transaction amount is {} and dispute amount is {} and createDisputeRequest {}",
                transactionDetail.getTransactionAmount(), createDisputeRequest.getDisputedAmount(), createDisputeRequest);
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_DISPUTE_AMOUNT,
                Map.of(Constants.MESSAGE,
                    "dispute amount is more than actual transaction amount for createDisputeRequest "+ createDisputeRequest));
        }
    }

    private void validatePreArb(CreateDisputeRequest createDisputeRequest) {
        Optional<DisputeWorkflow> disputeWorkflow = disputeService.getDisputeWorkflowOptional(
            createDisputeRequest.getTransactionId(),
            DtoUtils.transactionTypeToDisputeType(
                createDisputeRequest.getTransactionType(),
                createDisputeRequest.getDisputeData().getDisputeType()),
            DtoUtils.disputeDataStageToDisputeStage(DisputeStage.FIRST_LEVEL));



        if(disputeWorkflow.isEmpty()) {
            log.info("First level Dispute workflow is not present for createDisputeRequest {} ", createDisputeRequest);
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.FIRST_LEVEL_DISPUTE_NOT_FOUND,
                Map.of(Constants.MESSAGE, "First level dispute workflow not found for transaction Id is "
                    + createDisputeRequest.getTransactionId()));
        }

        if(!(TERMINAL_STATES.contains(disputeWorkflow.get().getCurrentState()))) {
            log.info("first level is not in terminal state for createDisputeRequest {} ",
                createDisputeRequest);
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.FIRST_LEVEL_NOT_IN_TERMINAL_STATE,
                Map.of(Constants.MESSAGE, "First Level not in terminal state for "
                    + "transaction Id is" + createDisputeRequest.getTransactionId())
            );
        }

        validatePreArbAmount(disputeWorkflow.get(), createDisputeRequest);
    }

    private void validatePreArbAmount(DisputeWorkflow disputeWorkflow, CreateDisputeRequest createDisputeRequest) {

        FinancialDisputeWorkflow financialDisputeWorkflow = DisputeWorkflowUtils.getFinancialDisputeWorkflow(
            disputeWorkflow);
        if ((financialDisputeWorkflow.getDisputedAmount() - financialDisputeWorkflow.getAcceptedAmount())
            < createDisputeRequest.getDisputedAmount()) {

            log.info("Error in validation of dispute amount disputed amount is {}, acceptedAmount is"
                    + " {} and first level disputed amount is {} for createDisputeRequest {}",
                createDisputeRequest.getDisputedAmount() , financialDisputeWorkflow.getAcceptedAmount()
            , financialDisputeWorkflow.getDisputedAmount(), createDisputeRequest);
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_DISPUTE_AMOUNT,
                Map.of(
                    Constants.MESSAGE, StratosErrorCodeKey.INVALID_DISPUTE_AMOUNT.getKey(),
                    "Raise Disputed Amount ", createDisputeRequest.getDisputedAmount(),
                    Fields.disputedAmount, financialDisputeWorkflow.getDisputedAmount(),
                    FinancialDisputeWorkflow.Fields.acceptedAmount,
                    financialDisputeWorkflow.getAcceptedAmount()));
        }
    }

    private void validateFraud(final CreateDisputeRequest createDisputeRequest){
        if(DisputeCategory.FRAUD.equals(createDisputeRequest.getDisputeData().getDisputeCategory())){
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.FRAUD_CHARGEBACK_NOT_ALLOWED,
                Map.of(Constants.MESSAGE,
                    "Invalid dispute category for  createDisputeRequest" + createDisputeRequest));
        }
    }

    @Override
    public void validate(final CreateDisputeRequest createDisputeRequest){
        WalletTransactionDetails transactionDetail = walletTransactionService.getTransactionDetails(
            createDisputeRequest.getTransactionId());

        if(!(PaymentState.COMPLETED.toString()).equals(transactionDetail.getPaymentState())){
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.PAYMENT_NOT_IN_COMPLETED_STATE, Map.of(Constants.MESSAGE,
                ("Transaction is not in complete state for create dispute request : "
                    + createDisputeRequest)));
        }

        validateFraud(createDisputeRequest);

        validateAmount(transactionDetail, createDisputeRequest);

        if (createDisputeRequest.getDisputeData().getDisputeStage()
            .equals(DisputeStage.PRE_ARBITRATION)) {
            validatePreArb(createDisputeRequest);
            }
    }
}
