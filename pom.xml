<?xml version="1.0" ?><project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.phonepe</groupId>
    <artifactId>pp-parent-pom</artifactId>
    <version>0.115</version>
  </parent>

  <groupId>com.phonepe.merchant.platform</groupId>
  <artifactId>stratos</artifactId>
  <packaging>pom</packaging>
    <version>2.0.67-SNAPSHOT</version>

  <modules>
    <module>stratos-server</module>
    <module>stratos-models</module>
  </modules>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

    <!-- Maven Plugins -->
    <lombok.maven.version>*********</lombok.maven.version>
    <maven.shade.version>3.2.4</maven.shade.version>
    <maven.compiler.version>3.8.1</maven.compiler.version>
    <maven.compiler.source>17</maven.compiler.source>
    <maven.compiler.target>17</maven.compiler.target>
    <maven.compiler.release>17</maven.compiler.release>
    <maven.javadoc.skip>true</maven.javadoc.skip>

    <!-- Dropwizard Bundles -->
    <dropwizard.version>2.0.23</dropwizard.version>
    <dropwizard.event.ingestion.version>2.1.0-12-SNAPSHOT</dropwizard.event.ingestion.version>
    <dropwizard.request.info.version>2.0.23-27</dropwizard.request.info.version>
    <dropwizard.oor.bundle.version>2.0.24-1</dropwizard.oor.bundle.version>
    <merchant.mandate.model.version>0.0.56</merchant.mandate.model.version>
    <dropwizard.metric.bundle.version>1.80</dropwizard.metric.bundle.version>
    <dropwizard.swagger.version>2.0.23-1</dropwizard.swagger.version>
    <dropwizard.db.sharding.bundle.version>2.0.28-9</dropwizard.db.sharding.bundle.version>
    <dropwizard.rabbitmq.version>2.0.23-4</dropwizard.rabbitmq.version>
    <dropwizard.aerospike.version>2.0.18-1</dropwizard.aerospike.version>
    <rosey.dropwizard.config>2.0.74</rosey.dropwizard.config>
    <tstore.bundle.version>5.3.28</tstore.bundle.version>
    <api.metrics.version>0.0.11</api.metrics.version>
    <validation.bundle.version>1.3.13.3</validation.bundle.version>
    <handlebars.version>4.2.0</handlebars.version>

    <!-- Hystrix Versions -->
    <hystrix.version>1.5.18</hystrix.version>
    <hystrix.function.wrapper.version>1.0.0</hystrix.function.wrapper.version>
    <hystrix.dropwizard.version>0.4</hystrix.dropwizard.version>

    <!-- Libraries -->
    <lombok.version>1.18.22</lombok.version>
    <guava.version>30.1.1-jre</guava.version>
    <okhttp3.version>4.11.0-PPE</okhttp3.version>
    <instrumented.okhttp.version>0.5.5-PP</instrumented.okhttp.version>
    <guice.version>5.4.0</guice.version>
    <zookeeper.version>3.4.6</zookeeper.version>
    <curator.version>4.2.0</curator.version>
    <wiremock.version>1.58</wiremock.version>
    <hamcrest.version>1.3</hamcrest.version>
    <data.provider.version>2.15</data.provider.version>
    <awaitility.version>3.1.6</awaitility.version>
    <aspectj.version>1.9.8</aspectj.version>
    <fuction.metrics.version>1.0.1</fuction.metrics.version>
    <jackson.version>2.12.1</jackson.version>
    <metrics.version>4.0.5</metrics.version>
    <spring.statemachine.version>2.4.0</spring.statemachine.version>
    <spring.version>5.3.3</spring.version>
    <mariadb.client.version>2.7.2</mariadb.client.version>
    <hibernate.version>5.4.30.Final</hibernate.version>
    <rabbitmq.client.version>5.17.0</rabbitmq.client.version>
    <aerospike.client.version>4.4.18</aerospike.client.version>
    <openfeign.version>11.2</openfeign.version>
    <apache.httpclient.version>4.5.13</apache.httpclient.version>
    <graphviz.version>0.18.1</graphviz.version>
    <http.feign.version>4.0.44</http.feign.version>
    <olympus.version>1.2.29</olympus.version>
    <commons.lang.version>3.12.0</commons.lang.version>
    <hystrix.configurator.version>0.0.8</hystrix.configurator.version>
    <warden.models.version>2.0.3</warden.models.version>
    <zencast-model.version>1.1.25</zencast-model.version>

    <!-- PhonePe Libraries -->
    <http.client.version>4.0.44</http.client.version>
    <phonepe.model.version>2.1.570-STAGE-SNAPSHOT</phonepe.model.version>
    <docstore-common.version>5.2.15</docstore-common.version>
    <docstore-client.version>2.5</docstore-client.version>
    <payment.model.version> 1.1.890-STAGE-SNAPSHOT</payment.model.version>
    <upi-client.model.version>0.2.110</upi-client.model.version>
    <pg.model.version>0.1.126</pg.model.version>
    <primus.version>1.35.1-SNAPSHOT</primus.version>
    <hibernate.validator.version>6.1.7.Final</hibernate.validator.version>
    <neuron.client.version>1.1.29</neuron.client.version>
    <killswitch.client.version>2.0.6</killswitch.client.version>
    <varys.version>1.0.6</varys.version>
    <api.killer.version>1.32</api.killer.version>
    <kratos.stratos.model.version>0.3-PROD</kratos.stratos.model.version>
    <kratos.client.version>0.12.8</kratos.client.version>
    <ro.model.version>1.2.0</ro.model.version>
    <pipeline.version>1.0.51</pipeline.version>
    <netpe.model.version>1.0.56</netpe.model.version>
    <zeus.model.version>2.0.73</zeus.model.version>
    <!-- Test Libraries -->
    <junit.jupiter.version>5.7.1</junit.jupiter.version>
    <mockito.version>4.3.1</mockito.version>
    <junit.testcontainers.version>1.0.6</junit.testcontainers.version>
    <docker.java.version>3.2.8</docker.java.version>
    <h2.version>1.4.200</h2.version>

    <!-- Sonar Settings -->
    <sonar.coverage.exclusions>
      **com/phonepe/merchant/platform/stratos/models/**/*,
      **com/phonepe/central/stratos/notification/**/*,
      **com/phonepe/central/stratos/penalty/beneficiary/**/*,
      **com/phonepe/central/stratos/penalty/disbursement/**/*,
      **com/phonepe/central/stratos/penalty/escalation/**/*,
      **com/phonepe/central/stratos/penalty/growth/**/*,
      **com/phonepe/central/stratos/penalty/meta/**/*,
      **com/phonepe/central/stratos/penalty/probables/**/*,
      **com/phonepe/central/stratos/penalty/recovery/**/*,
      **com/phonepe/central/stratos/penalty/request/**/*,
      **com/phonepe/central/stratos/penalty/response/**/*,
      **com/phonepe/central/stratos/penalty/response/PenaltyStatus,
      **com/phonepe/central/stratos/penalty/response/Penalty,
      **com/phonepe/central/stratos/penalty/server/resources/HouseKeepingResource*,
      **com/phonepe/central/stratos/penalty/server/config/**/*,
      **com/phonepe/central/stratos/penalty/server/util/**/*,
      **com/phonepe/central/stratos/penalty/server/mariadb/**/*,
      **com/phonepe/merchant/platform/stratos/server/core/mariadb/**/*,
      **com/phonepe/merchant/platform/stratos/server/core/utils/DisputeWorkflowUtils*,
      **com/phonepe/merchant/platform/stratos/server/core/utils/MapperUtils*,
      **com/phonepe/merchant/platform/stratos/server/BaseApplication*,
      **com/phonepe/merchant/platform/stratos/server/StratosApplication*,
      **com/phonepe/merchant/platform/stratos/server/StratosConfiguration*,
      **com/phonepe/merchant/platform/stratos/server/core/aerospike/keys/**/*,
      **com/phonepe/merchant/platform/stratos/server/core/exception/StratosError*,
      **com/phonepe/merchant/platform/stratos/server/core/configs/**/*,
      **com/phonepe/merchant/platform/stratos/server/core/guice/**/*,
      **com/phonepe/merchant/platform/stratos/server/core/queue/messages/**/*,
      **com/phonepe/merchant/platform/stratos/server/core/registries/keys/**/*,
      **com/phonepe/merchant/platform/stratos/server/core/exception/mappers/**/*,
      **com/phonepe/merchant/platform/stratos/server/core/models/**/*,
      **com/phonepe/merchant/platform/stratos/server/spokes/chargebacks/commons/visitors/PaymentContextMerchantTransactionIdVisitor.java,
      **com/phonepe/merchant/platform/stratos/server/core/visitors/payment/BasePaymentInstrumentVisitor*,
      **com/phonepe/merchant/platform/stratos/server/core/events/EventIngester*
    </sonar.coverage.exclusions>
    <sonar.exclusions>
      **src/main/resources/**/*,
      **com/phonepe/merchant/platform/stratos/models/disputes/toa/responses/ToaSummary*,
      **com/phonepe/merchant/platform/stratos/server/core/mariadb/entities/**/*
    </sonar.exclusions>
    <maven.deploy.skip>false</maven.deploy.skip>
    <maven.release.ignore.snapshots>true</maven.release.ignore.snapshots>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.phonepe.dataplatform</groupId>
        <artifactId>event-ingestion-client</artifactId>
        <version>${dropwizard.event.ingestion.version}</version>
        <exclusions>
          <exclusion>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.phonepe.models</groupId>
            <artifactId>phonepe-model</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.phonepe.platform.http.v2</groupId>
            <artifactId>http-client-all</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.phonepe.platform</groupId>
            <artifactId>requestinfo-bundle</artifactId>
          </exclusion>
          <exclusion>
            <groupId>io.appform.ranger</groupId>
            <artifactId>ranger-discovery-bundle</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.phonepe.platform</groupId>
        <artifactId>audit-logger-client</artifactId>
        <version>1.4</version>
      </dependency>
      <dependency>
        <artifactId>olympus-im-client</artifactId>
        <groupId>com.phonepe.olympus-im</groupId>
        <version>${olympus.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.glassfish.jersey.containers</groupId>
            <artifactId>jersey-container-servlet-core</artifactId>
          </exclusion>
          <exclusion>
            <groupId>io.raven.dropwizard</groupId>
            <artifactId>dropwizard-primer</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.phonepe.platform</groupId>
            <artifactId>zeus-models</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.phonepe.platform</groupId>
        <artifactId>requestinfo-bundle</artifactId>
        <version>2.0.23-36</version>
        <exclusions>
          <exclusion>
            <groupId>feign.ranger</groupId>
            <artifactId>feign-ranger</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.reflections</groupId>
        <artifactId>reflections</artifactId>
        <version>0.9.11</version>
      </dependency>
      <dependency>
        <groupId>org.javassist</groupId>
        <artifactId>javassist</artifactId>
        <version>3.28.0-GA</version>
      </dependency>
      <dependency>
        <groupId>com.phonepe.merchants.platform</groupId>
        <artifactId>varys</artifactId>
        <version>${varys.version}</version>
      </dependency>
      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>${lombok.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
        <version>${jackson.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-annotations</artifactId>
        <version>${jackson.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>${guava.version}</version>
      </dependency>
      <dependency>
        <groupId>com.h2database</groupId>
        <artifactId>h2</artifactId>
        <version>${h2.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-api</artifactId>
        <version>${junit.jupiter.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-engine</artifactId>
        <version>${junit.jupiter.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-params</artifactId>
        <version>${junit.jupiter.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>${mockito.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-inline</artifactId>
        <version>${mockito.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-junit-jupiter</artifactId>
        <version>${mockito.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.assertj</groupId>
        <artifactId>assertj-core</artifactId>
        <version>3.24.2</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.google.inject</groupId>
        <artifactId>guice</artifactId>
        <version>5.0.1</version>
      </dependency>
      <dependency>
        <groupId>ru.vyarus</groupId>
        <artifactId>dropwizard-guicey</artifactId>
        <version>${guice.version}</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard</groupId>
        <artifactId>dropwizard-core</artifactId>
        <version>${dropwizard.version}</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard</groupId>
        <artifactId>dropwizard-metrics</artifactId>
        <version>${dropwizard.version}</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard</groupId>
        <artifactId>dropwizard-jackson</artifactId>
        <version>${dropwizard.version}</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard</groupId>
        <artifactId>dropwizard-validation</artifactId>
        <version>${dropwizard.version}</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard</groupId>
        <artifactId>dropwizard-forms</artifactId>
        <version>${dropwizard.version}</version>
      </dependency>
      <dependency>
        <groupId>com.netflix.hystrix</groupId>
        <artifactId>hystrix-core</artifactId>
        <version>${hystrix.version}</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp</artifactId>
        <version>${okhttp3.version}</version>
      </dependency>
      <dependency>
        <groupId>io.appform.core</groupId>
        <artifactId>hystrix-function-wrapper</artifactId>
        <version>${hystrix.function.wrapper.version}</version>
      </dependency>
      <dependency>
        <groupId>com.phonepe.platform</groupId>
        <artifactId>sentinel-models</artifactId>
        <version>1.119</version>
        <exclusions>
          <exclusion>
            <groupId>io.appform.dropwizard.discovery</groupId>
            <artifactId>dropwizard-service-discovery-bundle</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.zookeeper</groupId>
        <artifactId>zookeeper</artifactId>
        <version>${zookeeper.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
          </exclusion>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
        </exclusions>
      </dependency>


      <dependency>
        <groupId>org.apache.curator</groupId>
        <artifactId>curator-framework</artifactId>
        <version>${curator.version}</version>
        <exclusions>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-core</artifactId>
        <version>${hibernate.version}</version>
      </dependency>
      <dependency>
        <groupId>com.aerospike</groupId>
        <artifactId>aerospike-client</artifactId>
        <version>${aerospike.client.version}</version>
      </dependency>
      <!-- Test -->
      <dependency>
        <groupId>io.dropwizard</groupId>
        <artifactId>dropwizard-testing</artifactId>
        <version>${dropwizard.version}</version>
        <scope>test</scope>
        <exclusions>
          <exclusion>
            <groupId>org.glassfish.jersey.test-framework.providers</groupId>
            <artifactId>*</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.github.tomakehurst</groupId>
        <artifactId>wiremock</artifactId>
        <version>${wiremock.version}</version>
        <scope>test</scope>
        <exclusions>
          <exclusion>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>io.dropwizard</groupId>
        <artifactId>metrics-core</artifactId>
        <version>${metrics.version}</version>
      </dependency>
      <dependency>
        <groupId>com.phonepe.merchants.platform</groupId>
        <artifactId>primus-core</artifactId>
        <version>${primus.version}</version>
        <exclusions>
          <exclusion>
            <groupId>io.raven.dropwizard</groupId>
            <artifactId>dropwizard-primer</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.phonepe.gandalf</groupId>
            <artifactId>gandalf-client</artifactId>
          </exclusion>
          <exclusion>
            <groupId>io.appform.dropwizard.discovery</groupId>
            <artifactId>dropwizard-service-discovery-bundle</artifactId>
          </exclusion>
          <exclusion>
            <groupId>io.appform.hope</groupId>
            <artifactId>hope-core</artifactId>
          </exclusion>
          <exclusion>
            <groupId>io.appform.hope</groupId>
            <artifactId>hope-lang</artifactId>
          </exclusion>
          <exclusion>
            <groupId>io.appform.hope</groupId>
            <artifactId>hope-core</artifactId>
          </exclusion>
          <exclusion>
            <groupId>io.appform.hope</groupId>
            <artifactId>hope-lang</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.phonepe.platform</groupId>
            <artifactId>dropwizard-requestinfo-bundle</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-httpclient</artifactId>
        <version>${openfeign.version}</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-core</artifactId>
        <version>${openfeign.version}</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-jackson</artifactId>
        <version>${openfeign.version}</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-okhttp</artifactId>
        <version>${openfeign.version}</version>
      </dependency>

      <dependency>
        <groupId>com.phonepe.payments</groupId>
        <artifactId>payment-model</artifactId>
        <version>${payment.model.version}</version>
        <exclusions>
          <exclusion>
            <groupId>com.phonepe.platform</groupId>
            <artifactId>dropwizard-requestinfo-bundle</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>com.phonepe.payments</groupId>
        <artifactId>pg-transport-model</artifactId>
        <version>${pg.model.version}</version>
      </dependency>

      <dependency>
        <groupId>com.phonepe.payments</groupId>
        <artifactId>upi-client-model</artifactId>
        <version>${upi-client.model.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>com.phonepe.platform.http.v2</groupId>
        <artifactId>http-client-all</artifactId>
        <version>${http.client.version}</version>
        <exclusions>
          <exclusion>
            <groupId>io.github.openfeign</groupId>
            <artifactId>*</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>com.raskasa.metrics</groupId>
        <artifactId>metrics-okhttp</artifactId>
        <version>${instrumented.okhttp.version}</version>
        <exclusions>
          <exclusion>
            <groupId>io.dropwizard.metrics</groupId>
            <artifactId>metrics-core</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient</artifactId>
        <version>${apache.httpclient.version}</version>
      </dependency>

      <dependency>
        <groupId>javax.validation</groupId>
        <artifactId>validation-api</artifactId>
        <version>2.0.1.Final</version>
      </dependency>

      <dependency>
        <groupId>org.hibernate.validator</groupId>
        <artifactId>hibernate-validator</artifactId>
        <version>${hibernate.validator.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <dependencies>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>${lombok.version}</version>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-params</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.phonepe.verified</groupId>
      <artifactId>kaizen-core</artifactId>
      <version>0.0.5</version>
      <exclusions>
        <exclusion>
          <groupId>com.phonepe.platform</groupId>
          <artifactId>zeus-models</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.phonepe.platform.http.v2</groupId>
          <artifactId>http-client-all</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.phonepe.platform.http.v2</groupId>
          <artifactId>http-feign</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.phonepe.platform</groupId>
          <artifactId>dropwizard-primer-bundle</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.appform.ranger</groupId>
          <artifactId>ranger-discovery-bundle</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.phonepe.platform.http.v2</groupId>
          <artifactId>http-client</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>${maven.compiler.version}</version>
        <configuration>
          <encoding>UTF-8</encoding>
          <source>${maven.compiler.source}</source>
          <target>${maven.compiler.target}</target>
          <release>${maven.compiler.release}</release>
          <forceJavacCompilerUse>true</forceJavacCompilerUse>
          <generatedSourcesDirectory>${project.build.directory}/generated-sources/
          </generatedSourcesDirectory>
        </configuration>
        <dependencies>
          <dependency>
            <groupId>org.ow2.asm</groupId>
            <artifactId>asm</artifactId>
            <version>7.2</version>
          </dependency>
        </dependencies>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-deploy-plugin</artifactId>
        <version>2.8.2</version>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>3.0.0-M5</version>
        <configuration>
          <argLine>${argLine}</argLine>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <version>0.8.7</version>
      </plugin>

    </plugins>
  </build>
</project>